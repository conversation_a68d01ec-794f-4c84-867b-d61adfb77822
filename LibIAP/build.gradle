plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdkVersion 33
    namespace = "com.example.libiap"

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.3.2'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.3.0'


    implementation "com.android.billingclient:billing-ktx:7.0.0"
    implementation 'com.google.code.gson:gson:2.10'

    def lifecycle_version_2 = "2.2.0"
    implementation "androidx.lifecycle:lifecycle-extensions:$lifecycle_version_2"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.4.0"
    annotationProcessor "androidx.lifecycle:lifecycle-compiler:$lifecycle_version_2"
}