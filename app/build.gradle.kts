plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.dagger.hilt.android)
    alias(libs.plugins.google.devtools.ksp)
    alias(libs.plugins.gms.google.services)
    alias(libs.plugins.google.firebase.crashlytics)
    id("kotlin-parcelize")
    id("kotlin-kapt")
    id("kotlin-android")

}

android {
    namespace = "co.ziplock"
    compileSdk = libs.versions.compileSdkVersion.get().toInt()
    defaultConfig {
        applicationId = "co.ziplock"
        minSdk = libs.versions.minSdkVersion.get().toInt()
        targetSdk = libs.versions.targetSdkVersion.get().toInt()
        versionCode = 5
        versionName = "dev_1.0.4"

        setProperty("archivesBaseName", "ZipLock_debug_${versionName}")


        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        javaCompileOptions {
            annotationProcessorOptions {
                argument("room.schemaLocation", "$projectDir/schemas")
                argument("room.incremental", "true")
                argument("room.expandProjection", "true")
            }
        }
    }

    buildTypes {
        debug {
            buildConfigField("String", "BASE_URL", "\"https://api.piontech.site/stores/\"")
            buildConfigField("String", "BASE_URL_OTP", "\"https://us-central1-ziplock-646de.cloudfunctions.net/\"")
            buildConfigField("String", "API_KEY", "\"45d2df1e-2aaf-4981-8491-94f1feb3528a\"")
        }
        release {
            buildConfigField("String", "BASE_URL", "\"https://api.piontech.site/stores/\"")
            buildConfigField("String", "BASE_URL_OTP", "\"https://us-central1-ziplock-646de.cloudfunctions.net/\"")
            buildConfigField("String", "API_KEY", "\"45d2df1e-2aaf-4981-8491-94f1feb3528a\"")
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            ndk {
                abiFilters += listOf("x86", "x86_64", "armeabi-v7a", "arm64-v8a")
            }
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    buildFeatures {
        viewBinding = true
        dataBinding = true
        buildConfig = true
    }

    bundle {
        language {
            enableSplit = false
        }
    }
}

dependencies {

    implementation(libs.kotlin.stdlib)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.appcompat.resources)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.legacy.support.v4)
    implementation(libs.navigation.fragment.ktx)
    implementation(libs.navigation.ui.ktx)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar"))))
    implementation(project(":commonRes"))

    implementation(project(":patternlockview"))

    implementation(project(":permissionx"))

    implementation(project(":photoview"))

    implementation(project(":LibAds"))

    implementation(project(":LibIAP"))

    // Crash recovery
    implementation(libs.lib.recovery)

    // Card View
    implementation(libs.androidx.cardview)

    // Recyclerview
    implementation(libs.androidx.recyclerview)

    // ViewModel & LiveData
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)

    // Hilt
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    implementation(libs.androidx.navigation.fragment.ktx)

    // Coroutines
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // Room
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // Glide
    api(libs.glide)
    annotationProcessor(libs.glide.compiler)

    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.crashlytics.ktx)
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.firebase.config.ktx)
    implementation(libs.firebase.messaging.ktx)

    // Viewpager2
    implementation(libs.androidx.viewpager2)

    // Nav component
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)

    // Retrofit2
    implementation(libs.retrofit)
    implementation(libs.converter.gson)

    //Okhttp3
    implementation(platform(libs.okhttp.bom))
    implementation(libs.okhttp3.okhttp)
    implementation(libs.okhttp3.logging.interceptor)

    // Material dialog
    implementation(libs.core)
    implementation(libs.lifecycle)
    implementation(libs.bottomsheets)

    // Auto dimen
    implementation(libs.autodimension)

    //Rounded Image View
    implementation(libs.roundedimageview)

    // Timber
    implementation(libs.timber)

    //Lottie
    implementation(libs.lottie)

    //Chucker
    debugImplementation(libs.chucker.library)
    releaseImplementation(libs.chucker.library.no.op)

    // work manager
    implementation(libs.androidx.work.runtime.ktx)

    implementation("androidx.hilt:hilt-work:1.0.0")
    
    // GridLayout
    implementation("androidx.gridlayout:gridlayout:1.0.0")
    
    // Shimmer Effect
    implementation("com.facebook.shimmer:shimmer:0.5.0")

    implementation("com.github.zladnrms:RoundableLayout:1.1.4")
}