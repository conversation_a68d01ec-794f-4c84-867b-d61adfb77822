package co.ziplock

import android.app.Application
import android.content.Context
import androidx.appcompat.app.AppCompatDelegate
import com.khaipv.recovery.core.Recovery
import dagger.hilt.android.HiltAndroidApp
import co.ziplock.framework.MainActivity
import co.ziplock.framework.presentation.common.lifecycleCallback.ActivityLifecycleCallbacksImpl
import co.ziplock.util.LocaleHelper
import timber.log.Timber

@HiltAndroidApp
class MyApplication : Application() {

    override fun attachBaseContext(base: Context?) {
        // Áp dụng ngôn ngữ đã chọn cho toàn bộ app
        val localizedContext = base?.let { LocaleHelper.createLocalizedContext(it) } ?: base
        super.attachBaseContext(localizedContext)
    }

    override fun onCreate() {
        super.onCreate()
        
        // Áp dụng ngôn ngữ cho Android 13+
        LocaleHelper.applyAppLanguage(this)
        
        //TODO: enable/disable dark theme
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)

        if (BuildConfig.DEBUG) {
            Recovery.getInstance()
                .debug(true)
                .recoverInBackground(false)
                .recoverStack(true)
                .mainPage(MainActivity::class.java)
                .recoverEnabled(true)
                .silent(false, Recovery.SilentMode.RECOVER_ACTIVITY_STACK)
                .init(this)

            Timber.plant(Timber.DebugTree())
        }
        registerActivityLifecycleCallbacks(ActivityLifecycleCallbacksImpl())
    }

}
