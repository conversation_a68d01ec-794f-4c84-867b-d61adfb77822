package co.ziplock.customview

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import androidx.viewpager2.widget.ViewPager2
import co.ziplock.R

class CustomDotsIndicator @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var dotHeight = 4f.dpToPx()
    private var dotWidth = 32f.dpToPx()
    private var dotSpacing = 8f.dpToPx()
    private var selectedDotWidthRatio = 1.5f
    private var selectedDotWidth = dotWidth * selectedDotWidthRatio
    private var cornerRadius = 10f.dpToPx()

    private var selectedDotColor = Color.parseColor("#3E9DF4")
    private var unselectedDotColor = Color.parseColor("#252836")

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    private var numberOfDots = 0
    private var selectedPosition = 0

    private val dotRect = RectF()

    init {
        attrs?.let {
            val typedArray = context.obtainStyledAttributes(it, R.styleable.CustomDotsIndicator)

            try {
                dotHeight = typedArray.getDimension(
                    R.styleable.CustomDotsIndicator_dotHeight,
                    dotHeight
                )

                dotWidth = typedArray.getDimension(
                    R.styleable.CustomDotsIndicator_dotWidth,
                    dotWidth
                )

                dotSpacing = typedArray.getDimension(
                    R.styleable.CustomDotsIndicator_dotSpacing,
                    dotSpacing
                )

                selectedDotWidthRatio = typedArray.getFloat(
                    R.styleable.CustomDotsIndicator_selectedDotWidthRatio,
                    selectedDotWidthRatio
                )

                cornerRadius = typedArray.getDimension(
                    R.styleable.CustomDotsIndicator_dotCornerRadius,
                    cornerRadius
                )

                selectedDotColor = typedArray.getColor(
                    R.styleable.CustomDotsIndicator_selectedDotColor,
                    selectedDotColor
                )

                unselectedDotColor = typedArray.getColor(
                    R.styleable.CustomDotsIndicator_unselectedDotColor,
                    unselectedDotColor
                )

                numberOfDots = typedArray.getInt(
                    R.styleable.CustomDotsIndicator_numbersOfDots,
                    numberOfDots
                )

                selectedDotWidth = dotWidth * selectedDotWidthRatio
            } finally {
                typedArray.recycle()
            }
        }
    }

    // Setter methods for programmatic customization
    fun setDotHeight(height: Float) {
        dotHeight = height.dpToPx()
        requestLayout()
        invalidate()
    }

    fun setDotWidth(width: Float) {
        dotWidth = width.dpToPx()
        selectedDotWidth = dotWidth * selectedDotWidthRatio
        requestLayout()
        invalidate()
    }

    fun setDotSpacing(spacing: Float) {
        dotSpacing = spacing.dpToPx()
        requestLayout()
        invalidate()
    }

    fun setSelectedDotWidthRatio(ratio: Float) {
        selectedDotWidthRatio = ratio
        selectedDotWidth = dotWidth * ratio
        requestLayout()
        invalidate()
    }

    fun setCornerRadius(radius: Float) {
        cornerRadius = radius.dpToPx()
        invalidate()
    }

    fun setDotColors(selectedColor: Int, unselectedColor: Int) {
        selectedDotColor = selectedColor
        unselectedDotColor = unselectedColor
        invalidate()
    }

    fun setNumberOfDots(count: Int) {
        numberOfDots = count
        requestLayout()
        invalidate()
    }

    fun setSelectedPosition(position: Int) {
        selectedPosition = position
        invalidate()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val desiredWidth = (numberOfDots - 1) * (dotWidth + dotSpacing) + selectedDotWidth
        val desiredHeight = dotHeight

        val width = resolveSize(desiredWidth.toInt(), widthMeasureSpec)
        val height = resolveSize(desiredHeight.toInt(), heightMeasureSpec)

        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        for (i in 0 until numberOfDots) {
            val isSelected = i == selectedPosition
            paint.color = if (isSelected) selectedDotColor else unselectedDotColor

            val dotWidth = if (isSelected) selectedDotWidth else dotWidth
            val left = if (i < selectedPosition) {
                i * (this.dotWidth + dotSpacing)
            } else if (i == selectedPosition) {
                i * (this.dotWidth + dotSpacing)
            } else {
                i * (this.dotWidth + dotSpacing) + (selectedDotWidth - this.dotWidth)
            }

            dotRect.set(
                left,
                0f,
                left + dotWidth,
                dotHeight
            )

            canvas.drawRoundRect(dotRect, cornerRadius, cornerRadius, paint)
        }
    }

    fun attachToViewPager2(viewPager: ViewPager2) {
        setNumberOfDots(viewPager.adapter?.itemCount ?: 0)

        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                setSelectedPosition(position)
            }
        })
    }

    private fun Float.dpToPx(): Float {
        return this * context.resources.displayMetrics.density
    }
}