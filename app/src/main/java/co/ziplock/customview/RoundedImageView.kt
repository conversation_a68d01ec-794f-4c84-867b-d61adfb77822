package co.ziplock.customview

import android.content.Context
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import co.ziplock.R

class RoundedImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val path = Path()
    private var cornerRadius = 0f
    private var roundRect = RectF()
    private val matrix = Matrix()

    init {
        // Lấy giá trị cornerRadius từ thuộc tính tùy chỉnh (nếu có)
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.RoundedImageView)
        cornerRadius = typedArray.getDimension(R.styleable.RoundedImageView_cornerRadius, 0f)
        typedArray.recycle()

        // B<PERSON>t chế độ drawing cache để cải thiện hiệu suất
        setLayerType(LAYER_TYPE_HARDWARE, null)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        roundRect.set(0f, 0f, width.toFloat(), height.toFloat())
    }

    override fun onDraw(canvas: Canvas) {
        val drawable = drawable ?: return
        if (drawable is BitmapDrawable) {
            val bitmap = drawable.bitmap
            val bitmapShader = BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)

            val scaleX = width.toFloat() / bitmap.width.toFloat()
            val scaleY = height.toFloat() / bitmap.height.toFloat()
            val scale = Math.max(scaleX, scaleY)
            matrix.setScale(scale, scale)

            val translateX = (width - bitmap.width * scale) / 2f
            val translateY = (height - bitmap.height * scale) / 2f
            matrix.postTranslate(translateX, translateY)

            bitmapShader.setLocalMatrix(matrix)
            paint.shader = bitmapShader

            path.addRoundRect(
                roundRect,
                cornerRadius,
                cornerRadius,
                Path.Direction.CW
            )
            canvas.drawPath(path, paint)
        } else {
            super.onDraw(canvas)
        }
    }

    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate() // Vẽ lại view khi cornerRadius thay đổi
    }
}