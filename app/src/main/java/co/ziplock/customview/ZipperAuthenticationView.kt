package co.ziplock.customview

import android.content.Context

import android.text.InputType
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.andrognito.patternlockview.PatternLockView
import com.andrognito.patternlockview.listener.PatternLockViewListener
import com.andrognito.patternlockview.utils.PatternLockUtils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import co.ziplock.R
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.edittext.AsteriskPasswordTransformationMethod
import co.ziplock.util.setTintColor

class ZipperAuthenticationView : FrameLayout {
    
    private var securityManager: SecurityManager? = null

    private var currentAuthView: View? = null
    
    // PIN input management
    private var pinInputs: List<EditText> = emptyList()
    private var currentPinIndex = 0
    private var isPinVisible = false
    private var currentPin = ""
    private var currentEnteredPin = ""
    private var isLockoutActive = false
    private var lockoutJob: Job? = null
    
    // Coroutines
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private var errorClearJob: Job? = null
    
    // Callbacks
    private var onAuthenticatedListener: (() -> Unit)? = null
    private var onForgotPasswordListener: (() -> Unit)? = null
    private var onCancelListener: (() -> Unit)? = null
    private var onAuthenticationResultListener: ((Boolean, SecurityManager.SecurityType) -> Unit)? = null
    private var onLockoutCompletedListener: (() -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        // Set background with semi-transparent overlay
        setBackgroundColor(ContextCompat.getColor(context, R.color.auth_overlay_background))
        visibility = View.GONE
    }
    
    fun setSecurityManager(securityManager: SecurityManager) {
        this.securityManager = securityManager
    }
    
    fun setOnAuthenticatedListener(listener: () -> Unit) {
        onAuthenticatedListener = listener
    }
    
    fun setOnForgotPasswordListener(listener: () -> Unit) {
        onForgotPasswordListener = listener
    }
    
    fun setOnCancelListener(listener: () -> Unit) {
        onCancelListener = listener
    }
    
    fun setOnAuthenticationResultListener(listener: (Boolean, SecurityManager.SecurityType) -> Unit) {
        onAuthenticationResultListener = listener
    }
    
    fun setOnLockoutCompletedListener(listener: () -> Unit) {
        onLockoutCompletedListener = listener
    }
    
    fun showAuthentication() {
        securityManager?.let { security ->
            if (!security.hasSecurity()) {
                // No security set, authenticate immediately
                onAuthenticatedListener?.invoke()
                return
            }
            
            // Clear any existing view
            hideAuthentication()
            
            // Create appropriate authentication view
            currentAuthView = when (security.getSecurityType()) {
                SecurityManager.SecurityType.PIN -> createPinAuthView()
                SecurityManager.SecurityType.PATTERN -> createPatternAuthView()
                else -> {
                    onAuthenticatedListener?.invoke()
                    return
                }
            }
            
            currentAuthView?.let { authView ->
                addView(authView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
                visibility = View.VISIBLE
            }
        }
    }
    
    fun hideAuthentication() {
        visibility = View.GONE
        removeAllViews()
        currentAuthView = null
        
        // Reset PIN state
        pinInputs = emptyList()
        currentPinIndex = 0
        isPinVisible = false
        currentPin = ""
        currentEnteredPin = ""
        isLockoutActive = false

        // Cancel error clear job
        errorClearJob?.cancel()
        errorClearJob = null
        
        // Cancel lockout job
        lockoutJob?.cancel()
        lockoutJob = null
    }
    
    private fun createPinAuthView(): View {
        val view = LayoutInflater.from(context).inflate(R.layout.overlay_pin_lock, null)
        
        val customKeypad = view.findViewById<View>(R.id.customKeypad)
        val tvErrorMessage = view.findViewById<TextView>(R.id.tvErrorMessage)
        val tvCountdown = view.findViewById<TextView>(R.id.tvCountdown)
        val btnForgotPassword = view.findViewById<Button>(R.id.btnForgotPassword)
        val btnTogglePinVisibility = view.findViewById<ImageView>(R.id.btnTogglePinVisibility)
        btnTogglePinVisibility.setTintColor(R.color.white)
        
        // Setup PIN input fields
        pinInputs = listOf(
            view.findViewById(R.id.etPin1),
            view.findViewById(R.id.etPin2),
            view.findViewById(R.id.etPin3),
            view.findViewById(R.id.etPin4)
        )

        // Initialize PIN inputs
        setupPinInputs()

        // Check if we're in lockout period
        securityManager?.let { security ->
            if (security.isLockedOut()) {
                isLockoutActive = true
                pinInputs.forEach { it.isEnabled = false }
                disableCustomKeypad(customKeypad)
                startLockoutCountdownWithSecurityManager()
            }
        }
        
        // Setup custom keypad buttons
        setupCustomKeypad(view)
        
        // Setup toggle pin visibility button
        btnTogglePinVisibility.setOnClickListener {
            if (!isLockoutActive) {
                isPinVisible = !isPinVisible
                togglePinVisibility()
            }
        }
        
        // Set up forgot password button
        btnForgotPassword.setOnClickListener {
            onForgotPasswordListener?.invoke()
        }
        
        return view
    }
    
    private fun setupCustomKeypad(view: View) {
        // Get all number buttons
        val btn0 = view.findViewById<Button>(R.id.btn_0)
        val btn1 = view.findViewById<Button>(R.id.btn_1)
        val btn2 = view.findViewById<Button>(R.id.btn_2)
        val btn3 = view.findViewById<Button>(R.id.btn_3)
        val btn4 = view.findViewById<Button>(R.id.btn_4)
        val btn5 = view.findViewById<Button>(R.id.btn_5)
        val btn6 = view.findViewById<Button>(R.id.btn_6)
        val btn7 = view.findViewById<Button>(R.id.btn_7)
        val btn8 = view.findViewById<Button>(R.id.btn_8)
        val btn9 = view.findViewById<Button>(R.id.btn_9)
        val btnDelete = view.findViewById<ImageView>(R.id.btn_delete)
        
        // Setup click listeners for number buttons
        val numberButtons = listOf(
            btn0 to "0", btn1 to "1", btn2 to "2", btn3 to "3", btn4 to "4",
            btn5 to "5", btn6 to "6", btn7 to "7", btn8 to "8", btn9 to "9"
        )
        
        numberButtons.forEach { (button, number) ->
            button.setOnClickListener {
                onNumberPressed(number)
            }
        }
        
        // Setup delete button
        btnDelete.setOnClickListener {
            onDeletePressed()
        }
    }
    
    private fun onNumberPressed(number: String) {
        // Skip if we're in lockout period
        if (isLockoutActive) return
        
        // Check if we can add more digits
        if (currentPin.length < 4) {
            currentPin += number
            updatePinDisplay(currentPin)
            
            // Check if PIN is complete (4 digits)
            if (currentPin.length == 4) {
                validatePin(currentPin)
            }
        }
    }
    
    private fun onDeletePressed() {
        // Skip if we're in lockout period
        if (isLockoutActive) return
        
        if (currentPin.isNotEmpty()) {
            currentPin = currentPin.dropLast(1)
            updatePinDisplay(currentPin)
        }
    }
    
    private fun createPatternAuthView(): View {
        val view = LayoutInflater.from(context).inflate(R.layout.overlay_pattern_lock, null)
        
        val patternLockView = view.findViewById<PatternLockView>(R.id.patternLockView)
        val tvErrorMessage = view.findViewById<TextView>(R.id.tvErrorMessage)
        val tvCountdown = view.findViewById<TextView>(R.id.tvCountdown)
        val btnForgotPassword = view.findViewById<Button>(R.id.btnForgotPassword)
        
        // Check if we're in lockout period
        securityManager?.let { security ->
            if (security.isLockedOut()) {
                isLockoutActive = true
                patternLockView.isEnabled = false
                startLockoutCountdownWithSecurityManager()
            }
        }
        
        patternLockView.addPatternLockListener(object : PatternLockViewListener {
            override fun onStarted() {
                if (!isLockoutActive) {
                    clearErrorState()
                }
            }
            
            override fun onProgress(progressPattern: MutableList<PatternLockView.Dot>?) {
                if (isLockoutActive) {
                    // If in lockout, clear the pattern immediately
                    patternLockView.clearPattern()
                    return
                }
                
                progressPattern?.let { dots ->
                    val patternIndices = dots.map { it.id }
                    // No need to do anything during progress
                }
            }
            
            override fun onComplete(pattern: MutableList<PatternLockView.Dot>?) {
                if (isLockoutActive) {
                    // If in lockout, clear the pattern immediately
                    patternLockView.clearPattern()
                    return
                }
                
                pattern?.let { dots ->
                    val patternIndices = dots.map { it.id }
                    verifyPattern(patternIndices)
                }
            }
            
            override fun onCleared() {
                // No action needed
            }
        })
        
        // Set up forgot password button
        btnForgotPassword.setOnClickListener {
            onForgotPasswordListener?.invoke()
        }

        return view
    }
    
    private fun setupPinInputs() {
        // Initialize PIN inputs with transformation method
        pinInputs.forEach { editText ->
            editText.transformationMethod = AsteriskPasswordTransformationMethod()
            editText.isEnabled = true
            editText.isFocusable = false  // Prevent manual focus/input since we use PinLockView
            editText.isFocusableInTouchMode = false
        }
        
        clearPinInputs()
    }
    
    private fun clearPinInputs() {
        pinInputs.forEach { 
            it.setText("")
            it.setTextColor(ContextCompat.getColor(context, R.color.white))
        }
        currentPin = ""
    }
    
    private fun updatePinDisplay(pin: String) {
        // Clear all inputs first
        pinInputs.forEach { it.setText("") }
        
        // Fill inputs with current pin
        for (i in pin.indices) {
            if (i < pinInputs.size) {
                val displayChar = pin[i].toString()
                pinInputs[i].setText(displayChar)
            }
        }

        // Apply visibility transformation
        pinInputs.forEach {
            it.transformationMethod = if (isPinVisible) null else AsteriskPasswordTransformationMethod()
        }
        
        // Update current PIN
        currentPin = pin
    }
    
    private fun validatePin(pin: String) {
        securityManager?.let { security ->
            val isSuccess = security.verifyPin(pin)
            
            if (isSuccess) {
                // PIN is correct - show success state
                setPinInputsSuccess()
                // Delay before calling authentication success to show visual feedback
                coroutineScope.launch {
                    delay(300)
                    onAuthenticatedListener?.invoke()
                    hideAuthentication()
                }
            }
            
            // Report result to parent (ZipperView) for attempt counting
            onAuthenticationResultListener?.invoke(isSuccess, SecurityManager.SecurityType.PIN)
        }
    }
    
    private fun verifyPattern(pattern: List<Int>) {
        if (pattern.size < 4) {
            val errorMessage = context.getString(R.string.connect_at_least_4_dots)
            showPatternError(errorMessage)
            resetPatternAfterDelay()
            // Don't report as failed attempt for too short pattern - just show message
            return
        }

        securityManager?.let { security ->
            val isSuccess = security.verifyPattern(pattern)
            
            if (isSuccess) {
                // Pattern is correct
                onAuthenticatedListener?.invoke()
                hideAuthentication()
            } else {
                // Pattern is incorrect - show error and reset after delay
                resetPatternAfterDelay()
            }
            
            // Report result to parent (ZipperView) for attempt counting
            onAuthenticationResultListener?.invoke(isSuccess, SecurityManager.SecurityType.PATTERN)
        }
    }
    
    private fun setPinInputsSuccess() {
        // Set PIN inputs to success color
        pinInputs.forEach { editText ->
            editText.setTextColor(ContextCompat.getColor(context, R.color.blue_2fafff))
        }
    }
    
    private fun showError(message: String) {
        currentAuthView?.let { view ->
            val tvErrorMessage = view.findViewById<TextView>(R.id.tvErrorMessage)
            tvErrorMessage.text = message
            tvErrorMessage.visibility = View.VISIBLE
            tvErrorMessage.setTextColor(ContextCompat.getColor(context, R.color.red_error))
            
            // Highlight incorrect inputs in PIN inputs
            pinInputs.forEach { editText ->
                editText.setTextColor(ContextCompat.getColor(context, R.color.red_error))
            }
            
            // Auto clear error after 2 seconds
            startErrorClearTimer()
        }
    }
    
    private fun startErrorClearTimer() {
        // Cancel any existing timer
        errorClearJob?.cancel()
        
        errorClearJob = coroutineScope.launch {
            delay(2000) // Wait 2 seconds
            clearErrorState()
            clearPinInputs()
        }
    }
    
    private fun clearErrorState() {
        currentAuthView?.let { view ->
            val tvErrorMessage = view.findViewById<TextView>(R.id.tvErrorMessage)
            tvErrorMessage.visibility = View.GONE
            
            // Reset PIN input colors
            pinInputs.forEach { editText ->
                editText.setTextColor(ContextCompat.getColor(context, R.color.white))
            }
        }
    }
    
    private fun showPatternError(message: String) {
        currentAuthView?.let { view ->
            val tvErrorMessage = view.findViewById<TextView>(R.id.tvErrorMessage)
            val patternLockView = view.findViewById<PatternLockView>(R.id.patternLockView)
            
            tvErrorMessage.text = message
            tvErrorMessage.visibility = View.VISIBLE
            tvErrorMessage.setTextColor(ContextCompat.getColor(context, R.color.red_error))
            
            // Set pattern to error state but don't clear it immediately
            patternLockView.setViewMode(PatternLockView.PatternViewMode.WRONG)
        }
    }
    
    private fun resetPatternAfterDelay() {
        errorClearJob?.cancel()
        errorClearJob = coroutineScope.launch {
            delay(2000)
            currentAuthView?.let { view ->
                val patternLockView = view.findViewById<PatternLockView>(R.id.patternLockView)
                patternLockView.clearPattern()
                clearErrorState()
            }
        }
    }
    
    private fun togglePinVisibility() {
        val iconRes = if (isPinVisible) R.drawable.ic_visibility else R.drawable.ic_visibility_off
        currentAuthView?.findViewById<ImageView>(R.id.btnTogglePinVisibility)?.apply {
            setImageResource(iconRes)
            setTintColor(R.color.white)
        }
        
        pinInputs.forEach { editText ->
            editText.transformationMethod = if (isPinVisible) {
                null
            } else {
                AsteriskPasswordTransformationMethod()
            }
        }
    }
    

    
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cleanup()
    }
    
    private fun disableCustomKeypad(keypadView: View) {
        // Disable all number buttons
        val buttonIds = listOf(
            R.id.btn_0, R.id.btn_1, R.id.btn_2, R.id.btn_3, R.id.btn_4,
            R.id.btn_5, R.id.btn_6, R.id.btn_7, R.id.btn_8, R.id.btn_9, R.id.btn_delete
        )
        
        buttonIds.forEach { id ->
            keypadView.findViewById<View>(id)?.isEnabled = false
        }
    }
    
    private fun enableCustomKeypad(keypadView: View) {
        // Enable all number buttons
        val buttonIds = listOf(
            R.id.btn_0, R.id.btn_1, R.id.btn_2, R.id.btn_3, R.id.btn_4,
            R.id.btn_5, R.id.btn_6, R.id.btn_7, R.id.btn_8, R.id.btn_9, R.id.btn_delete
        )
        
        buttonIds.forEach { id ->
            keypadView.findViewById<View>(id)?.isEnabled = true
        }
    }
    
    private fun cleanup() {
        // Cancel error clear job
        errorClearJob?.cancel()
        errorClearJob = null
        
        // Cancel lockout job
        lockoutJob?.cancel()
        lockoutJob = null
        
        // Reset states
        currentPin = ""
        currentEnteredPin = ""
        isPinVisible = false
        currentPinIndex = 0
        isLockoutActive = false
    }
    
    // Public methods for external control
    fun isAuthenticationVisible(): Boolean {
        return visibility == View.VISIBLE
    }
    
    fun handleBackPress(): Boolean {
        return if (isAuthenticationVisible()) {
            onCancelListener?.invoke()
            hideAuthentication()
            true
        } else {
            false
        }
    }
    
    fun getSecurityType(): SecurityManager.SecurityType? {
        return securityManager?.getSecurityType()
    }
    
    // Methods called by ZipperView for attempt management
    fun showAttemptsLeftError(attemptsLeft: Int, securityType: SecurityManager.SecurityType) {
        val errorMessage = when (securityType) {
            SecurityManager.SecurityType.PIN -> 
                context.getString(R.string.incorrect_pin_attempts_left, attemptsLeft)
            SecurityManager.SecurityType.PATTERN -> 
                context.getString(R.string.incorrect_pattern_attempts_left, attemptsLeft)
            else -> context.getString(R.string.incorrect_pin_attempts_left, attemptsLeft)
        }
        
        when (securityType) {
            SecurityManager.SecurityType.PIN -> showError(errorMessage)
            SecurityManager.SecurityType.PATTERN -> showPatternError(errorMessage)
            else -> showError(errorMessage)
        }
    }
    
    fun handleLockout() {
        startLockout()
    }
    
    private fun startLockout() {
        isLockoutActive = true
        
        // Disable PIN inputs and keypad for PIN authentication
        pinInputs.forEach { it.isEnabled = false }
        currentAuthView?.findViewById<View>(R.id.customKeypad)?.let { keypad ->
            disableCustomKeypad(keypad)
        }
        
        // Disable pattern view for Pattern authentication
        currentAuthView?.findViewById<PatternLockView>(R.id.patternLockView)?.let { patternView ->
            patternView.isEnabled = false
            patternView.setViewMode(PatternLockView.PatternViewMode.WRONG)
        }
        
        // Cancel any existing error clear job
        errorClearJob?.cancel()
        
        // Start lockout countdown
        startLockoutCountdownWithSecurityManager()
    }
    
    private fun startLockoutCountdownWithSecurityManager() {
        // Cancel any existing lockout job
        lockoutJob?.cancel()
        
        // Start a new countdown job
        lockoutJob = coroutineScope.launch {
            while (securityManager?.isLockedOut() == true) {
                val remainingSeconds = securityManager?.getRemainingLockoutTimeSeconds() ?: 0
                
                // Show lockout message with countdown
                currentAuthView?.let { view ->
                    val tvErrorMessage = view.findViewById<TextView>(R.id.tvErrorMessage)
                    val tvCountdown = view.findViewById<TextView>(R.id.tvCountdown)
                    
                    tvErrorMessage?.let {
                        it.text = context.getString(R.string.locked_out_countdown, remainingSeconds)
                        it.visibility = View.VISIBLE
                        it.setTextColor(ContextCompat.getColor(context, R.color.red_error))
                    }
                }
                
                delay(1000) // Update every second
            }
            
            // When lockout is over
            pinInputs.forEach { it.isEnabled = true }
            currentAuthView?.findViewById<View>(R.id.customKeypad)?.let { keypad ->
                enableCustomKeypad(keypad)
            }
            currentAuthView?.findViewById<PatternLockView>(R.id.patternLockView)?.let { patternView ->
                patternView.isEnabled = true
                patternView.clearPattern()
            }
            clearPinInputs()
            clearErrorState()
            isLockoutActive = false
            
            // Notify ZipperView that lockout is completed
            onLockoutCompletedListener?.invoke()
        }
    }
    

}