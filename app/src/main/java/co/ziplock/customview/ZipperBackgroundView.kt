package co.ziplock.customview

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import androidx.core.graphics.createBitmap
import co.ziplock.R

// Background Layer - Lớp dưới để hiển thị background
class ZipperBackgroundView : View {
    private var backgroundBitmap: Bitmap? = null

    private var mBackgroundBitmap: Bitmap? = null

    private var paint: Paint = Paint().apply {
        isAntiAlias = true
        isFilterBitmap = true
    }

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    private fun init() {
        backgroundBitmap = BitmapFactory.decodeResource(resources, R.drawable.default_background)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            //createDefaultBackground(w, h)
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        backgroundBitmap?.let { mBackgroundBitmap = centerCropBitmap(it, width, height) }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        mBackgroundBitmap?.let { bitmap ->
            canvas.drawBitmap(bitmap, 0f, 0f, paint)
        }
    }

    private fun createDefaultBackground(width: Int, height: Int) {
        backgroundBitmap?.recycle()
        backgroundBitmap = createBitmap(width, height)
        val backgroundCanvas = Canvas(backgroundBitmap!!)

        // Create a beautiful gradient background
        val gradientPaint = Paint().apply {
            isAntiAlias = true
            shader = LinearGradient(
                0f, 0f, 0f, height.toFloat(),
                intArrayOf(
                    Color.parseColor("#1a1a2e"), // Dark blue-purple at top
                    Color.parseColor("#16213e"), // Darker blue in middle
                    Color.parseColor("#0f3460")  // Even darker blue at bottom
                ),
                floatArrayOf(0f, 0.5f, 1f),
                Shader.TileMode.CLAMP
            )
        }

        backgroundCanvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), gradientPaint)
    }

    fun setBackgroundBitmap(bitmap: Bitmap?) {
        backgroundBitmap?.recycle()
        backgroundBitmap = bitmap
        requestLayout()
    }

    fun setBackgroundFromResource(resourceId: Int) {
        try {
            val bitmap = BitmapFactory.decodeResource(resources, resourceId)
            setBackgroundBitmap(bitmap)
        } catch (e: Exception) {
            e.printStackTrace()
            createDefaultBackground(width, height)
        }
    }

    override fun setBackgroundColor(color: Int) {
        if (width <= 0 || height <= 0) return

        backgroundBitmap?.recycle()
        backgroundBitmap = createBitmap(width, height)
        val backgroundCanvas = Canvas(backgroundBitmap!!)

        val colorPaint = Paint().apply {
            this.color = color
            isAntiAlias = true
        }

        backgroundCanvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), colorPaint)
        invalidate()
    }

    private fun centerCropBitmap(source: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        val sourceWidth = source.width
        val sourceHeight = source.height

        val scale: Float
        val dx: Float
        val dy: Float

        if (sourceWidth * targetHeight > targetWidth * sourceHeight) {
            scale = targetHeight.toFloat() / sourceHeight.toFloat()
            dx = (targetWidth - sourceWidth * scale) / 2f
            dy = 0f
        } else {
            scale = targetWidth.toFloat() / sourceWidth.toFloat()
            dx = 0f
            dy = (targetHeight - sourceHeight * scale) / 2f
        }

        val matrix = Matrix()
        matrix.setScale(scale, scale)
        matrix.postTranslate(dx, dy)

        val targetBitmap = createBitmap(targetWidth, targetHeight)
        val canvas = Canvas(targetBitmap)
        canvas.drawBitmap(source, matrix, Paint(Paint.FILTER_BITMAP_FLAG))

        return targetBitmap
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        backgroundBitmap?.recycle()
        backgroundBitmap = null
    }
}
