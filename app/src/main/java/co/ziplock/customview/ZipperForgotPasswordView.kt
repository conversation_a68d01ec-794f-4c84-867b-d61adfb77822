package co.ziplock.customview

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import co.ziplock.customview.forgot_password_overlay_screen.ZipperForgotPasswordManagerView
import co.ziplock.framework.presentation.manager.SecurityManager

class ZipperForgotPasswordView : FrameLayout {
    
    private lateinit var zipperForgotPasswordManagerView: ZipperForgotPasswordManagerView
    
    // Callbacks
    private var onPasswordResetSuccessListener: (() -> Unit)? = null
    private var onCancelListener: (() -> Unit)? = null
    private var onRequestOtpListener: ((String) -> Unit)? = null
    private var onSkipSetupListener: (() -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        zipperForgotPasswordManagerView = ZipperForgotPasswordManagerView(context)
        addView(zipperForgotPasswordManagerView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        
        // Setup callbacks
        zipperForgotPasswordManagerView.setOnPasswordResetSuccessListener {
            onPasswordResetSuccessListener?.invoke()
        }
        
        zipperForgotPasswordManagerView.setOnCancelListener {
            onCancelListener?.invoke()
        }
        
        zipperForgotPasswordManagerView.setOnRequestOtpListener { email ->
            onRequestOtpListener?.invoke(email)
        }
        
        zipperForgotPasswordManagerView.setOnSkipSetupListener {
            onSkipSetupListener?.invoke()
        }
    }
    
    fun setSecurityManager(securityManager: SecurityManager) {
        zipperForgotPasswordManagerView.setSecurityManager(securityManager)
    }
    
    fun setOnPasswordResetSuccessListener(listener: () -> Unit) {
        onPasswordResetSuccessListener = listener
    }
    
    fun setOnCancelListener(listener: () -> Unit) {
        onCancelListener = listener
    }
    
    fun setOnRequestOtpListener(listener: (String) -> Unit) {
        onRequestOtpListener = listener
    }
    
    fun setOnSkipSetupListener(listener: () -> Unit) {
        onSkipSetupListener = listener
    }
    
    fun setOnOTPValidationListener(listener: (String, () -> Unit, () -> Unit) -> Unit) {
        zipperForgotPasswordManagerView.setOnOTPValidationListener(listener)
    }
    
    fun showForgotPassword() {
        zipperForgotPasswordManagerView.showForgotPassword()
    }
    
    fun hideForgotPassword() {
        zipperForgotPasswordManagerView.hideForgotPassword()
    }
    
    // Additional methods for external control
    fun getCurrentStep(): ZipperForgotPasswordManagerView.ForgotPasswordStep {
        return zipperForgotPasswordManagerView.getCurrentStep()
    }
    
    fun canGoBack(): Boolean {
        return zipperForgotPasswordManagerView.canGoBack()
    }
    
    fun goBack() {
        zipperForgotPasswordManagerView.goBack()
    }
    
    fun handleOTPVerificationResult(isSuccess: Boolean, errorMessage: String? = null) {
        zipperForgotPasswordManagerView.handleOTPVerificationResult(isSuccess, errorMessage)
    }
    
    fun handleEmailRecoveryResult(isSuccess: Boolean, errorMessage: String? = null) {
        zipperForgotPasswordManagerView.handleEmailRecoveryResult(isSuccess, errorMessage)
    }
}