package co.ziplock.customview

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Typeface
import android.os.BatteryManager
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.FloatRange
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.createBitmap
import androidx.core.graphics.drawable.toBitmap
import androidx.core.graphics.scale
import androidx.core.graphics.withTranslation
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import co.ziplock.R
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.util.Constant
import co.ziplock.util.PrefUtil
import co.ziplock.util.renderToBitmap
import com.bumptech.glide.load.engine.DiskCacheStrategy
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.Timer
import java.util.TimerTask
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sin

// Top Layer - Lớp trên với zipper và mask
class ZipperTopLayerView(context: Context) : View(context) {
    private var mBitmapZipper: Bitmap? = null
    private var mBitmapWallpaper: Bitmap? = null
    private var maskBitmap: Bitmap? = null
    var wallpaperBitmap: Bitmap? = null

    var zipperBitmap: Bitmap? = null

    // Row data and bitmaps from URL
    private var currentRowResponse: RowResponse? = null
    private var imageRowLeftBitmap: Bitmap? = null
    private var imageRowRightBitmap: Bitmap? = null
    private var itemDepth: Float = 18f // Độ sâu xuống của mỗi item
    
    // Zipper data
    private var currentZipperResponse: ZipperResponse? = null
    private var drawZipperHeight = 60 // Chiều cao zipper theo dp

    private var mPaint: Paint? = null
    private var maskPaint: Paint? = null
    private var clearPaint: Paint? = null
    private var moveY = 0f
    private var yTouch = 0f

    private var isTouch = false
    private val INIT_HEIGHT_INT = 0f

    // Canvas tạm để vẽ mask
    private var tempCanvas: Canvas? = null
    private var tempBitmap: Bitmap? = null

    // Biến để lưu hệ số parabol
    private var parabolicCoefficient = 0.0008f

    // Hệ số cho hiệu ứng chữ S
    private var sCurveAmplitude = 100f
    private var sCurveFrequency = 0.75f

    // Animation settings
    private val unlockThreshold = 0.8f
    private val animationDuration = 300L
    private var currentProgress = 0f
    private var hasReachedThreshold = false

    // Information display
    private lateinit var infoContainer: LinearLayout
    private lateinit var timeTextView: TextView
    private lateinit var dateTextView: TextView
    private lateinit var batteryContainer: LinearLayout
    private lateinit var batteryIconView: ImageView
    private lateinit var batteryTextView: TextView
    private var informationRect: Rect = Rect()
    private var mInformationBitmap: Bitmap? = null

    // Battery receiver for real-time battery updates
    private var batteryReceiver: BroadcastReceiver? = null

    // Time update timer
    private var timeUpdateTimer: Timer? = null
    
    // Dynamic mask caching
    private var cachedDynamicMask: Bitmap? = null
    private var lastMaskBaseY: Float = -1f
    private var lastMaskUpdateTime: Long = 0L
    private val maskCacheInterval = 50L // 50ms cache interval

    // Quick action buttons
    //private var cameraButtonRect: RectF = RectF()
    private var flashlightButtonRect: RectF = RectF()
    //private var cameraIcon: Bitmap? = null
    private var flashlightIcon: Bitmap? = null
    private var flashlightOnIcon: Bitmap? = null
    private var isFlashlightOn = false
    private var showFlashlightIcon = true

    // Touch tracking for button taps
    //private var isTouchingCamera = false
    private var isTouchingFlashlight = false
    private var touchStartX = 0f
    private var touchStartY = 0f

    // Callbacks
    private var onUnlockListener: (() -> Unit)? = null
    private var onAuthenticationRequiredListener: (() -> Unit)? = null
    //private var onCameraRequestListener: (() -> Unit)? = null
    private var onFlashlightToggleListener: (() -> Unit)? = null
    private var onProgressResetListener: (() -> Unit)? = null

    // External vibration and sound callbacks
    private var externalDragVibration: (() -> Unit)? = null
    private var externalStrongVibration: (() -> Unit)? = null
    private var externalPlaySound: (() -> Unit)? = null
    private var externalPauseSound: (() -> Unit)? = null

    // Security
    var securityManager: SecurityManager? = null
    private var prefUtil: PrefUtil? = null
    
    // Preview mode settings
    private var isPreviewMode = false
    private var previewProgress = 0f

    //TODO: Configuration params
    //Param 1: y_left_row (or y_right_row) = y_right_row (or y_left_row) + percentToothSpacing * teethHeight
    @FloatRange(from = 0.0, to = 1.0)
    private var percentToothSpacing = 1f

    //Param 2: teeth expected height
    private var drawTeethHeight = 60

    //Param 3:
    // teeth RIGHT -> offset left from CENTER_HORIZONTAL screen by TOOTH PERCENT
    // teeth LEFT -> offset right from CENTER_HORIZONTAL screen by TOOTH PERCENT
    private var toothCenterOffsetPercent = 0.186f


    init {
        initWallpaperLayer()

        setLayerType(LAYER_TYPE_HARDWARE, null)
    }

    private fun initWallpaperLayer() {
        Timber.d("ZipperTopLayerView: Init")
        mPaint = Paint()
        mPaint!!.isAntiAlias = true
        mPaint!!.isFilterBitmap = true

        // Paint cho mask với PorterDuff
        maskPaint = Paint()
        maskPaint!!.isAntiAlias = true
        maskPaint!!.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)

        // Paint để xóa nền
        clearPaint = Paint()
        clearPaint!!.isAntiAlias = true
        clearPaint!!.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        // Load bitmaps
        zipperBitmap = BitmapFactory.decodeResource(resources, R.drawable.default_zipper)

        wallpaperBitmap = BitmapFactory.decodeResource(resources, R.drawable.default_wallpaper)

        imageRowLeftBitmap = BitmapFactory.decodeResource(resources, R.drawable.img_default_row_left)
            .scale(135, 60, false)
        imageRowRightBitmap = BitmapFactory.decodeResource(resources, R.drawable.img_default_row_right)
            .scale(135, 60, false)

        // Setup other components
        loadQuickActionIcons()
        initInformationViews()
        startTimeUpdates()
        setupBatteryReceiver()

        // Tạo mask bitmap đơn giản
        createDefaultMask()
    }

    fun setupExternalVibration(dragVibration: () -> Unit, strongVibration: () -> Unit) {
        externalDragVibration = dragVibration
        externalStrongVibration = strongVibration
    }

    fun setupExternalSound(playSound: () -> Unit, pauseSound: () -> Unit) {
        externalPlaySound = playSound
        externalPauseSound = pauseSound
    }


    fun setRowResponse(rowResponse: RowResponse?) {
        currentRowResponse = rowResponse
        
        // Cập nhật các tham số từ RowResponse
        rowResponse?.let { row ->
            percentToothSpacing = row.percentToothSpacing
            // Chuyển đổi draw_height từ dp sang px
            drawTeethHeight = dpToPx(row.drawHeight)
            toothCenterOffsetPercent = row.toothCenterOffset
        }
        
        loadRowImages()
        invalidateMaskCache()
        invalidate()
    }

    fun setItemDepth(depth: Float) {
        itemDepth = depth
        invalidate()
    }
    
    fun setZipperResponse(zipperResponse: ZipperResponse?) {
        currentZipperResponse = zipperResponse
        
        // Cập nhật các tham số từ ZipperResponse
        zipperResponse?.let { zipper ->
            // Chuyển đổi draw_height từ dp sang px
            drawZipperHeight = max(dpToPx(zipper.drawHeight) , dpToPx(60))
        }
        
        loadZipperImage()
        invalidateMaskCache()
        postInvalidate()
    }

    private fun loadRowImages() {
        currentRowResponse?.let { row ->
            // Load image_row_left
            if (!row.imageRowLeft.isNullOrEmpty()) {
                Glide.with(context)
                    .asBitmap()
                    .load(row.imageRowLeft)
                    .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                    .skipMemoryCache(false)
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                            imageRowLeftBitmap?.recycle()
                            imageRowLeftBitmap = null
                            val targetHeight = drawTeethHeight
                            val ratio = resource.width.toFloat() / resource.height
                            val targetWidth = (targetHeight * ratio).toInt()
                            val scaledBitmap = resource.scale(targetWidth, targetHeight).copy(Bitmap.Config.ARGB_8888, true)
                            imageRowLeftBitmap = scaledBitmap
                            invalidate()
                        }
                        override fun onLoadCleared(placeholder: android.graphics.drawable.Drawable?) {
                            // Optional: Handle cleanup
                        }
                    })
            }
            
            // Load image_row_right
            if (!row.imageRowRight.isNullOrEmpty()) {
                Glide.with(context)
                    .asBitmap()
                    .load(row.imageRowRight)
                    .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                    .skipMemoryCache(false)
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                            imageRowRightBitmap?.recycle()
                            imageRowRightBitmap = null

                            val targetHeight = drawTeethHeight
                            val ratio = resource.width.toFloat() / resource.height
                            val targetWidth = (targetHeight * ratio).toInt()
                            val scaledBitmap = resource.scale(targetWidth, targetHeight).copy(Bitmap.Config.ARGB_8888, true)

                            imageRowRightBitmap = scaledBitmap
                            invalidate()
                        }
                        override fun onLoadCleared(placeholder: android.graphics.drawable.Drawable?) {
                            // Optional: Handle cleanup
                        }
                    })
            }
        }
    }
    
    private fun loadZipperImage() {
        post {
            currentZipperResponse?.let { zipper ->
                // Load zipper image from fileUrl
                if (!zipper.fileUrl.isNullOrEmpty()) {
                    Glide.with(context)
                        .asBitmap()
                        .load(zipper.fileUrl)
                        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                        .skipMemoryCache(false)
                        .into(object : CustomTarget<Bitmap>() {
                            override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                                zipperBitmap?.recycle()
                                zipperBitmap = null

                                // Sử dụng drawZipperHeight đã được chuyển đổi từ dp sang px
                                val targetHeight = drawZipperHeight
                                val ratio = resource.width.toFloat() / resource.height
                                val targetWidth = (targetHeight * ratio).toInt()
                                val scaledBitmap = resource.scale(targetWidth, targetHeight).copy(Bitmap.Config.ARGB_8888, true)

                                zipperBitmap = scaledBitmap
                                //createDefaultMask()
                                //invalidateMaskCache()
                                postInvalidate()
                            }
                            override fun onLoadCleared(placeholder: android.graphics.drawable.Drawable?) {
                                // Optional: Handle cleanup
                            }
                        })
                }
            }
        }
    }



    fun setWallpaperShowingBitmap(bitmap: Bitmap?) {
        wallpaperBitmap?.recycle()
        wallpaperBitmap = bitmap
        requestLayout()
    }

    fun setPreviewMode(previewMode: Boolean, progress: Float = 0f) {
        isPreviewMode = previewMode
        previewProgress = progress
        if (previewMode) {
            // In preview mode, set fixed progress and disable touch
            currentProgress = progress
            isEnabled = false
        } else {
            // In normal mode, reset progress and enable touch
            currentProgress = 0f
            isEnabled = true
            
            // Notify that progress has been reset to 0
            onProgressResetListener?.invoke()
        }
        invalidateMaskCache()
        invalidate()
    }

    private fun loadQuickActionIcons() {
        try {
            val iconSize = dpToPx(24)

            /*ContextCompat.getDrawable(context, R.drawable.ic_camera)?.let { drawable ->
                cameraIcon = drawable.toBitmap(iconSize, iconSize)
            }*/

            ContextCompat.getDrawable(context, R.drawable.ic_flashlight)?.let { drawable ->
                flashlightIcon = drawable.toBitmap(iconSize, iconSize)
            }

            ContextCompat.getDrawable(context, R.drawable.ic_flashlight_on)?.let { drawable ->
                flashlightOnIcon = drawable.toBitmap(iconSize, iconSize)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initInformationViews() {
        infoContainer = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
        }

        timeTextView = TextView(context).apply {
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 30f)
            setTextColor(Color.WHITE)
            typeface = Typeface.DEFAULT_BOLD
            gravity = Gravity.CENTER
            setShadowLayer(4f, 2f, 2f, Color.BLACK)
        }

        dateTextView = TextView(context).apply {
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 20f)
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
            setShadowLayer(3f, 1f, 1f, Color.BLACK)
        }

        batteryContainer = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
        }

        batteryIconView = ImageView(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                dpToPx(24),
                dpToPx(24)
            ).apply {
                setMargins(0, 0, dpToPx(8), 0)
            }
        }

        batteryTextView = TextView(context).apply {
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 20f)
            setTextColor(Color.WHITE)
            typeface = Typeface.DEFAULT_BOLD
            setShadowLayer(3f, 1f, 1f, Color.BLACK)
        }
    }

    private fun startTimeUpdates() {
        timeUpdateTimer = Timer()
        timeUpdateTimer?.schedule(object : TimerTask() {
            override fun run() {
                post { updateTimeAndDate() }
            }
        }, 0, 1000)
    }

    private fun updateTimeAndDate() {
        val now = Calendar.getInstance()

        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
        timeTextView.text = timeFormat.format(now.time)

        val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
        dateTextView.text = dateFormat.format(now.time)

        invalidate()
    }

    private fun setupBatteryReceiver() {
        batteryReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                updateBatteryInfo()
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_BATTERY_CHANGED)
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)
        }

        context.registerReceiver(batteryReceiver, filter)
    }

    private fun updateBatteryInfo() {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        val batteryLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
        
        // Kiểm tra trạng thái sạc pin
        val batteryStatus = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS)
        val isCharging = batteryStatus == BatteryManager.BATTERY_STATUS_CHARGING
        val isFull = batteryStatus == BatteryManager.BATTERY_STATUS_FULL
        
        Timber.d("Battery: Level=$batteryLevel%, Status=$batteryStatus, Charging=$isCharging, Full=$isFull")

        // Hiển thị text với trạng thái sạc
        batteryTextView.text = "$batteryLevel%"

        // Chọn icon phù hợp với trạng thái sạc
        val iconRes = try {
            if (isCharging || isFull) {
                when {
                    batteryLevel <= 15 -> R.drawable.ic_battery_charging_0
                    batteryLevel <= 25 -> R.drawable.ic_battery_charging_25
                    batteryLevel <= 50 -> R.drawable.ic_battery_charging_50
                    batteryLevel <= 75 -> R.drawable.ic_battery_charging_75
                    else -> R.drawable.ic_battery_charging_100
                }
            } else {
                when {
                    batteryLevel <= 15 -> R.drawable.ic_battery_0
                    batteryLevel <= 25 -> R.drawable.ic_battery_25
                    batteryLevel <= 50 -> R.drawable.ic_battery_50
                    batteryLevel <= 75 -> R.drawable.ic_battery_75
                    else -> R.drawable.ic_battery_100
                }
            }
        } catch (e: Exception) {
            // Fallback to normal battery icons if charging icons are not available
            when {
                batteryLevel <= 15 -> R.drawable.ic_battery_0
                batteryLevel <= 25 -> R.drawable.ic_battery_25
                batteryLevel <= 50 -> R.drawable.ic_battery_50
                batteryLevel <= 75 -> R.drawable.ic_battery_75
                else -> R.drawable.ic_battery_100
            }
        }

        batteryIconView.setImageResource(iconRes)
        invalidate()
    }

    private fun createDefaultMask() : Bitmap? {
        if (width <= 0 || height <= 0) return null

        maskBitmap?.recycle()
        maskBitmap = createBitmap(width, height)
        val maskCanvas = Canvas(maskBitmap!!)

        val maskPaintWhite = Paint()
        maskPaintWhite.color = 0xFFFFFFFF.toInt()
        maskPaintWhite.isAntiAlias = true
        maskPaintWhite.strokeWidth = 1f // nhỏ nhưng không phải 0
        maskPaintWhite.style = Paint.Style.STROKE // nếu dùng drawLine thì dùng STROKE

        val centerX = width / 2f
        val spacing = 40f
        //val toothWidth = toothLeftBitmap?.width?.toFloat() ?: 40f

        for (y in 0 until height) {
            val distanceFromVertex = y.toFloat()
            val parabolicOffset = parabolicCoefficient * (distanceFromVertex * distanceFromVertex)

            val maxOffset = (width / 2f) - spacing - 20f
            val finalOffset = min(parabolicOffset, maxOffset)

            val teethLeftRight = centerX - finalOffset
            val teethRightLeft = centerX + finalOffset

            if (teethRightLeft > teethLeftRight) {
                maskCanvas.drawLine(
                    teethLeftRight,
                    y.toFloat(),
                    teethRightLeft,
                    y.toFloat(),
                    maskPaintWhite
                )
            }
        }

        return maskBitmap
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        //wallpaperBitmap?.let { mBitmapWallpaper = centerCropBitmap(it, width, height) }

        if (w > 0 && h > 0) {
            tempBitmap?.recycle()
            tempBitmap = createBitmap(w, h)
            tempCanvas = Canvas(tempBitmap!!)

            createDefaultMask()
            setupQuickActionButtons()
            invalidateMaskCache()
        }
    }

    private fun setupQuickActionButtons() {
        val buttonSize = dpToPx(56)
        val margin = dpToPx(24)

        /*cameraButtonRect.set(
            margin.toFloat(),
            (height - buttonSize - margin).toFloat(),
            (margin + buttonSize).toFloat(),
            (height - margin).toFloat()
        )*/

        flashlightButtonRect.set(
            (width - buttonSize - margin).toFloat(),
            (height - buttonSize - margin).toFloat(),
            (width - margin).toFloat(),
            (height - margin).toFloat()
        )
    }

    private fun dpToPx(dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            context.resources.displayMetrics
        ).toInt()
    }

    fun centerCropBitmap(source: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        val sourceWidth = source.width
        val sourceHeight = source.height

        val scale: Float
        val dx: Float
        val dy: Float

        if (sourceWidth * targetHeight > targetWidth * sourceHeight) {
            scale = targetHeight.toFloat() / sourceHeight.toFloat()
            dx = (targetWidth - sourceWidth * scale) / 2f
            dy = 0f
        } else {
            scale = targetWidth.toFloat() / sourceWidth.toFloat()
            dx = 0f
            dy = (targetHeight - sourceHeight * scale) / 2f
        }

        val matrix = Matrix()
        matrix.setScale(scale, scale)
        matrix.postTranslate(dx, dy)

        val targetBitmap = createBitmap(targetWidth, targetHeight)
        val canvas = Canvas(targetBitmap)
        canvas.drawBitmap(source, matrix, Paint(Paint.FILTER_BITMAP_FLAG))

        return targetBitmap
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        wallpaperBitmap?.let { mBitmapWallpaper = centerCropBitmap(it, width, height) }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (tempBitmap == null || tempCanvas == null) return

        mInformationBitmap = createInformationBitmapViews()
        val leftInfoRect = dpToPx(10)
        val infoTop = (height * 0.3f).toInt()

        mInformationBitmap?.let { informationBitmap ->
            informationRect.set(leftInfoRect, infoTop, informationBitmap.width + leftInfoRect, infoTop + informationBitmap.height)
        }

        tempCanvas!!.drawColor(0, PorterDuff.Mode.CLEAR)

        val dy = if (currentProgress > 0) {
            height * currentProgress
        } else if (isTouch && (yTouch - moveY) > 0) {
            yTouch - moveY
        } else {
            INIT_HEIGHT_INT
        }

        // Điều chỉnh dy cho preview mode để phù hợp với scale 46%
        val adjustedDy = dy

        if (height > 0) min(dy / height.toFloat(), 1f) else 0f

        // Vẽ wallpaper đầy đủ lên trên (không scale trong preview mode)
        mBitmapWallpaper?.let {
            tempCanvas!!.drawBitmap(it, 0f, 0f, mPaint)
        }

        // Cắt phần wallpaper để lộ background bên dưới - sử dụng adjustedDy để phù hợp với scale
        applyMaskToWallpaper(adjustedDy)

        // Vẽ răng cưa dựa trên loại Row
        when (currentRowResponse?.type) {
            Constant.TYPE_ROW_SPLIT -> {
                drawSplitZipperTeeth(tempCanvas!!, adjustedDy)
            }
            else -> {
                drawInterlockZipperTeeth(tempCanvas!!, adjustedDy)
            }
        }

        // Draw lock head với scale trong preview mode - sử dụng adjustedDy
        drawZipperLockHead(tempCanvas!!, adjustedDy)

        // Vẽ kết quả cuối cùng lên canvas chính
        canvas.drawBitmap(tempBitmap!!, 0f, 0f, mPaint)

        // Vẽ thông tin lên canvas với translation và scale trong preview mode
        drawInformationWithTranslation(canvas, currentProgress)

        // Vẽ các nút quick action
        drawQuickActionButtons(canvas)
    }

    private fun applyMaskToWallpaper(baseY: Float) {
        if (tempCanvas == null) return

        val dynamicMask = getCachedDynamicMask(baseY)
        tempCanvas!!.drawBitmap(dynamicMask, 0f, 0f, maskPaint)
    }

    private fun invalidateMaskCache() {
        cachedDynamicMask?.recycle()
        cachedDynamicMask = null
        lastMaskBaseY = -1f
        lastMaskUpdateTime = 0L
    }

    private fun getCachedDynamicMask(baseY: Float): Bitmap {
        val currentTime = System.currentTimeMillis()
        val shouldUpdateCache = cachedDynamicMask == null || 
                               abs(baseY - lastMaskBaseY) > 1f || // Update if baseY changed significantly
                               (currentTime - lastMaskUpdateTime) >= maskCacheInterval
        
        if (shouldUpdateCache) {
            // Recycle old cached mask
            cachedDynamicMask?.recycle()
            
            // Create new mask
            cachedDynamicMask = createDynamicMask(baseY)
            lastMaskBaseY = baseY
            lastMaskUpdateTime = currentTime
        }
        
        return cachedDynamicMask!!
    }

    private fun createDynamicMask(baseY: Float): Bitmap {
        val mask = createBitmap(width, height)
        val maskCanvas = Canvas(mask)

        val maskPaintWhite = Paint()
        maskPaintWhite.color = 0xFFFFFFFF.toInt()
        maskPaintWhite.isAntiAlias = true
        maskPaintWhite.strokeWidth = 5f // nhỏ nhưng không phải 0
        maskPaintWhite.style = Paint.Style.STROKE // nếu dùng drawLine thì dùng STROKE

        val centerX = width / 2f
        
        // Sử dụng spacing nhỏ hơn để tạo đường cong mượt mà hơn
        val spacing = 5f
        var y = 0f

        while (y <= baseY) {
            val drawY = baseY - y
            val distanceFromTopToZipperHead = baseY
            val zipperProgress = if (height > 0) {
                min(distanceFromTopToZipperHead / height.toFloat(), 1f)
            } else 0f

            val distanceFromCurrentToZipperHead = abs(drawY - baseY)
            val toothProgress = if (baseY > 0) {
                max(0f, (distanceFromCurrentToZipperHead / baseY))
            } else 0f

            val combinedProgress = zipperProgress * toothProgress
            val maxSeparation = width / 0.8f
            val currentSeparation = if(drawY < baseY) {
                combinedProgress * maxSeparation
            } else {
                0f
            }

            val baseLeftX = centerX - currentSeparation
            val baseRightX = centerX + currentSeparation

            val parabolaFactor = 0.3f
            val parabolaOffset = parabolaFactor * currentSeparation * currentSeparation / maxSeparation

            val sCurveOffsetLeft = calculateSCurveOffset(drawY, baseY, currentSeparation, true)
            val sCurveOffsetRight = calculateSCurveOffset(drawY, baseY, currentSeparation, false)

            val minLeftX = -30f
            val maxRightX = width + 30f

            var leftTeethRight = baseLeftX - parabolaOffset + sCurveOffsetLeft
            var rightTeethLeft = baseRightX + parabolaOffset + sCurveOffsetRight

            leftTeethRight = max(leftTeethRight, minLeftX)
            rightTeethLeft = min(rightTeethLeft, maxRightX)

            val isFullyUnlocked = zipperProgress >= 1.0f

            if (isFullyUnlocked) {
                // Vẽ toàn bộ chiều rộng khi đã mở hoàn toàn
                maskCanvas.drawRoundRect(
                    minLeftX,
                    drawY,
                    maxRightX,
                    drawY + spacing,
                    5f,
                    5f,
                    maskPaintWhite
                )
            } else {
                if (rightTeethLeft > leftTeethRight) {
                    // Vẽ từng dải nhỏ để tạo đường cong mượt mà
                    maskCanvas.drawRoundRect(
                        leftTeethRight,
                        drawY,
                        rightTeethLeft,
                        drawY + spacing,
                        5f,
                        5f,
                        maskPaintWhite
                    )
                }
            }

            y += spacing
        }

        return mask
    }

    private fun calculateSCurveOffset(y: Float, maxY: Float, separation: Float, isLeft: Boolean): Float {
        if (separation <= 0f || maxY <= 0f) return 0f
        if (y >= maxY) return 0f

        val normalizedY = y / maxY
        val phase = if (isLeft) 0f else PI.toFloat()
        val sineValue = sin(normalizedY * sCurveFrequency * PI + phase).toFloat()

        val separationRatio = min(separation / (width / 4f), 1f)
        val distanceFromHead = abs(y - maxY) / maxY
        var effectiveAmplitude = sCurveAmplitude * separationRatio * (1f - distanceFromHead * 0.5f)
        
        // Giảm độ SCurve khi ở Preview Mode
        if (isPreviewMode) {
            effectiveAmplitude *= 0.35f // Giảm xuống 35% so với bình thường
        }

        return sineValue * effectiveAmplitude
    }

    private fun drawSplitZipperTeeth(canvas: Canvas, dy: Float) {
        if (imageRowLeftBitmap == null || imageRowRightBitmap == null) return

        // Calculate scale factors for preview mode
        val scaleX = if (isPreviewMode) {
            val parentView = parent as? ZipperView
            val previewWidth = parentView?.getPreviewItemWidth() ?: (width * 0.466f)
            previewWidth / width
        } else 1f
        
        val scaleY = if (isPreviewMode) {
            val parentView = parent as? ZipperView
            val previewHeight = parentView?.getPreviewItemHeight() ?: (height * 0.508f)
            previewHeight / height
        } else 1f

        val centerX = width / 2f
        val toothWidth = imageRowLeftBitmap!!.width.toFloat() * scaleX
        val toothHeight = imageRowLeftBitmap!!.height.toFloat() * scaleY
        val spacing = toothHeight
        var toothIndex = 0
        var y = 0f

        val zipperProgress = if (height > 0) {
            min(dy / height.toFloat(), 1f)
        } else 0f

        val halfToothWidth = toothWidth / 2f
        val minLeftX = -halfToothWidth
        val maxRightX = width - halfToothWidth
        val isFullyUnlocked = zipperProgress >= 1.0f

        // Trong preview mode, tính toán heightLimit để vẽ thêm row theo 100% chiều cao progress
        val heightLimit = height.toFloat()

        while (y <= heightLimit) {
            val drawY = y
            val isLeftTooth = toothIndex % 2 == 0

            if (isFullyUnlocked) {
                if (isLeftTooth) {
                    if (isPreviewMode) {
                        val destRect = RectF(minLeftX, drawY, minLeftX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowLeftBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(minLeftX, drawY) {
                            drawBitmap(imageRowLeftBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                } else {
                    if (isPreviewMode) {
                        val destRect = RectF(maxRightX, drawY, maxRightX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowRightBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(maxRightX, drawY) {
                            drawBitmap(imageRowRightBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                }
            } else {
                val distanceFromCurrentToZipperHead = abs(drawY - dy)
                val toothProgress = if (dy > 0) {
                    max(0f, (distanceFromCurrentToZipperHead / dy))
                } else 0f

                val combinedProgress = zipperProgress * toothProgress
                val maxSeparation = width / 0.8f

                val currentSeparation = if(drawY < dy) {
                    combinedProgress * maxSeparation
                } else {
                    0f
                }

                val baseLeftX = centerX - toothWidth - currentSeparation
                val baseRightX = centerX + currentSeparation

                val parabolaFactor = 0.3f
                val parabolaOffset = parabolaFactor * currentSeparation * currentSeparation / maxSeparation

                val sCurveOffsetLeft = calculateSCurveOffset(drawY, dy, currentSeparation, true)
                val sCurveOffsetRight = calculateSCurveOffset(drawY, dy, currentSeparation, false)

                var currentLeftX = baseLeftX - parabolaOffset + sCurveOffsetLeft
                var currentRightX = baseRightX + parabolaOffset + sCurveOffsetRight

                currentLeftX = max(currentLeftX, minLeftX)
                currentRightX = min(currentRightX, maxRightX)

                if (isLeftTooth) {
                    if (isPreviewMode) {
                        val destRect = RectF(currentLeftX, drawY, currentLeftX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowLeftBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(currentLeftX, drawY) {
                            drawBitmap(imageRowLeftBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                } else {
                    if (isPreviewMode) {
                        val destRect = RectF(currentRightX, drawY, currentRightX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowRightBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(currentRightX, drawY) {
                            drawBitmap(imageRowRightBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                }
            }

            toothIndex++

            if (toothIndex % 2 == 0) {
                y += spacing
            }
        }

    }

    private fun drawInterlockZipperTeeth(canvas: Canvas, dy: Float) {
        if (imageRowLeftBitmap == null || imageRowRightBitmap == null) return

        // Calculate scale factors for preview mode
        val scaleX = if (isPreviewMode) {
            val parentView = parent as? ZipperView
            val previewWidth = parentView?.getPreviewItemWidth() ?: (width * 0.466f)
            previewWidth / width
        } else 1f
        
        val scaleY = if (isPreviewMode) {
            val parentView = parent as? ZipperView
            val previewHeight = parentView?.getPreviewItemHeight() ?: (height * 0.508f)
            previewHeight / height
        } else 1f

        val centerX = width / 2f
        val toothWidth = imageRowLeftBitmap!!.width.toFloat() * scaleX
        val toothHeight = imageRowLeftBitmap!!.height.toFloat() * scaleY
        val spacing = percentToothSpacing * toothHeight
        var toothIndex = 0
        var y = 0f

        val zipperProgress = if (height > 0) {
            min(dy / height.toFloat(), 1f)
        } else 0f

        val halfToothWidth = toothWidth / 2f
        val minLeftX = -halfToothWidth
        val maxRightX = width - halfToothWidth
        val isFullyUnlocked = zipperProgress >= 1.0f

        // Trong preview mode, tính toán heightLimit để vẽ thêm row theo 100% chiều cao progress
        val heightLimit = height.toFloat()

        while (y <= heightLimit) {
            val drawY = y
            val isLeftTooth = toothIndex % 2 == 0

            if (isFullyUnlocked) {
                if (isLeftTooth) {
                    if (isPreviewMode) {
                        val destRect = RectF(minLeftX, drawY, minLeftX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowLeftBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(minLeftX, drawY) {
                            drawBitmap(imageRowLeftBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                } else {
                    if (isPreviewMode) {
                        val destRect = RectF(maxRightX, drawY, maxRightX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowRightBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(maxRightX, drawY) {
                            drawBitmap(imageRowRightBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                }
            } else {
                val distanceFromCurrentToZipperHead = abs(drawY - dy)
                val toothProgress = if (dy > 0) {
                    max(0f, (distanceFromCurrentToZipperHead / dy))
                } else 0f

                val combinedProgress = zipperProgress * toothProgress
                val maxSeparation = width / 0.8f

                val currentSeparation = if(drawY < dy) {
                    combinedProgress * maxSeparation
                } else {
                    0f
                }

                val baseToothOffsetFromCenter = toothWidth * toothCenterOffsetPercent

                val initialLeftX = centerX + baseToothOffsetFromCenter - toothWidth
                val initialRightX = centerX - baseToothOffsetFromCenter

                val baseLeftX = initialLeftX - currentSeparation
                val baseRightX = initialRightX + currentSeparation

                val parabolaFactor = 0.3f
                val parabolaOffset = parabolaFactor * currentSeparation * currentSeparation / maxSeparation

                val sCurveOffsetLeft = calculateSCurveOffset(drawY, dy, currentSeparation, true)
                val sCurveOffsetRight = calculateSCurveOffset(drawY, dy, currentSeparation, false)

                var currentLeftX = baseLeftX - parabolaOffset + sCurveOffsetLeft
                var currentRightX = baseRightX + parabolaOffset + sCurveOffsetRight

                currentLeftX = max(currentLeftX, minLeftX)
                currentRightX = min(currentRightX, maxRightX)

                if (isLeftTooth) {
                    if (isPreviewMode) {
                        val destRect = RectF(currentLeftX, drawY, currentLeftX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowLeftBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(currentLeftX, drawY) {
                            drawBitmap(imageRowLeftBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                } else {
                    if (isPreviewMode) {
                        val destRect = RectF(currentRightX, drawY, currentRightX + toothWidth, drawY + toothHeight)
                        canvas.drawBitmap(imageRowRightBitmap!!, null, destRect, mPaint)
                    } else {
                        canvas.withTranslation(currentRightX, drawY) {
                            drawBitmap(imageRowRightBitmap!!, 0f, 0f, mPaint)
                        }
                    }
                }
            }

            y += spacing
            toothIndex++
        }

    }

    private fun drawZipperLockHead(canvas: Canvas, dy: Float) {
        mBitmapZipper = zipperBitmap
        mBitmapZipper?.let { lock ->
            if (isPreviewMode) {
                // In preview mode, scale zipper lock head to 46.6% x 50.8%
                val parentView = parent as? ZipperView
                val previewWidth = parentView?.getPreviewItemWidth() ?: (width * 0.466f)
                val previewHeight = parentView?.getPreviewItemHeight() ?: (height * 0.508f)
                
                val scaleX = previewWidth / width
                val scaleY = previewHeight / height
                
                val scaledLockWidth = lock.width * scaleX
                val scaledLockHeight = lock.height * scaleY
                val scaledDy = dy * scaleY
                
                val destRect = RectF(
                    width / 2f - scaledLockWidth / 2f,
                    dy,
                    width / 2f + scaledLockWidth / 2f,
                    dy + scaledLockHeight
                )

                val scaleBitmap = lock.scale(scaledLockWidth.toInt(), scaledLockHeight.toInt())
                
                canvas.drawBitmap(scaleBitmap, null, destRect, mPaint)
            } else {
                canvas.drawBitmap(
                    lock,
                    width / 2f - lock.width / 2f,
                    dy,
                    mPaint
                )
            }
        }
    }

    private fun createInformationBitmapViews(): Bitmap? {
        batteryContainer.removeAllViews()
        infoContainer.removeAllViews()

        // Check settings from PrefUtil before adding views
        val showDateTime = prefUtil?.dateTimeEnabled ?: true
        val showBattery = prefUtil?.batteryWidgetEnabled ?: true

        if (showDateTime) {
            infoContainer.addView(timeTextView)
            infoContainer.addView(dateTextView)
        }

        if (showBattery) {
            batteryContainer.addView(batteryIconView)
            batteryContainer.addView(batteryTextView)
            infoContainer.addView(batteryContainer)
        }

        updateTimeAndDate()
        updateBatteryInfo()

        return kotlin.runCatching { infoContainer.renderToBitmap() } .getOrNull()
    }

    private fun drawInformationWithTranslation(canvas: Canvas, zipperProgress: Float) {
        if (mInformationBitmap == null) return

        val translationX = -width.toFloat() * zipperProgress

        if (isPreviewMode) {
            // In preview mode, scale text elements to 46.6% x 50.8%
            val parentView = parent as? ZipperView
            val previewWidth = parentView?.getPreviewItemWidth() ?: (width * 0.466f)
            val previewHeight = parentView?.getPreviewItemHeight() ?: (height * 0.508f)
            
            val scaleX = previewWidth / width
            val scaleY = previewHeight / height
            
            val scaledRect = Rect(
                (informationRect.left * scaleX + translationX * scaleX).toInt(),
                (informationRect.top * scaleY).toInt(),
                (informationRect.right * scaleX + translationX * scaleX).toInt(),
                (informationRect.bottom * scaleY).toInt()
            )
            
            if (scaledRect.right > 0) {
                canvas.drawBitmap(mInformationBitmap!!, null, scaledRect, mPaint)
            }
        } else {
            val destRect = Rect(
                (informationRect.left + translationX).toInt(),
                informationRect.top,
                (informationRect.right + translationX).toInt(),
                informationRect.bottom
            )

            if (destRect.right > 0) {
                canvas.drawBitmap(mInformationBitmap!!, null, destRect, mPaint)
            }
        }
    }

    private fun drawQuickActionButtons(canvas: Canvas) {
        // Không hiển thị nút Camera và Flash trong preview mode hoặc khi đã unlock
        if (currentProgress >= 1f || isPreviewMode) return

        val iconPaint = Paint().apply {
            isAntiAlias = true
            isFilterBitmap = true
        }

        // Draw camera button
        /*val cameraButtonPaint = Paint().apply {
            isAntiAlias = true
            color = if (isTouchingCamera) {
                Color.argb(180, 255, 255, 255)
            } else {
                Color.argb(120, 0, 0, 0)
            }
        }
        canvas.drawRoundRect(cameraButtonRect, 28f, 28f, cameraButtonPaint)
        cameraIcon?.let { icon ->
            val iconSize = dpToPx(24)
            val iconLeft = cameraButtonRect.centerX() - iconSize / 2f
            val iconTop = cameraButtonRect.centerY() - iconSize / 2f
            val iconRect = RectF(iconLeft, iconTop, iconLeft + iconSize, iconTop + iconSize)
            canvas.drawBitmap(icon, null, iconRect, iconPaint)
        }*/

        // Draw flashlight button
        if (showFlashlightIcon) {
            val flashlightButtonPaint = Paint().apply {
                isAntiAlias = true
                color = if (isTouchingFlashlight) {
                    Color.argb(180, 255, 255, 255)
                } else if (isFlashlightOn) {
                    Color.argb(150, 255, 235, 59)
                } else {
                    Color.argb(120, 0, 0, 0)
                }
            }
            canvas.drawRoundRect(flashlightButtonRect, 28f, 28f, flashlightButtonPaint)
            val flashIcon = if (isFlashlightOn) flashlightOnIcon else flashlightIcon
            flashIcon?.let { icon ->
                val iconSize = dpToPx(24)
                val iconLeft = flashlightButtonRect.centerX() - iconSize / 2f
                val iconTop = flashlightButtonRect.centerY() - iconSize / 2f
                val iconRect = RectF(iconLeft, iconTop, iconLeft + iconSize, iconTop + iconSize)
                canvas.drawBitmap(icon, null, iconRect, iconPaint)
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        // Block touch events in preview mode
        if (isPreviewMode) {
            return false
        }

        val x = event.x
        val y = event.y
        yTouch = y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                touchStartX = x
                touchStartY = y

                //isTouchingCamera = cameraButtonRect.contains(x, y)
                isTouchingFlashlight = showFlashlightIcon && flashlightButtonRect.contains(x, y)

                if (!isTouchingFlashlight) {
                    isTouch = true
                    externalPlaySound?.invoke()
                }
                invalidate()
            }

            MotionEvent.ACTION_MOVE -> {
                if (!isTouchingFlashlight) {
                    moveY = yTouch - event.y

                    if (isTouch && (yTouch - moveY) > 0) {
                        val dy = (yTouch - moveY)
                        val zipperProgress = if (height > 0) min(dy / height.toFloat(), 1f) else 0f

                        val hasSecurityEnabled = securityManager?.hasSecurity() == true && prefUtil?.passwordEnabled == true

                        currentProgress = if (hasSecurityEnabled && zipperProgress >= unlockThreshold && hasReachedThreshold) {
                            unlockThreshold
                        } else {
                            zipperProgress
                        }

                        externalDragVibration?.invoke()

                        if (zipperProgress >= unlockThreshold && !hasReachedThreshold) {
                            hasReachedThreshold = true
                            externalStrongVibration?.invoke()

                            if(hasSecurityEnabled) {
                                onAuthenticationRequiredListener?.invoke()
                                currentProgress = unlockThreshold
                            } else {
                                // If password is disabled, complete unlock immediately
                                completeUnlock()
                            }
                        }
                    }
                    invalidate()
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                /*if (isTouchingCamera && cameraButtonRect.contains(x, y)) {
                    onCameraRequestListener?.invoke()
                } else*/ if (isTouchingFlashlight && showFlashlightIcon && flashlightButtonRect.contains(x, y)) {
                    onFlashlightToggleListener?.invoke()
                } else if (isTouch) {
                    if (currentProgress >= unlockThreshold) {
                        val hasSecurityEnabled = securityManager?.hasSecurity() == true && prefUtil?.passwordEnabled == true

                        if (hasSecurityEnabled) {
                            // Keep at 80% until unlocked
                        } else {
                            completeUnlock()
                        }
                    } else {
                        animateToProgress(0f)
                        resetToLocked()
                        hasReachedThreshold = false
                    }
                }

                externalPauseSound?.invoke()

                //isTouchingCamera = false
                isTouchingFlashlight = false
                isTouch = false

                invalidate()
            }
        }
        return true
    }

    // Setters for callbacks
    fun setOnUnlockListener(listener: () -> Unit) {
        onUnlockListener = listener
    }

    fun setOnAuthenticationRequiredListener(listener: () -> Unit) {
        onAuthenticationRequiredListener = listener
    }

    /*fun setOnCameraRequestListener(listener: () -> Unit) {
        onCameraRequestListener = listener
    }*/

    fun setOnFlashlightToggleListener(listener: () -> Unit) {
        onFlashlightToggleListener = listener
    }

    fun setOnProgressResetListener(listener: () -> Unit) {
        onProgressResetListener = listener
    }
    
    fun setPrefUtil(prefUtil: PrefUtil) {
        this.prefUtil = prefUtil
        // Refresh information display when settings change
        refreshInformationDisplay()
    }

    fun refreshInformationDisplay() {
        // Recreate information bitmap with current settings
        mInformationBitmap = createInformationBitmapViews()
        invalidate()
    }

    fun updateFlashlightState(isOn: Boolean) {
        isFlashlightOn = isOn
        invalidate()
    }
    
    fun setShowFlashlightIcon(show: Boolean) {
        showFlashlightIcon = show
        invalidate()
    }

    fun completeUnlock() {
        animateToProgress(1f) {
            externalStrongVibration?.invoke()

            postDelayed({
                onUnlockListener?.invoke()
            }, 1000)
        }
    }

    fun resetToLocked() {
        currentProgress = 0f
        hasReachedThreshold = false
        isTouch = false
        
        // Notify that progress has been reset to 0
        onProgressResetListener?.invoke()
        
        invalidate()
    }

    // Font and Color customization methods
    fun applyFontFamily(fontResourceId: Int) {
        try {
            val typeface = ResourcesCompat.getFont(context, fontResourceId)
            typeface?.let {
                timeTextView.typeface = it
                dateTextView.typeface = it
                batteryTextView.typeface = it
                invalidate()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun applyFontColor(colorResourceId: Int) {
        try {
            val color = ContextCompat.getColor(context, colorResourceId)
            timeTextView.setTextColor(color)
            dateTextView.setTextColor(color)
            batteryTextView.setTextColor(color)
            invalidate()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun applyFontAndColorSettings(fontResourceId: Int?, colorResourceId: Int?) {
        fontResourceId?.let { applyFontFamily(it) }
        colorResourceId?.let { applyFontColor(it) }
    }

    private fun animateToProgress(targetProgress: Float, onComplete: (() -> Unit)? = null) {
        val animator = ValueAnimator.ofFloat(currentProgress, targetProgress)
        animator.duration = animationDuration

        var hasVibratedDuringAnimation = false

        animator.addUpdateListener { animation ->
            val progress = animation.animatedValue as Float
            currentProgress = progress

            if (targetProgress == 1f && progress >= 0.9f && !hasVibratedDuringAnimation) {
                hasVibratedDuringAnimation = true
                externalDragVibration?.invoke()
            }

            invalidate()
        }

        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onComplete?.invoke()
            }
        })

        animator.start()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()

        // Cleanup resources
        tempBitmap?.recycle()
        maskBitmap?.recycle()
        mBitmapWallpaper?.recycle()
        mBitmapZipper?.recycle()
        zipperBitmap?.recycle()
        //toothLeftBitmap?.recycle()
        //toothRightBitmap?.recycle()
        wallpaperBitmap?.recycle()
        imageRowLeftBitmap?.recycle()
        imageRowRightBitmap?.recycle()
        //cameraIcon?.recycle()
        flashlightIcon?.recycle()
        flashlightOnIcon?.recycle()
        cachedDynamicMask?.recycle()

        timeUpdateTimer?.cancel()
        batteryReceiver?.let { receiver ->
            try {
                context.unregisterReceiver(receiver)
            } catch (e: IllegalArgumentException) {
                // Receiver was not registered
            }
        }
    }
}