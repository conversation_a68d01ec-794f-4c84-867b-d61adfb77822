package co.ziplock.customview

import android.content.Context
import android.graphics.Bitmap
import android.media.MediaPlayer
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.widget.FrameLayout

import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.util.PrefUtil

class ZipperView : FrameLayout {
    // Background Layer (Bottom Layer)
    private lateinit var backgroundLayer: ZipperBackgroundView
    
    // Top Layer with Zipper)
    private lateinit var zipperTopLayerView: ZipperTopLayerView
    
    // Authentication Layer
    private lateinit var authenticationLayer: ZipperAuthenticationView
    
    // Forgot Password Layer
    private lateinit var forgotPasswordLayer: ZipperForgotPasswordView
    
    private var style: StyleRow = StyleRow.STYLE_1
    private var wSize = 0
    private var hSize = 0
    
    // Preview mode settings
    private var isPreviewMode = false
    private var previewItemWidth = 0f
    private var previewItemHeight = 0f
    
    // Sound and vibration
    private var mediaPlayer: MediaPlayer? = null
    private var vibrator: Vibrator? = null
    private var isPlayingSound = false
    private var isSoundEnabled = true
    private var isVibrationEnabled = true
    private var lastVibrateTime = 0L
    private val VIBRATION_INTERVAL = 100L
    private var soundUrl: String? = null

    // Security
    private var securityManager: SecurityManager? = null
    private var prefUtil: PrefUtil? = null
    
    // Authentication attempt management
    private var attemptCount = 0
    private var isLockoutActive = false

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        setupLayers()
        setupSoundAndVibration()
        setupSecurity()
    }
    
    fun setPreviewMode(previewMode: Boolean, previewProgress: Float = 0.6f) {
        isPreviewMode = previewMode
        if (previewMode) {
            // Disable sound and vibration in preview mode
            setSoundEnabled(false)
            setVibrationEnabled(false)
            // Calculate preview item dimensions
            calculatePreviewDimensions()
            // Set zipper to preview mode with specified progress
            zipperTopLayerView.setPreviewMode(true, previewProgress)
            // Disable touch events in preview mode
            isClickable = false
            isFocusable = false
        } else {
            zipperTopLayerView.setPreviewMode(false, 0f)
            // Re-enable touch events
            isClickable = true
            isFocusable = true
        }
        invalidate()
    }
    
    private fun setupLayers() {
        // Create background layer (bottom)
        backgroundLayer = ZipperBackgroundView(context)
        addView(backgroundLayer, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        
        // Create wallpaper layer (top)
        zipperTopLayerView = ZipperTopLayerView(context)
        addView(zipperTopLayerView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        
        // Create authentication layer
        authenticationLayer = ZipperAuthenticationView(context)
        addView(authenticationLayer, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        
        // Create forgot password layer
        forgotPasswordLayer = ZipperForgotPasswordView(context)
        addView(forgotPasswordLayer, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        
        // Forward touch events to wallpaper layer
        zipperTopLayerView.setOnTouchListener { _, event ->
            zipperTopLayerView.onTouchEvent(event)
        }
        
        // Setup vibration and sound for wallpaper layer
        zipperTopLayerView.setupExternalVibration(this::performDragVibration, this::performStrongVibration)
        zipperTopLayerView.setupExternalSound(this::playSound, this::pauseSound)
        
        // Setup progress reset callback to hide authentication when user cancels drag
        zipperTopLayerView.setOnProgressResetListener {
            // Hide authentication if it's currently visible
            if (authenticationLayer.isAuthenticationVisible()) {
                authenticationLayer.hideAuthentication()
            }
        }
    }
    
    private fun setupSoundAndVibration() {
        // Setup vibrator
        vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator
        } else {
            @Suppress("DEPRECATION")
            context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        }
    }
    
    private fun setupSecurity() {
        //securityManager = SecurityManager(context)
        zipperTopLayerView.securityManager = securityManager
        
        // Only setup callbacks if securityManager is not null
        securityManager?.let { sm ->
            authenticationLayer.setSecurityManager(sm)
            forgotPasswordLayer.setSecurityManager(sm)
            
            // Setup authentication layer callbacks
            authenticationLayer.setOnAuthenticatedListener {
                // Reset attempt count on successful authentication
                resetAttemptCount()
                zipperTopLayerView.completeUnlock()
            }
            
            // Setup authentication result callback
            authenticationLayer.setOnAuthenticationResultListener { isSuccess, securityType ->
                handleAuthenticationResult(isSuccess, securityType)
            }
            
            // Setup lockout completed callback
            authenticationLayer.setOnLockoutCompletedListener {
                resetAttemptCount()
            }
            
            authenticationLayer.setOnForgotPasswordListener {
                authenticationLayer.hideAuthentication()
                forgotPasswordLayer.showForgotPassword()
            }
            
            authenticationLayer.setOnCancelListener {
                // Reset attempt count when user cancels authentication
                resetAttemptCount()
                zipperTopLayerView.resetToLocked()
            }
            
            // Setup forgot password layer callbacks
            forgotPasswordLayer.setOnPasswordResetSuccessListener {
                // Password reset successful, show authentication again
                forgotPasswordLayer.hideForgotPassword()
                authenticationLayer.hideAuthentication()
                completeUnlock()
            }
            
            forgotPasswordLayer.setOnCancelListener {
                forgotPasswordLayer.hideForgotPassword()
                authenticationLayer.showAuthentication()
            }
            
            forgotPasswordLayer.setOnSkipSetupListener {
                // Skip setup confirmed, complete unlock and hide forgot password layer
                forgotPasswordLayer.hideForgotPassword()
                completeUnlock()

                //Clear any existing security data & clear password
                securityManager?.clearSecurity()
                prefUtil?.passwordEnabled = false
            }
        }
    }
    
    fun setRowResponse(rowResponse: RowResponse?) {
        zipperTopLayerView.setRowResponse(rowResponse)
        invalidate()
    }
    
    fun setZipperResponse(zipperResponse: ZipperResponse?) {
        zipperTopLayerView.setZipperResponse(zipperResponse)
        invalidate()
    }

    fun setItemDepth(depth: Float) {
        zipperTopLayerView.setItemDepth(depth)
    }
    
    fun setWallpaperBitmap(bitmap: Bitmap?) {
        zipperTopLayerView.setWallpaperShowingBitmap(bitmap)
    }
    
    fun setSecurityManager(securityManager: SecurityManager) {
        this.securityManager = securityManager
        setupSecurity()
    }
    
    fun setPrefUtil(prefUtil: PrefUtil) {
        this.prefUtil = prefUtil
        zipperTopLayerView.setPrefUtil(prefUtil)
        // Update sound and vibration settings
        setSoundEnabled(prefUtil.soundEnabled)
        setVibrationEnabled(prefUtil.vibrationEnabled)
    }

    fun refreshSettings() {
        prefUtil?.let { pref ->
            setSoundEnabled(pref.soundEnabled)
            setVibrationEnabled(pref.vibrationEnabled)
            zipperTopLayerView.refreshInformationDisplay()
        }
    }
    
    fun setSoundUrl(url: String?) {
        soundUrl = url
        // Stop current sound if playing
        pauseSound()
        // Release current MediaPlayer
        mediaPlayer?.release()
        mediaPlayer = null

        // Initialize MediaPlayer if URL is provided
        if (!url.isNullOrEmpty()) {
            initializeMediaPlayer(url)
        }
    }
    
    fun getSoundUrl(): String? = soundUrl
    
    fun hasSoundUrl(): Boolean = !soundUrl.isNullOrEmpty()
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        wSize = w
        hSize = h
        if (isPreviewMode) {
            calculatePreviewDimensions()
        }
    }
    
    private fun calculatePreviewDimensions() {
        previewItemWidth = wSize * 0.466f
        previewItemHeight = hSize * 0.508f
    }
    
    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        // Block touch events in preview mode
        return if (isPreviewMode) {
            true
        } else {
            super.onInterceptTouchEvent(ev)
        }
    }
    
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        // Block touch events in preview mode
        return if (isPreviewMode) {
            false
        } else {
            super.onTouchEvent(event)
        }
    }
    
    // Getter methods for preview mode
    fun isInPreviewMode(): Boolean = isPreviewMode
    fun getPreviewItemWidth(): Float = previewItemWidth
    fun getPreviewItemHeight(): Float = previewItemHeight
    
    // Delegate methods to wallpaper layer
    fun setOnUnlockListener(listener: () -> Unit) {
        zipperTopLayerView.setOnUnlockListener(listener)
    }

    fun requestOnAuthenticationRequiredListener() {
        zipperTopLayerView.setOnAuthenticationRequiredListener {
            // Show authentication layer instead of calling the listener
            authenticationLayer.showAuthentication()
        }
    }
    
    /*fun setOnCameraRequestListener(listener: () -> Unit) {
        zipperTopLayerView.setOnCameraRequestListener(listener)
    }*/
    
    fun setOnFlashlightToggleListener(listener: () -> Unit) {
        zipperTopLayerView.setOnFlashlightToggleListener(listener)
    }
    
    fun setOnRequestOtpListener(listener: (String) -> Unit) {
        forgotPasswordLayer.setOnRequestOtpListener(listener)
    }
    
    fun setOnOTPValidationListener(listener: (String, () -> Unit, () -> Unit) -> Unit) {
        forgotPasswordLayer.setOnOTPValidationListener(listener)
    }
    
    fun handleOTPVerificationResult(isSuccess: Boolean, errorMessage: String? = null) {
        forgotPasswordLayer.handleOTPVerificationResult(isSuccess, errorMessage)
    }
    
    fun setSoundEnabled(enabled: Boolean) {
        isSoundEnabled = enabled
        if (!enabled) {
            pauseSound()
        }
    }
    
    fun isCurrentlyPlayingSound(): Boolean = isPlayingSound
    
    fun setVibrationEnabled(enabled: Boolean) {
        isVibrationEnabled = enabled
    }

    fun applyFontAndColorSettings(fontResourceId: Int?, colorResourceId: Int?) {
        if (fontResourceId != null) {
            zipperTopLayerView.applyFontAndColorSettings(
                fontResourceId,
                colorResourceId
            )
            invalidate()
        }
    }

    fun isSoundEnabled(): Boolean = isSoundEnabled
    fun isVibrationEnabled(): Boolean = isVibrationEnabled
    
    fun updateFlashlightState(isOn: Boolean) {
        zipperTopLayerView.updateFlashlightState(isOn)
    }
    
    fun setShowFlashlightIcon(show: Boolean) {
        zipperTopLayerView.setShowFlashlightIcon(show)
    }
    
    fun completeUnlock() {
        zipperTopLayerView.completeUnlock()
    }
    
    fun resetToLocked() {
        zipperTopLayerView.resetToLocked()
    }
    
    // Delegate methods to background layer
    fun setBackgroundBitmap(bitmap: Bitmap?) {
        backgroundLayer.setBackgroundBitmap(bitmap)
    }
    
    fun setBackgroundFromResource(resourceId: Int) {
        backgroundLayer.setBackgroundFromResource(resourceId)
    }
    
    override fun setBackgroundColor(color: Int) {
        backgroundLayer.setBackgroundColor(color)
    }
    
    // Sound methods
    private fun initializeMediaPlayer(url: String) {
        try {
            mediaPlayer = MediaPlayer().apply {
                setDataSource(url)
                prepareAsync()
                setOnPreparedListener { mp ->
                    mp.isLooping = true
                    Log.d("ZipperView", "MediaPlayer prepared and ready to play")
                }
                setOnErrorListener { _, what, extra ->
                    Log.e("ZipperView", "MediaPlayer error: what=$what, extra=$extra")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e("ZipperView", "Error initializing MediaPlayer", e)
            mediaPlayer = null
        }
    }
    
    private fun playSound() {
        if (!isSoundEnabled || isPlayingSound) return
        
        try {
            mediaPlayer?.apply {
                if (!isPlaying) {
                    start()
                    isPlayingSound = true
                }
            } ?: run {
                // Fallback: if no MediaPlayer, just set flag (original behavior)
                isPlayingSound = true
            }
        } catch (e: Exception) {
            Log.e("ZipperView", "Error playing sound", e)
            isPlayingSound = false
        }
    }
    
    private fun pauseSound() {
        if (!isPlayingSound) return
        
        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    pause()
                }
            }
        } catch (e: Exception) {
            Log.e("ZipperView", "Error stopping sound", e)
        } finally {
            isPlayingSound = false
        }
    }
    
    // Vibration methods
    private fun performDragVibration() {
        if (!isVibrationEnabled) return
        
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastVibrateTime < VIBRATION_INTERVAL) return
        
        lastVibrateTime = currentTime
        
        vibrator?.let { vib ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val vibrationEffect = VibrationEffect.createWaveform(
                    longArrayOf(0, 20, 20),
                    -1
                )
                vib.vibrate(vibrationEffect)
            } else {
                @Suppress("DEPRECATION")
                vib.vibrate(20)
            }
        }
    }
    
    private fun performStrongVibration() {
        if (!isVibrationEnabled) return
        
        vibrator?.let { vib ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val vibrationEffect = VibrationEffect.createWaveform(
                    longArrayOf(0, 100, 50, 100, 50, 150),
                    intArrayOf(
                        0, VibrationEffect.DEFAULT_AMPLITUDE, 0,
                        VibrationEffect.DEFAULT_AMPLITUDE, 0,
                        VibrationEffect.DEFAULT_AMPLITUDE
                    ),
                    -1
                )
                vib.vibrate(vibrationEffect)
            } else {
                @Suppress("DEPRECATION")
                vib.vibrate(300)
            }
        }
    }
    
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        
        // Stop and release MediaPlayer resources
        pauseSound()
        mediaPlayer?.release()
        mediaPlayer = null
        soundUrl = null
    }
    
    // Authentication attempt management methods
    private fun handleAuthenticationResult(isSuccess: Boolean, securityType: SecurityManager.SecurityType) {
        if (isSuccess) {
            resetAttemptCount()
        } else {
            attemptCount++
            
            val maxAttempts = when (securityType) {
                SecurityManager.SecurityType.PIN -> MAX_PIN_ATTEMPTS
                SecurityManager.SecurityType.PATTERN -> MAX_PATTERN_ATTEMPTS
                else -> MAX_PIN_ATTEMPTS
            }
            
            if (attemptCount >= maxAttempts) {
                // Too many failed attempts - start lockout
                startLockout()
            } else {
                // Show error message with remaining attempts
                val attemptsLeft = maxAttempts - attemptCount
                authenticationLayer.showAttemptsLeftError(attemptsLeft, securityType)
            }
        }
    }
    
    private fun resetAttemptCount() {
        attemptCount = 0
        isLockoutActive = false
    }
    
    private fun startLockout() {
        isLockoutActive = true
        //securityManager?.startLockout()
        authenticationLayer.handleLockout()
    }
    
    // Getter methods for attempt management
    fun getAttemptCount(): Int = attemptCount
    fun isInLockout(): Boolean = isLockoutActive
    
    companion object {
        const val MAX_PIN_ATTEMPTS = 5
        const val MAX_PATTERN_ATTEMPTS = 5
    }
}

enum class StyleRow {
    STYLE_1, STYLE_2, STYLE_3
}