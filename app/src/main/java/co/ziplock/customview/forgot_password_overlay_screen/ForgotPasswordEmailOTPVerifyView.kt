package co.ziplock.customview.forgot_password_overlay_screen

import android.content.Context
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.text.bold
import androidx.core.text.buildSpannedString
import androidx.core.view.isVisible
import co.ziplock.R
import co.ziplock.databinding.OverlayEmailOtpVerifyBinding
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.framework.presentation.security.email_otp_verify_when_forgot_password_fragment.EmailOTPVerifyWhenForgotPasswordFragment
import co.ziplock.util.Constant
import co.ziplock.util.EmailUtils

class ForgotPasswordEmailOTPVerifyView : FrameLayout {
    
    private lateinit var binding: OverlayEmailOtpVerifyBinding
    private var securityManager: SecurityManager? = null
    
    // Countdown timers
    private var resendCountDownTimer: CountDownTimer? = null
    private var otpExpirationTimer: CountDownTimer? = null

    // Constants for countdown

    // Callbacks
    private var onOTPVerifiedListener: (() -> Unit)? = null
    private var onRequestNewOtpListener: ((String) -> Unit)? = null
    private var onBackListener: (() -> Unit)? = null
    private var onOTPValidationListener: ((String, () -> Unit, () -> Unit) -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        binding = OverlayEmailOtpVerifyBinding.inflate(LayoutInflater.from(context), this, true)
        setupUI()
    }
    
    fun setSecurityManager(securityManager: SecurityManager) {
        this.securityManager = securityManager
        // Set up text instructions now that securityManager is available
        setUpTextInstructions()
    }
    
    fun setOnOTPVerifiedListener(listener: () -> Unit) {
        onOTPVerifiedListener = listener
    }
    
    fun setOnRequestNewOtpListener(listener: (String) -> Unit) {
        onRequestNewOtpListener = listener
    }
    
    fun setOnBackListener(listener: () -> Unit) {
        onBackListener = listener
    }
    
    fun setOnOTPValidationListener(listener: (String, () -> Unit, () -> Unit) -> Unit) {
        onOTPValidationListener = listener
    }
    
    fun startCountdowns() {
        startOtpExpirationCountdown()
        startResendCountdown()
    }
    
    fun stopCountdowns() {
        resendCountDownTimer?.cancel()
        resendCountDownTimer = null
        otpExpirationTimer?.cancel()
        otpExpirationTimer = null
    }
    
    private fun setupUI() {
        val etCode1 = binding.etCode1
        val etCode2 = binding.etCode2
        val etCode3 = binding.etCode3
        val etCode4 = binding.etCode4
        val etCode5 = binding.etCode5
        val etCode6 = binding.etCode6
        val btnVerifyCode = binding.btnVerifyCode
        val tvResendEmail = binding.tvResendEmail
        //val tvOpenEmailApp = binding.tvOpenEmailApp
        val tvError = binding.tvError
        val progressBar = binding.progressBar
        val btnBack = binding.btnBack
        
        val otpEditTexts = listOf(etCode1, etCode2, etCode3, etCode4, etCode5, etCode6)

        // Note: setUpTextInstructions() is now called after securityManager is set

        // Set up OTP input handling
        setupOtpInput(otpEditTexts, btnVerifyCode, tvError)
        
        // Set up buttons
        btnVerifyCode.setOnClickListener {
            val otp = otpEditTexts.joinToString("") { it.text.toString() }
            
            if (otp.length != 6) {
                tvError.text = context.getString(R.string.invalid_verification_code)
                tvError.visibility = View.VISIBLE
                return@setOnClickListener
            }
            
            // Show loading
            progressBar.visibility = View.VISIBLE
            btnVerifyCode.isEnabled = false
            
            // Use external OTP validation if available
            onOTPValidationListener?.invoke(
                otp,
                // Success callback
                {
                    progressBar.visibility = View.GONE
                    btnVerifyCode.isEnabled = true
                    onOTPVerifiedListener?.invoke()
                },
                // Failure callback
                {
                    progressBar.visibility = View.GONE
                    btnVerifyCode.isEnabled = true
                    tvError.text = context.getString(R.string.invalid_verification_code)
                    tvError.visibility = View.VISIBLE
                }
            )
        }
        
        tvResendEmail.setOnClickListener {
            if (tvResendEmail.isEnabled) {
                // Request new OTP
                securityManager?.let { security ->
                    onRequestNewOtpListener?.invoke(security.getRecoveryEmail() ?: "")
                }
                
                // Start resend countdown again
                startResendCountdown()
            }
        }

        
        btnBack.setOnClickListener {
            onBackListener?.invoke()
        }
    }
    
    private fun startOtpExpirationCountdown() {
        // Cancel any existing timer
        otpExpirationTimer?.cancel()
        
        val tvCountdown = binding.tvCountdown
        
        otpExpirationTimer = object : CountDownTimer(Constant.TIMEOUT_OTP, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val minutes = millisUntilFinished / 1000 / 60
                val seconds = millisUntilFinished / 1000 % 60
                val timeFormatted = String.format("%02d:%02d", minutes, seconds)
                tvCountdown.text = context.getString(R.string.otp_expires_in, timeFormatted)
            }
            
            override fun onFinish() {
                tvCountdown.text = context.getString(R.string.otp_expired)
                Toast.makeText(context, context.getString(R.string.otp_expired), Toast.LENGTH_SHORT).show()
            }
        }.start()
    }
    
    private fun startResendCountdown() {
        // Cancel any existing resend timer
        resendCountDownTimer?.cancel()
        
        val tvResendEmail = binding.tvResendEmail
        
        // Disable resend button and start countdown
        tvResendEmail.isEnabled = false
        tvResendEmail.alpha = 0.7f
        
        resendCountDownTimer = object : CountDownTimer(Constant.TIME_RESEND_OTP, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds = millisUntilFinished / 1000
                tvResendEmail.text = context.getString(R.string.resend_in_seconds, seconds)
            }
            
            override fun onFinish() {
                // Enable resend button after countdown
                tvResendEmail.isEnabled = true
                tvResendEmail.alpha = 1f
                tvResendEmail.text = context.getString(R.string.resend_email)
                resendCountDownTimer = null
            }
        }.start()
    }

    private fun setupOtpInput(otpEditTexts: List<EditText>, btnVerify: TextView, tvError: TextView) {
        otpEditTexts.forEachIndexed { index, editText ->
            editText.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    if (s?.length == 1 && index < otpEditTexts.size - 1) {
                        otpEditTexts[index + 1].requestFocus()
                    }
                    
                    // Check if all fields are filled
                    val allFilled = otpEditTexts.all { it.text.isNotEmpty() }
                    btnVerify.isEnabled = allFilled
                    
                    if (tvError.isVisible) {
                        tvError.isVisible = false
                    }
                }
            })
            
            editText.setOnKeyListener { _, keyCode, event ->
                if (keyCode == android.view.KeyEvent.KEYCODE_DEL && event.action == android.view.KeyEvent.ACTION_DOWN) {
                    if (editText.text.isEmpty() && index > 0) {
                        otpEditTexts[index - 1].requestFocus()
                        otpEditTexts[index - 1].setText("")
                    }
                }
                false
            }
        }
    }
    
    fun clearOTPInputs() {
        val otpEditTexts = listOf(
            binding.etCode1, binding.etCode2, binding.etCode3,
            binding.etCode4, binding.etCode5, binding.etCode6
        )
        otpEditTexts.forEach { it.setText("") }
        otpEditTexts.first().requestFocus()
    }
    
    fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }
    
    fun hideLoading() {
        binding.progressBar.visibility = View.GONE
        binding.btnVerifyCode.isEnabled = true
    }

    fun setUpTextInstructions() {
        // Only set up instructions if securityManager is available
        securityManager?.let { security ->
            binding.tvInstruction.text = buildSpannedString {
                append(context.getString(R.string.password_recovery_email_sent_prefix))
                bold {
                    append(EmailUtils.maskEmail(security.getRecoveryEmail()))
                    append(".")
                    append("\n")
                    append(context.getString(R.string.password_recovery_email_sent_suffix))
                }
            }
        }
    }
}