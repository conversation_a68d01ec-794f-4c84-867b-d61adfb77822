package co.ziplock.customview.forgot_password_overlay_screen

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.isVisible
import co.ziplock.R
import co.ziplock.databinding.OverlayEmailRecoveryBinding
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.EmailUtils

class ForgotPasswordEmailRecoveryView : FrameLayout {
    
    private lateinit var binding: OverlayEmailRecoveryBinding
    private var securityManager: SecurityManager? = null
    
    // Callbacks
    private var onRequestOtpListener: ((String) -> Unit)? = null
    private var onAnswerSecurityQuestionListener: (() -> Unit)? = null
    private var onBackListener: (() -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        binding = OverlayEmailRecoveryBinding.inflate(LayoutInflater.from(context), this, true)
        setupUI()
    }
    
    fun setSecurityManager(securityManager: SecurityManager) {
        this.securityManager = securityManager
    }
    
    fun setOnRequestOtpListener(listener: (String) -> Unit) {
        onRequestOtpListener = listener
    }
    
    fun setOnAnswerSecurityQuestionListener(listener: () -> Unit) {
        onAnswerSecurityQuestionListener = listener
    }
    
    fun setOnBackListener(listener: () -> Unit) {
        onBackListener = listener
    }
    
    private fun setupUI() {
        val etEmail = binding.etEmail
        val btnContinue = binding.btnContinue
        val tvAnswerSecurityQuestion = binding.tvAnswerSecurityQuestion
        val progressBar = binding.progressBar
        val tvError = binding.tvError
        val btnBack = binding.btnBack
        
        // Enable continue button when email is valid and matches saved email
        etEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val email = s.toString().trim()
                val isValidEmail = EmailUtils.validateEmail(email)
                val isMatchingSavedEmail = isValidEmail && isEmailMatchingSaved(email)
                
                btnContinue.isEnabled = isMatchingSavedEmail
                btnContinue.alpha = if (isMatchingSavedEmail) 1f else 0.3f
                
                if (tvError.isVisible) {
                    tvError.visibility = View.GONE
                }
            }
        })
        
        // Set up buttons
        btnContinue.setOnClickListener {
            val email = etEmail.text.toString().trim()
            
            if (!EmailUtils.validateEmail(email)) {
                tvError.text = context.getString(R.string.invalid_email_address)
                tvError.visibility = View.VISIBLE
                return@setOnClickListener
            }
            
            if (!isEmailMatchingSaved(email)) {
                tvError.text = context.getString(R.string.email_not_matching_saved)
                tvError.visibility = View.VISIBLE
                return@setOnClickListener
            }
            
            // Show loading
            progressBar.visibility = View.VISIBLE
            btnContinue.isEnabled = false
            
            // Request OTP using callback
            onRequestOtpListener?.invoke(email)
            
            // Hide loading after a short delay
            postDelayed({
                progressBar.visibility = View.GONE
                btnContinue.isEnabled = true
            }, 2000)
        }
        
        tvAnswerSecurityQuestion.setOnClickListener {
            onAnswerSecurityQuestionListener?.invoke()
        }
        
        btnBack.setOnClickListener {
            onBackListener?.invoke()
        }
    }
    
    private fun isEmailMatchingSaved(email: String): Boolean {
        return securityManager?.getRecoveryEmail()?.equals(email, ignoreCase = true) == true
    }
    
    fun hideLoading() {
        binding.progressBar.visibility = View.GONE
        binding.btnContinue.isEnabled = true
    }
    
    fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }
}