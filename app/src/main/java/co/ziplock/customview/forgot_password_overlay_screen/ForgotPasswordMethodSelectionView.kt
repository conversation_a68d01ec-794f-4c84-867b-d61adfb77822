package co.ziplock.customview.forgot_password_overlay_screen

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.Toast
import co.ziplock.R
import co.ziplock.databinding.OverlayPasswordRecoveryMethodBinding

class ForgotPasswordMethodSelectionView : FrameLayout {
    
    private lateinit var binding: OverlayPasswordRecoveryMethodBinding

    private var isEmailRecoveryAvailable = false

    // Callbacks
    private var onSecurityQuestionSelected: (() -> Unit)? = null
    private var onEmailRecoverySelected: (() -> Unit)? = null
    private var onCancelListener: (() -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        binding = OverlayPasswordRecoveryMethodBinding.inflate(LayoutInflater.from(context), this, true)
        setupUI()
    }
    
    fun setRecoveryEmailAvailable(recoveryEmailAvailable: Boolean) {
        isEmailRecoveryAvailable = recoveryEmailAvailable
        updateUIBasedOnAvailability()
        invalidate()
    }
    
    fun setOnSecurityQuestionSelectedListener(listener: () -> Unit) {
        onSecurityQuestionSelected = listener
    }
    
    fun setOnEmailRecoverySelectedListener(listener: () -> Unit) {
        onEmailRecoverySelected = listener
    }
    
    fun setOnCancelListener(listener: () -> Unit) {
        onCancelListener = listener
    }
    
    private fun setupUI() {
        // Get UI elements from ViewBinding
        val layoutSecurityQuestionOption = binding.layoutSecurityQuestionOption
        val layoutEmailOption = binding.layoutEmailOption
        val radioSecurityQuestion = binding.radioSecurityQuestion
        val radioEmailRecovery = binding.radioEmailRecovery
        val btnCancel = binding.btnCancel
        val btnContinue = binding.btnContinue
        
        // Variables to track selected method
        var selectedMethod = RecoveryMethod.SECURITY_QUESTION // Default selection
        
        // Set up radio button click listeners
        layoutSecurityQuestionOption.setOnClickListener {
            radioSecurityQuestion.isChecked = true
            radioEmailRecovery.isChecked = false
            selectedMethod = RecoveryMethod.SECURITY_QUESTION
        }
        
        layoutEmailOption.setOnClickListener {
            if (isEmailRecoveryAvailable) {
                radioSecurityQuestion.isChecked = false
                radioEmailRecovery.isChecked = true
                selectedMethod = RecoveryMethod.EMAIL_RECOVERY
            } else {
                Toast.makeText(context, context.getString(R.string.user_has_not_set_up_recovery_email), Toast.LENGTH_SHORT).show()
            }
        }
        
        // Set up button click listeners
        btnCancel.setOnClickListener {
            onCancelListener?.invoke()
        }
        
        btnContinue.setOnClickListener {
            when (selectedMethod) {
                RecoveryMethod.SECURITY_QUESTION -> {
                    onSecurityQuestionSelected?.invoke()
                }
                RecoveryMethod.EMAIL_RECOVERY -> {
                    if (isEmailRecoveryAvailable) {
                        onEmailRecoverySelected?.invoke()
                    } else {
                        Toast.makeText(context, context.getString(R.string.user_has_not_set_up_recovery_email), Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }
    
    private fun updateUIBasedOnAvailability() {
        // Update UI based on email availability
        if (!isEmailRecoveryAvailable) {
            binding.radioEmailRecovery.isEnabled = false
            binding.layoutEmailOption.alpha = 0.5f
            binding.layoutEmailOption.isClickable = false
        }
    }
    
    enum class RecoveryMethod {
        SECURITY_QUESTION,
        EMAIL_RECOVERY
    }
}