package co.ziplock.customview.forgot_password_overlay_screen

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.FrameLayout
import android.widget.TextView
import co.ziplock.R
import co.ziplock.databinding.OverlaySecurityQuestionBinding
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.showKeyboard

class ForgotPasswordSecurityQuestionView : FrameLayout {
    
    private lateinit var binding: OverlaySecurityQuestionBinding
    private var securityManager: SecurityManager? = null
    
    // Callbacks
    private var onCorrectAnswerListener: (() -> Unit)? = null
    private var onRecoverViaEmailListener: (() -> Unit)? = null
    private var onBackListener: (() -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        binding = OverlaySecurityQuestionBinding.inflate(LayoutInflater.from(context), this, true)
        setupUI()
    }
    
    fun setSecurityManager(securityManager: SecurityManager) {
        this.securityManager = securityManager
    }
    
    fun setOnCorrectAnswerListener(listener: () -> Unit) {
        onCorrectAnswerListener = listener
    }
    
    fun setOnRecoverViaEmailListener(listener: () -> Unit) {
        onRecoverViaEmailListener = listener
    }
    
    fun setOnBackListener(listener: () -> Unit) {
        onBackListener = listener
    }
    
    private fun setupUI() {
        val spinnerSecurityQuestion = binding.spinnerSecurityQuestion
        val etSecurityAnswer = binding.etSecurityAnswer
        val btnSubmit = binding.btnSubmit
        val tvRecoverViaEmail = binding.tvRecoverViaEmail
        val tvError = binding.tvError
        val btnBack = binding.btnBack
        
        // Load security questions
        val availableSecurityQuestions = context.resources.getStringArray(R.array.security_questions).toList()
        val questions = listOf(context.getString(R.string.select_question)) + availableSecurityQuestions
        
        val adapter = ArrayAdapter(
            context,
            android.R.layout.simple_spinner_item,
            questions
        ).apply {
            setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        }
        
        spinnerSecurityQuestion.adapter = adapter

        val hasRecoveryEmail = securityManager?.hasRecoveryEmail() ?: false

        if(!hasRecoveryEmail) {
            tvRecoverViaEmail.isEnabled = false
            tvRecoverViaEmail.alpha = 0.7f
        } else {
            tvRecoverViaEmail.isEnabled = true
            tvRecoverViaEmail.alpha = 1f
        }

        // Variables to track selection
        var selectedQuestion = ""
        var selectedAnswer = ""
        
        // Set up spinner listener
        spinnerSecurityQuestion.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0) { // Skip the "Select question" placeholder
                    selectedQuestion = parent?.getItemAtPosition(position)?.toString() ?: ""
                } else {
                    selectedQuestion = ""
                }
                updateSubmitButtonState(selectedQuestion, selectedAnswer, btnSubmit)
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        
        // Enable submit button when answer is not empty and question is selected
        etSecurityAnswer.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                selectedAnswer = s.toString().trim()
                updateSubmitButtonState(selectedQuestion, selectedAnswer, btnSubmit)
                
                // Hide error when user starts typing
                if (selectedAnswer.isNotEmpty()) {
                    tvError.visibility = View.GONE
                }
            }
        })

        etSecurityAnswer.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                showKeyboard()
            }
        }

        // Set up buttons
        btnSubmit.setOnClickListener {
            val answer = etSecurityAnswer.text.toString().trim()
            
            if (selectedQuestion.isEmpty()) {
                tvError.text = context.getString(R.string.please_select_a_security_question)
                tvError.visibility = View.VISIBLE
                return@setOnClickListener
            }
            
            if (answer.isEmpty()) {
                tvError.text = context.getString(R.string.please_enter_an_answer)
                tvError.visibility = View.VISIBLE
                return@setOnClickListener
            }
            
            securityManager?.let { security ->
                val isCorrectQuestion = security.getSecurityQuestionText(context = context) == selectedQuestion
                val isCorrectAnswer = security.verifySecurityAnswer(answer)
                
                if (isCorrectQuestion && isCorrectAnswer) {
                    // Security answer is correct, proceed to password reset
                    security.resetFailedAttempts()
                    onCorrectAnswerListener?.invoke()
                } else {
                    tvError.text = context.getString(R.string.your_question_answer_is_incorrect_try_again)
                    tvError.visibility = View.VISIBLE
                }
            }
        }
        
        tvRecoverViaEmail.setOnClickListener {
            onRecoverViaEmailListener?.invoke()
        }
        
        btnBack.setOnClickListener {
            onBackListener?.invoke()
        }
    }
    
    private fun updateSubmitButtonState(question: String, answer: String, btnSubmit: TextView) {
        val isEnabled = question.isNotEmpty() && answer.isNotEmpty()
        btnSubmit.isEnabled = isEnabled
        btnSubmit.alpha = if (isEnabled) 1f else 0.3f
    }
}