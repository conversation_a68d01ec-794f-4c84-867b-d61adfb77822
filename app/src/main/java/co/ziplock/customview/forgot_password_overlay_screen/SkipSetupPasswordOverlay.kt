package co.ziplock.customview.forgot_password_overlay_screen

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import co.ziplock.R
import co.ziplock.databinding.OverlaySkipSetupPasswordBinding
import co.ziplock.databinding.OverlaySkipSetupPasswordBindingImpl

class SkipSetupPasswordOverlay : FrameLayout {
    
    private lateinit var btnCancel: TextView
    private lateinit var btnSkip: TextView
    
    // Callbacks
    private var onSkipConfirmedListener: (() -> Unit)? = null
    private var onCancelListener: (() -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        // Set background with semi-transparent overlay
        setBackgroundColor(ContextCompat.getColor(context, R.color.auth_overlay_background))
        
        // Inflate layout
        //val view = LayoutInflater.from(context).inflate(R.layout.overlay_skip_setup_password, this, true)
        val binding = OverlaySkipSetupPasswordBinding.inflate(LayoutInflater.from(context), this, true)

        // Find views
        btnCancel = binding.btnCancel
        btnSkip = binding.btnSkip
        
        setupUI()
        visibility = View.GONE
    }
    
    fun setOnSkipConfirmedListener(listener: () -> Unit) {
        onSkipConfirmedListener = listener
    }
    
    fun setOnCancelListener(listener: () -> Unit) {
        onCancelListener = listener
    }
    
    fun show() {
        visibility = View.VISIBLE
    }
    
    fun hide() {
        visibility = View.GONE
    }
    
    private fun setupUI() {
        btnCancel.setOnClickListener {
            hide()
            onCancelListener?.invoke()
        }
        
        btnSkip.setOnClickListener {
            hide()
            onSkipConfirmedListener?.invoke()
        }
    }
}