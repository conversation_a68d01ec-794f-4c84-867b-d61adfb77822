package co.ziplock.customview.forgot_password_overlay_screen

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import co.ziplock.R
import co.ziplock.framework.presentation.manager.SecurityManager

class ZipperForgotPasswordManagerView : FrameLayout {
    
    private var securityManager: SecurityManager? = null
    private var currentStep: ForgotPasswordStep = ForgotPasswordStep.METHOD_SELECTION
    private var currentView: View? = null
    
    // View instances
    private var forgotPasswordMethodSelectionView: ForgotPasswordMethodSelectionView? = null
    private var forgotPasswordSecurityQuestionView: ForgotPasswordSecurityQuestionView? = null
    private var forgotPasswordEmailRecoveryView: ForgotPasswordEmailRecoveryView? = null
    private var forgotPasswordEmailOTPVerifyView: ForgotPasswordEmailOTPVerifyView? = null
    private var zipperSetupNewPasswordView: ZipperSetupNewPasswordView? = null
    
    // Callbacks
    private var onPasswordResetSuccessListener: (() -> Unit)? = null
    private var onCancelListener: (() -> Unit)? = null
    private var onRequestOtpListener: ((String) -> Unit)? = null
    private var onOTPValidationListener: ((String, () -> Unit, () -> Unit) -> Unit)? = null
    private var onSkipSetupListener: (() -> Unit)? = null
    
    constructor(context: Context) : super(context) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }
    
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init()
    }
    
    private fun init() {
        // Set background with semi-transparent overlay
        setBackgroundColor(ContextCompat.getColor(context, R.color.auth_overlay_background))
        visibility = View.GONE
    }
    
    fun setSecurityManager(securityManager: SecurityManager) {
        this.securityManager = securityManager
    }
    
    fun setOnPasswordResetSuccessListener(listener: () -> Unit) {
        onPasswordResetSuccessListener = listener
    }
    
    fun setOnCancelListener(listener: () -> Unit) {
        onCancelListener = listener
    }
    
    fun setOnRequestOtpListener(listener: (String) -> Unit) {
        onRequestOtpListener = listener
    }
    
    fun setOnOTPValidationListener(listener: (String, () -> Unit, () -> Unit) -> Unit) {
        onOTPValidationListener = listener
    }
    
    fun setOnSkipSetupListener(listener: () -> Unit) {
        onSkipSetupListener = listener
    }
    
    fun showForgotPassword() {
        currentStep = ForgotPasswordStep.METHOD_SELECTION
        showMethodSelection()
        visibility = View.VISIBLE
    }
    
    fun hideForgotPassword() {
        visibility = View.GONE
        removeAllViews()
        currentView = null
        currentStep = ForgotPasswordStep.METHOD_SELECTION
        
        // Clean up OTP timers if active
        forgotPasswordEmailOTPVerifyView?.stopCountdowns()
        
        // Clear view references
        clearViewReferences()
    }
    
    private fun clearViewReferences() {
        forgotPasswordMethodSelectionView = null
        forgotPasswordSecurityQuestionView = null
        forgotPasswordEmailRecoveryView = null
        forgotPasswordEmailOTPVerifyView = null
        zipperSetupNewPasswordView = null
    }
    
    private fun showMethodSelection() {
        currentStep = ForgotPasswordStep.METHOD_SELECTION
        
        if (forgotPasswordMethodSelectionView == null) {
            forgotPasswordMethodSelectionView = ForgotPasswordMethodSelectionView(context).apply {
                setSecurityManager(<EMAIL>!!)
                setOnSecurityQuestionSelectedListener {
                    showSecurityQuestion()
                }
                setOnEmailRecoverySelectedListener {
                    showEmailRecovery()
                }
                setOnCancelListener {
                    onCancelListener?.invoke()
                    hideForgotPassword()
                }
                setRecoveryEmailAvailable(securityManager?.hasRecoveryEmail() == true)
            }
        }
        
        switchToView(forgotPasswordMethodSelectionView!!)
    }
    
    private fun showSecurityQuestion() {
        currentStep = ForgotPasswordStep.SECURITY_QUESTION
        
        if (forgotPasswordSecurityQuestionView == null) {
            forgotPasswordSecurityQuestionView = ForgotPasswordSecurityQuestionView(context).apply {
                setSecurityManager(<EMAIL>!!)
                setOnCorrectAnswerListener {
                    showNewPasswordSetup()
                }
                setOnRecoverViaEmailListener {
                    showEmailRecovery()
                }
                setOnBackListener {
                    goBack()
                }
            }
        }
        
        switchToView(forgotPasswordSecurityQuestionView!!)
    }
    
    private fun showEmailRecovery() {
        currentStep = ForgotPasswordStep.EMAIL_RECOVERY
        
        if (forgotPasswordEmailRecoveryView == null) {
            forgotPasswordEmailRecoveryView = ForgotPasswordEmailRecoveryView(context).apply {
                setSecurityManager(<EMAIL>!!)
                setOnRequestOtpListener { email ->
                    onRequestOtpListener?.invoke(email)
                    // Move to OTP verification after requesting OTP
                    postDelayed({
                        hideLoading()
                        showEmailOTPVerify()
                    }, 2000)
                }
                setOnAnswerSecurityQuestionListener {
                    showSecurityQuestion()
                }
                setOnBackListener {
                    goBack()
                }
            }
        }
        
        switchToView(forgotPasswordEmailRecoveryView!!)
    }
    
    private fun showEmailOTPVerify() {
        currentStep = ForgotPasswordStep.EMAIL_OTP_VERIFY
        
        if (forgotPasswordEmailOTPVerifyView == null) {
            forgotPasswordEmailOTPVerifyView = ForgotPasswordEmailOTPVerifyView(context).apply {
                setSecurityManager(<EMAIL>!!)
                setOnOTPVerifiedListener {
                    showNewPasswordSetup()
                }
                setOnRequestNewOtpListener { email ->
                    onRequestOtpListener?.invoke(email)
                }
                setOnBackListener {
                    goBack()
                }
                setOnOTPValidationListener { otp, onSuccess, onFailure ->
                    onOTPValidationListener?.invoke(otp, onSuccess, onFailure)
                }
            }
        }
        
        // Start countdowns when showing OTP view
        forgotPasswordEmailOTPVerifyView!!.startCountdowns()
        switchToView(forgotPasswordEmailOTPVerifyView!!)
    }
    
    private fun showNewPasswordSetup() {
        currentStep = ForgotPasswordStep.NEW_PASSWORD_SETUP
        
        if (zipperSetupNewPasswordView == null) {
            zipperSetupNewPasswordView = ZipperSetupNewPasswordView(context).apply {
                setSecurityManager(<EMAIL>!!)
                setOnPasswordResetSuccessListener {
                    onPasswordResetSuccessListener?.invoke()
                    hideForgotPassword()
                }
                setOnCancelListener {
                    onCancelListener?.invoke()
                    hideForgotPassword()
                }
                setOnBackListener {
                    goBack()
                }
                setOnSkipSetupListener {
                    onSkipSetupListener?.invoke()
                }
            }
        }
        
        switchToView(zipperSetupNewPasswordView!!)
    }
    
    private fun switchToView(view: View) {
        // Remove current view
        removeAllViews()
        
        // Add new view
        currentView = view
        addView(view, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
    }
    
    // Public methods for external control
    fun getCurrentStep(): ForgotPasswordStep {
        return currentStep
    }
    
    fun canGoBack(): Boolean {
        return currentStep != ForgotPasswordStep.METHOD_SELECTION
    }
    
    fun goBack() {
        when (currentStep) {
            ForgotPasswordStep.SECURITY_QUESTION -> showMethodSelection()
            ForgotPasswordStep.EMAIL_RECOVERY -> showMethodSelection()
            ForgotPasswordStep.EMAIL_OTP_VERIFY -> showEmailRecovery()
            ForgotPasswordStep.NEW_PASSWORD_SETUP -> {
                // Could go back to previous verification step
                // For now, just go to method selection
                showMethodSelection()
            }
            ForgotPasswordStep.METHOD_SELECTION -> {
                // Already at first step, nothing to do
            }
        }
    }
    
    // Handle OTP verification result from external source
    fun handleOTPVerificationResult(isSuccess: Boolean, errorMessage: String? = null) {
        if (currentStep == ForgotPasswordStep.EMAIL_OTP_VERIFY) {
            forgotPasswordEmailOTPVerifyView?.let { otpView ->
                otpView.hideLoading()
                if (isSuccess) {
                    showNewPasswordSetup()
                } else {
                    otpView.showError(errorMessage ?: context.getString(R.string.invalid_verification_code))
                    otpView.clearOTPInputs()
                }
            }
        }
    }
    
    // Handle email recovery result from external source
    fun handleEmailRecoveryResult(isSuccess: Boolean, errorMessage: String? = null) {
        if (currentStep == ForgotPasswordStep.EMAIL_RECOVERY) {
            forgotPasswordEmailRecoveryView?.let { recoveryView ->
                recoveryView.hideLoading()
                if (isSuccess) {
                    showEmailOTPVerify()
                } else {
                    recoveryView.showError(errorMessage ?: context.getString(R.string.invalid_email_address))
                }
            }
        }
    }
    
    enum class ForgotPasswordStep {
        METHOD_SELECTION,
        SECURITY_QUESTION,
        EMAIL_RECOVERY,
        EMAIL_OTP_VERIFY,
        NEW_PASSWORD_SETUP
    }
}