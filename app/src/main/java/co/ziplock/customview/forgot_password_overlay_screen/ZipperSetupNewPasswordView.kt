package co.ziplock.customview.forgot_password_overlay_screen

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import co.ziplock.R
import co.ziplock.databinding.OverlayNewPasswordSetupBinding
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.edittext.AsteriskPasswordTransformationMethod
import co.ziplock.util.hideKeyboard
import com.andrognito.patternlockview.PatternLockView
import com.andrognito.patternlockview.listener.PatternLockViewListener
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

class ZipperSetupNewPasswordView : FrameLayout {
    private lateinit var binding: OverlayNewPasswordSetupBinding
    private var securityManager: SecurityManager? = null

    // Current state
    private var currentTab = PasswordSetupTab.PIN
    private var currentStep = PasswordSetupStep.SETUP
    private var firstPin = ""
    private var firstPattern: List<PatternLockView.Dot>? = null
    private var isPinVisible = false
    private var isPatternDrawing = false
    private var isPatternListenerSetup = false

    // PIN input management for custom keypad
    private var currentPin = ""

    // Callbacks
    private var onPasswordResetSuccessListener: (() -> Unit)? = null
    private var onCancelListener: (() -> Unit)? = null
    private var onBackListener: (() -> Unit)? = null
    private var onSkipSetupListener: (() -> Unit)? = null

    // Skip setup overlay
    private var skipSetupOverlay: SkipSetupPasswordOverlay? = null

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    ) {
        init()
    }

    private fun init() {
        binding = OverlayNewPasswordSetupBinding.inflate(LayoutInflater.from(context), this, true)
        setupUI()
        setupSkipOverlay()
        setupPatternTouchHandling()
        showAdsPin()
        showAdsPattern()
    }

    fun setSecurityManager(securityManager: SecurityManager) {
        this.securityManager = securityManager
    }

    fun setOnPasswordResetSuccessListener(listener: () -> Unit) {
        onPasswordResetSuccessListener = listener
    }

    fun setOnCancelListener(listener: () -> Unit) {
        onCancelListener = listener
    }

    fun setOnBackListener(listener: () -> Unit) {
        onBackListener = listener
    }

    fun setOnSkipSetupListener(listener: () -> Unit) {
        onSkipSetupListener = listener
    }

    private fun showReloadAdsPin() {
        context.showLoadedNative(
            spaceNameConfig = "setnew-pin-reEnter",
            spaceName = "pincode-reEnter_native",
            layoutToAttachAds = binding.adViewGroupPin,
            layoutContainAds = binding.layoutAdsPin,
            onAdsClick = {},
        )
    }

    private fun showAdsPin() {
        context.showLoadedNative(
            spaceNameConfig = "setnew-pin-enter",
            spaceName = "pincode-enter_native",
            layoutToAttachAds = binding.adViewGroupPin,
            layoutContainAds = binding.layoutAdsPin,
            onAdsClick = {},
        )
        context.safePreloadAds(
            spaceNameConfig = "setnew-pin-reEnter",
            spaceNameAds = "pincode-reEnter_native",
        )
    }

    private fun showAdsPattern() {
        context.showLoadedNative(
            spaceNameConfig = "setnew-pattern-draw",
            spaceName = "pattern-draw_native",
            layoutToAttachAds = binding.adViewGroupPattern,
            layoutContainAds = binding.layoutAdsPattern,
            onAdsClick = {},
        )
        context.safePreloadAds(
            spaceNameConfig = "setnew-pattern-reDraw",
            spaceNameAds = "pattern-reDraw_native",
        )
    }

    private fun showReloadAdsPattern() {
        context.showLoadedNative(
            spaceNameConfig = "setnew-pattern-reDraw",
            spaceName = "pattern-reDraw_native",
            layoutToAttachAds = binding.adViewGroupPattern,
            layoutContainAds = binding.layoutAdsPattern,
            onAdsClick = {},
        )
    }

    private fun setupUI() {
        // Get UI elements
        val tabPinCode = binding.tabPinCode
        val tabPattern = binding.tabPattern
        val pinContent = binding.pinContent
        val patternContent = binding.patternContent

        // Setup PIN components
        val pinInputs = listOf(binding.etPin1, binding.etPin2, binding.etPin3, binding.etPin4)
        val tvSubtitle = binding.tvSubtitle
        val btnNext = binding.btnNext
        val btnCancel = binding.btnCancel
        val btnTogglePinVisibility = binding.btnTogglePinVisibility

        // Setup Pattern components
        val patternLockView = binding.patternLockView
        val tvPatternSubtitle = binding.tvPatternSubtitle
        val tvHelperText = binding.tvHelperText
        val btnPatternNext = binding.btnPatternNext
        val btnPatternCancel = binding.btnPatternCancel
        val btnBack = binding.btnBack

        // Tab switching logic
        tabPinCode.setOnClickListener {
            switchToTab(PasswordSetupTab.PIN)
        }

        tabPattern.setOnClickListener {
            switchToTab(PasswordSetupTab.PATTERN)
        }

        // PIN setup
        setupPinInput(pinInputs, btnNext)

        // Setup custom keypad
        setupCustomKeypad()

        btnTogglePinVisibility.setOnClickListener {
            togglePinVisibility(pinInputs, btnTogglePinVisibility)
        }

        btnNext.setOnClickListener {
            handlePinNextClick(pinInputs, tvSubtitle, btnNext)
        }

        btnCancel.setOnClickListener {
            showSkipSetupOverlay()
        }

        // Pattern setup
        setupPatternInput(patternLockView, tvHelperText, btnPatternNext)

        btnPatternNext.setOnClickListener {
            handlePatternNextClick(tvPatternSubtitle, tvHelperText, btnPatternNext)
        }

        btnPatternCancel.setOnClickListener {
            showSkipSetupOverlay()
        }

        btnBack.setOnClickListener {
            onBackListener?.invoke()
        }

        // Initialize with PIN tab selected
        switchToTab(PasswordSetupTab.PIN)
    }

    private fun switchToTab(tab: PasswordSetupTab) {
        currentTab = tab
        currentStep = PasswordSetupStep.SETUP

        when (tab) {
            PasswordSetupTab.PIN -> {
                // Update tab appearance
                binding.tabPinCode.setBackgroundResource(R.drawable.tab_selected_background)
                binding.tabPinCode.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.blue_007aff,
                    ),
                )
                binding.tabPattern.setBackgroundResource(R.drawable.tab_unselected_background)
                binding.tabPattern.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.grey_5b5b5b,
                    ),
                )

                // Show PIN content
                binding.pinContent.visibility = VISIBLE
                binding.patternContent.visibility = GONE

                // Reset PIN state
                resetPinToInitialState()
            }

            PasswordSetupTab.PATTERN -> {
                // Update tab appearance
                binding.tabPattern.setBackgroundResource(R.drawable.tab_selected_background)
                binding.tabPattern.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.blue_007aff,
                    ),
                )
                binding.tabPinCode.setBackgroundResource(R.drawable.tab_unselected_background)
                binding.tabPinCode.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.grey_5b5b5b,
                    ),
                )

                // Show Pattern content
                binding.patternContent.visibility = VISIBLE
                binding.pinContent.visibility = GONE

                // Reset Pattern state with delay to ensure UI is ready
                postDelayed({
                    resetPatternState()
                    setupPatternTouchHandling()
                    // Force refresh PatternLockView
                    binding.patternLockView.invalidate()
                }, 100)
            }
        }
    }

    private fun resetPinToInitialState() {
        firstPin = ""
        currentPin = ""
        clearPinInputs()
        binding.tvSubtitle.text = context.getString(R.string.set_your_new_pin_code)
        binding.tvSubtitle.setTextColor(ContextCompat.getColor(context, R.color.black_35496d))
        binding.btnNext.isEnabled = false
        binding.btnNext.backgroundTintList =
            ContextCompat.getColorStateList(context, R.color.blue_2fafff)
        binding.btnNext.setTextColor(ContextCompat.getColor(context, R.color.white))
        binding.btnNext.text = context.getString(R.string.next)
        binding.btnNext.alpha = 0.5f
    }

    private fun resetPatternState() {
        firstPattern = null
        currentStep = PasswordSetupStep.SETUP
        binding.tvPatternSubtitle.text = context.getString(R.string.set_your_new_pattern)
        binding.tvPatternSubtitle.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.black_35496d,
            ),
        )
        binding.tvHelperText.text = context.getString(R.string.connect_at_least_4_dots)
        binding.tvHelperText.setTextColor(ContextCompat.getColor(context, R.color.grey_5b5b5b))
        binding.btnPatternNext.isEnabled = false
        binding.btnPatternNext.backgroundTintList =
            ContextCompat.getColorStateList(context, R.color.blue_2fafff)
        binding.btnPatternNext.setTextColor(ContextCompat.getColor(context, R.color.white))
        binding.btnPatternNext.text = context.getString(R.string.next)
        binding.btnPatternNext.alpha = 0.5f

        // Clear pattern with a small delay to ensure UI is ready
        postDelayed({
            binding.patternLockView.clearPattern()
            // Debug PatternLockView state
        }, 100)

        hideKeyboard()
    }

    private fun setupPinInput(
        pinInputs: List<EditText>,
        btnNext: TextView,
    ) {
        // Initialize PIN inputs with transformation method and disable keyboard
        pinInputs.forEach { editText ->
            editText.transformationMethod = AsteriskPasswordTransformationMethod()
            editText.isEnabled = true
            editText.isFocusable = false // Prevent manual focus/input since we use custom keypad
            editText.isFocusableInTouchMode = false
        }

        clearPinInputs()
    }

    private fun setupPatternInput(
        patternLockView: PatternLockView,
        tvHelperText: TextView,
        btnPatternNext: TextView,
    ) {
        // Only setup listener once
        if (isPatternListenerSetup) return
        isPatternListenerSetup = true

        // Clear any existing listeners first
        try {
            patternLockView.removeAllPatternLockListeners()
        } catch (e: Exception) {
            Timber.tag("testPattern").e(e, "Error clearing pattern listeners")
        }

        patternLockView.addPatternLockListener(
            object : PatternLockViewListener {
                override fun onStarted() {
                    Timber.tag("testPattern").i("onStarted")
                    // Set drawing state
                    isPatternDrawing = true
                    // Reset helper text when user starts drawing
                    tvHelperText.text = context.getString(R.string.connect_at_least_4_dots)
                    tvHelperText.setTextColor(ContextCompat.getColor(context, R.color.grey_5b5b5b))
                }

                override fun onProgress(progressPattern: MutableList<PatternLockView.Dot>?) {
                    Timber.tag("testPattern").i("onProgress: $progressPattern")
                }

                override fun onComplete(pattern: MutableList<PatternLockView.Dot>?) {
                    Timber.tag("testPattern").i("onComplete: $pattern")
                    // Clear drawing state
                    isPatternDrawing = false

                    pattern?.let { dots ->
                        if (dots.size >= 4) {
                            btnPatternNext.isEnabled = true
                            btnPatternNext.alpha = 1f
                            tvHelperText.text = context.getString(R.string.pattern_recorded)
                            tvHelperText.setTextColor(
                                ContextCompat.getColor(
                                    context,
                                    R.color.green_4caf50,
                                ),
                            )
                        } else {
                            tvHelperText.text = context.getString(R.string.connect_at_least_4_dots)
                            tvHelperText.setTextColor(
                                ContextCompat.getColor(
                                    context,
                                    R.color.red_f44336,
                                ),
                            )
                            // Delay clearing pattern to give user time to see the feedback
                            postDelayed({
                                if (!isPatternDrawing && patternLockView.pattern?.size ?: 0 < 4) {
                                    Timber
                                        .tag("testPattern")
                                        .i("Auto-clearing pattern due to insufficient dots")
                                    patternLockView.clearPattern()
                                }
                            }, 1500)
                        }
                    }
                }

                override fun onCleared() {
                    Timber.tag("testPattern").i("onCleared")
                    // Clear drawing state
                    isPatternDrawing = false
                    btnPatternNext.isEnabled = false
                    btnPatternNext.alpha = 0.5f
                    tvHelperText.text = context.getString(R.string.connect_at_least_4_dots)
                    tvHelperText.setTextColor(ContextCompat.getColor(context, R.color.grey_5b5b5b))
                }
            },
        )
    }

    private fun setupCustomKeypad() {
        // Get all number buttons using binding for included layout
        val btn0 = binding.root.findViewById<Button>(R.id.btn_0)
        val btn1 = binding.root.findViewById<Button>(R.id.btn_1)
        val btn2 = binding.root.findViewById<Button>(R.id.btn_2)
        val btn3 = binding.root.findViewById<Button>(R.id.btn_3)
        val btn4 = binding.root.findViewById<Button>(R.id.btn_4)
        val btn5 = binding.root.findViewById<Button>(R.id.btn_5)
        val btn6 = binding.root.findViewById<Button>(R.id.btn_6)
        val btn7 = binding.root.findViewById<Button>(R.id.btn_7)
        val btn8 = binding.root.findViewById<Button>(R.id.btn_8)
        val btn9 = binding.root.findViewById<Button>(R.id.btn_9)
        val btnDelete = binding.root.findViewById<ImageView>(R.id.btn_delete)

        // Setup click listeners for number buttons
        val numberButtons =
            listOf(
                btn0 to "0",
                btn1 to "1",
                btn2 to "2",
                btn3 to "3",
                btn4 to "4",
                btn5 to "5",
                btn6 to "6",
                btn7 to "7",
                btn8 to "8",
                btn9 to "9",
            )

        numberButtons.forEach { (button, number) ->
            button.setOnClickListener {
                onNumberPressed(number)
            }
        }

        // Setup delete button
        btnDelete.setOnClickListener {
            onDeletePressed()
        }
    }

    private fun onNumberPressed(number: String) {
        // Check if we can add more digits
        if (currentPin.length < 4) {
            currentPin += number
            updatePinDisplay(currentPin)

            // Check if PIN is complete (4 digits)
            if (currentPin.length == 4) {
                binding.btnNext.isEnabled = true
                binding.btnNext.alpha = 1f
            }
        }
    }

    private fun onDeletePressed() {
        if (currentPin.isNotEmpty()) {
            currentPin = currentPin.dropLast(1)
            updatePinDisplay(currentPin)

            // Update button state
            binding.btnNext.isEnabled = currentPin.length == 4
            binding.btnNext.alpha = if (currentPin.length == 4) 1f else 0.5f
        }
    }

    private fun updatePinDisplay(pin: String) {
        val pinInputs = listOf(binding.etPin1, binding.etPin2, binding.etPin3, binding.etPin4)

        // Clear all inputs first
        pinInputs.forEach { it.setText("") }

        // Fill inputs with current pin
        for (i in pin.indices) {
            if (i < pinInputs.size) {
                val displayChar = pin[i].toString()
                pinInputs[i].setText(displayChar)
            }
        }

        // Apply visibility transformation
        pinInputs.forEach {
            it.transformationMethod =
                if (isPinVisible) null else AsteriskPasswordTransformationMethod()
        }
    }

    private fun handlePinNextClick(
        pinInputs: List<EditText>,
        tvSubtitle: TextView,
        btnNext: TextView,
    ) {
        val pinToProcess = currentPin

        when (currentStep) {
            PasswordSetupStep.SETUP -> {
                // First PIN entry
                firstPin = pinToProcess
                currentStep = PasswordSetupStep.CONFIRM

                // Update UI for confirmation
                tvSubtitle.text = context.getString(R.string.confirm_your_new_pin_code)
                clearPinInputs()
                btnNext.text = context.getString(R.string.confirm)
                btnNext.isEnabled = false
                btnNext.alpha = 0.5f
                showReloadAdsPin()
            }

            PasswordSetupStep.CONFIRM -> {
                // PIN confirmation
                if (pinToProcess == firstPin) {
                    // PINs match, save and complete
                    savePinAndComplete(pinToProcess)
                } else {
                    // PINs don't match, show error and clear inputs
                    tvSubtitle.text = context.getString(R.string.pins_do_not_match_try_again)
                    tvSubtitle.setTextColor(ContextCompat.getColor(context, R.color.red_f44336))

                    postDelayed({
                        clearPinError()
                    }, 2000)
                }
            }
        }
    }

    private fun handlePatternNextClick(
        tvPatternSubtitle: TextView,
        tvHelperText: TextView,
        btnPatternNext: TextView,
    ) {
        val currentPattern = binding.patternLockView.pattern

        when (currentStep) {
            PasswordSetupStep.SETUP -> {
                // First pattern entry
                firstPattern = currentPattern?.toList()
                currentStep = PasswordSetupStep.CONFIRM

                // Update UI for confirmation
                tvPatternSubtitle.text = context.getString(R.string.confirm_your_new_pattern)
                tvHelperText.text = context.getString(R.string.draw_pattern_again_to_confirm)
                tvHelperText.setTextColor(ContextCompat.getColor(context, R.color.grey_5b5b5b))
                binding.patternLockView.clearPattern()
                btnPatternNext.text = context.getString(R.string.confirm)
                btnPatternNext.isEnabled = false
                btnPatternNext.alpha = 0.5f
                showReloadAdsPattern()
            }

            PasswordSetupStep.CONFIRM -> {
                // Pattern confirmation
                if (patternsMatch(currentPattern, firstPattern)) {
                    // Patterns match, save and complete
                    savePatternAndComplete(currentPattern)
                } else {
                    // Patterns don't match, show error and clear pattern
                    tvPatternSubtitle.text =
                        context.getString(R.string.patterns_do_not_match_try_again)
                    tvPatternSubtitle.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.red_f44336,
                        ),
                    )
                    binding.patternLockView.setViewMode(PatternLockView.PatternViewMode.WRONG)
                    postDelayed({
                        clearPatternError()
                    }, 2000)
                }
            }
        }
    }

    private fun patternsMatch(
        pattern1: List<PatternLockView.Dot>?,
        pattern2: List<PatternLockView.Dot>?,
    ): Boolean {
        if (pattern1 == null || pattern2 == null) return false
        if (pattern1.size != pattern2.size) return false

        return pattern1.zip(pattern2).all { (dot1, dot2) ->
            dot1.row == dot2.row && dot1.column == dot2.column
        }
    }

    private fun savePinAndComplete(pin: String) {
        securityManager?.let { security ->
            security.setupPinCode(pin)
            onPasswordResetSuccessListener?.invoke()
        }
    }

    private fun savePatternAndComplete(pattern: List<PatternLockView.Dot>?) {
        pattern?.let { dots ->
            securityManager?.let { security ->
                val pattern = dots.map { it.id }
                security.setupPatternCode(pattern)
                onPasswordResetSuccessListener?.invoke()
            }
        }
    }

    private fun clearPinInputs() {
        val pinInputs = listOf(binding.etPin1, binding.etPin2, binding.etPin3, binding.etPin4)
        pinInputs.forEach { it.setText("") }
        currentPin = ""
    }

    private fun clearPinError() {
        binding.tvSubtitle.text = context.getString(R.string.confirm_your_new_pin_code)
        binding.tvSubtitle.setTextColor(ContextCompat.getColor(context, R.color.black_35496d))
        clearPinInputs()
        binding.btnNext.isEnabled = false
        binding.btnNext.alpha = 0.5f
    }

    private fun clearPatternError() {
        binding.tvPatternSubtitle.text = context.getString(R.string.confirm_your_new_pattern)
        binding.tvPatternSubtitle.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.black_35496d,
            ),
        )
        binding.tvHelperText.text = context.getString(R.string.draw_pattern_again_to_confirm)
        binding.tvHelperText.setTextColor(ContextCompat.getColor(context, R.color.grey_5b5b5b))
        binding.btnPatternNext.isEnabled = false
        binding.btnPatternNext.alpha = 0.5f

        // Clear pattern with a small delay to ensure UI is ready
        postDelayed({
            binding.patternLockView.clearPattern()
        }, 100)
    }

    private fun togglePinVisibility(
        pinInputs: List<EditText>,
        btnToggle: ImageView,
    ) {
        isPinVisible = !isPinVisible

        // Update display with current visibility setting
        updatePinDisplay(currentPin)

        btnToggle.setImageResource(
            if (isPinVisible) R.drawable.ic_visibility else R.drawable.ic_visibility_off,
        )
    }

    private fun setupSkipOverlay() {
        skipSetupOverlay = SkipSetupPasswordOverlay(context)
        skipSetupOverlay?.setOnSkipConfirmedListener {
            onSkipSetupListener?.invoke()
        }
        skipSetupOverlay?.setOnCancelListener {
            // Do nothing, overlay will hide itself
        }

        // Add overlay to parent view
        addView(
            skipSetupOverlay,
            LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT),
        )
    }

    private fun showSkipSetupOverlay() {
        skipSetupOverlay?.show()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupPatternTouchHandling() {
        // Ensure PatternLockView can receive touch events properly
        binding.patternLockView.isEnabled = true
        binding.patternLockView.isClickable = true
        binding.patternLockView.isFocusable = true
        binding.patternLockView.isFocusableInTouchMode = true

        // Prevent parent ScrollView from intercepting touch events when user is drawing pattern
        binding.patternLockView.setOnTouchListener { view, event ->
            Timber.tag("testPattern").i("Touch event: ${event.action}")

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // Request parent to not intercept touch events
                    view.parent?.requestDisallowInterceptTouchEvent(true)
                    // Also request for the entire hierarchy
                    var parent = view.parent
                    while (parent != null) {
                        parent.requestDisallowInterceptTouchEvent(true)
                        parent = parent.parent
                    }
                }

                MotionEvent.ACTION_UP,
                MotionEvent.ACTION_CANCEL,
                -> {
                    // Allow parent to intercept touch events again
                    view.parent?.requestDisallowInterceptTouchEvent(false)
                }
            }
            false
        }
    }

    enum class PasswordSetupTab {
        PIN,
        PATTERN,
    }

    enum class PasswordSetupStep {
        SETUP,
        CONFIRM,
    }
}
