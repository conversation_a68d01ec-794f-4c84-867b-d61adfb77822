package co.ziplock.framework

import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import co.ziplock.BuildConfig
import co.ziplock.R
import co.ziplock.databinding.ActivityMainBinding
import co.ziplock.framework.presentation.common.BaseActivity
import co.ziplock.framework.presentation.common.LoadingDialog
import co.ziplock.framework.presentation.common.lifecycleCallback.FragmentLifecycleCallbacksImpl
import co.ziplock.util.Constant
import com.example.libiap.IAPConnector
import com.example.libiap.SubscribeInterface
import com.example.libiap.model.ProductModel
import dagger.hilt.android.AndroidEntryPoint
import pion.datlt.libads.AdsController
import pion.datlt.libads.utils.AdsConstant

@AndroidEntryPoint
class MainActivity : BaseActivity<ActivityMainBinding>() {
    override fun inflateViewBinding(inflater: LayoutInflater): ActivityMainBinding = ActivityMainBinding.inflate(inflater)

    override fun configBeforeSettingBinding() {
        super.configBeforeSettingBinding()
        supportFragmentManager.registerFragmentLifecycleCallbacks(
            FragmentLifecycleCallbacksImpl(),
            true,
        )
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
        )
    }

    private fun getNavHost(): NavController {
        val navHostFragment =
            supportFragmentManager.findFragmentById(binding.fragmentContainerMain.id) as NavHostFragment
        return navHostFragment.navController
    }

    private fun initAds() {
        AdsController.init(
            activity = this,
            isDebug = BuildConfig.DEBUG,
            listAppId =
                arrayListOf(
                    getString(R.string.admob_application_id),
                ),
            packageName = packageName,
            navController = getNavHost(),
        )
    }

    private fun initPurchaseIap() {
        application?.let { IAPConnector.initIap(it, "iap_id.json", BuildConfig.DEBUG) }
        IAPConnector.addIAPListener(
            object : SubscribeInterface {
                override fun subscribeSuccess(productModel: ProductModel) {
                    // set lai cac bien check
                    Constant.isPremium = true
                    AdsConstant.isPremium = true
                    prefUtil.isPremium = true
                    Handler(Looper.getMainLooper()).postDelayed({
                        val intent =
                            baseContext.packageManager.getLaunchIntentForPackage(
                                baseContext.packageName,
                            )
                        intent!!.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                        startActivity(intent)
                    }, 500)
                }

                override fun subscribeError(error: String) {
                    // No action needed for error
                }
            },
        )
    }

    override fun initView() {
        initAds()
        initPurchaseIap()
    }

    fun showLoading() {
        LoadingDialog.getInstance().show(supportFragmentManager)
    }

    fun hiddenLoading() {
        LoadingDialog.getInstance().dismiss()
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}
