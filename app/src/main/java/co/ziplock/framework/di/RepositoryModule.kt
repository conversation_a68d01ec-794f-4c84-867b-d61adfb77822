package co.ziplock.framework.di

import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import co.ziplock.framework.network.ApiInterface
import co.ziplock.repository.ApiRepository
import co.ziplock.repository.FileRepository
import co.ziplock.repository.RemoteConfigRepository
import co.ziplock.repository.impl.ApiRepositoryImpl
import co.ziplock.repository.impl.FileRepositoryImpl
import co.ziplock.repository.impl.RemoteConfigRepositoryImpl
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindFileRepository(fileRepositoryImpl: FileRepositoryImpl): FileRepository

    @Binds
    @Singleton
    abstract fun provideRemoteConfigRepository(remoteConfigRepositoryImpl: RemoteConfigRepositoryImpl): RemoteConfigRepository

    @Binds
    @Singleton
    abstract fun provideApiRepository(apiRepositoryImpl: ApiRepositoryImpl): ApiRepository

}