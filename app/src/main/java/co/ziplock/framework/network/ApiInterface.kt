package co.ziplock.framework.network

import co.ziplock.framework.network.model.ApiObjectResponseData
import co.ziplock.framework.network.model.AppCategoryData
import co.ziplock.framework.network.model.template.TemplateModelData
import co.ziplock.util.Constant
import retrofit2.http.GET
import retrofit2.http.Query

interface ApiInterface {
    @GET("api/v5.0/public/categories?app_id=${Constant.APP_CMS_ID}")
    suspend fun getAppCategory(): ApiObjectResponseData<List<AppCategoryData>>

    @GET("api/v5.0/public/items/get-all?region_code=%7Bregion_code%7D")
    suspend fun <T> getAllTemplate(@Query("category_id") categoryId: String): ApiObjectResponseData<List<TemplateModelData<T>>>

    @GET("/stores/api/v5.0/public/categories")
    suspend fun getAllSubCategoriesByParentId(@Query("parent_id") parentId: String): ApiObjectResponseData<List<AppCategoryData>>
}