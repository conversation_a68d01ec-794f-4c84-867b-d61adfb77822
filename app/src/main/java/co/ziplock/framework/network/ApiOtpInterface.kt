package co.ziplock.framework.network

import co.ziplock.framework.network.model.ApiObjectResponse
import co.ziplock.framework.network.model.OtpRequestBody
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface ApiOtpInterface {
    @POST("sendEmail")
    suspend fun sendOtp(
        @Header("x-api-key") apiKey: String,
        @Body request: OtpRequestBody,
    ): ApiObjectResponse<Int>
}
