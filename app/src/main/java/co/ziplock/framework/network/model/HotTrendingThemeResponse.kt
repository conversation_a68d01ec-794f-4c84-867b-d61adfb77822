package co.ziplock.framework.network.model

import com.google.gson.annotations.SerializedName

data class HotTrendingThemeResponse(
    @SerializedName("id")
    val id: String?,

    @SerializedName("is_pro")
    val isPro: Boolean?,

    @SerializedName("id_row")
    val idRow: String?,
    
    @SerializedName("id_background")
    val idBackground: String?,

    @SerializedName("id_sound")
    val idSound: String?,

    @SerializedName("id_wallpaper")
    val idWallpaper: String?,

    @SerializedName("id_zipper")
    val idZipper: String?,

    @SerializedName("preview_thumbnail")
    val previewThumbnail: String?,

    @SerializedName("id_category")
    val idCategory: String?
)
