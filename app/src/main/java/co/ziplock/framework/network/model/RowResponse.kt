package co.ziplock.framework.network.model

import android.os.Parcelable
import co.ziplock.framework.presentation.model.AdsItem
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class RowResponse(
    @SerializedName("id")
    val id: String?,
    @SerializedName("is_pro")
    val isPro: Boolean?,
    @SerializedName("image_row_left")
    val imageRowLeft: String?,
    @SerializedName("image_row_right")
    val imageRowRight: String?,
    @SerializedName("preview_thumbnail")
    val previewThumbnail: String?,
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("percent_tooth_spacing")
    val percentToothSpacing: Float = 1f,
    @SerializedName("draw_height")
    val drawHeight: Int = 60,
    @SerializedName("tooth_center_offset")
    val toothCenterOffset: Float = 0.186f,
    val adsItem: AdsItem? = null,
) : Parcelable
