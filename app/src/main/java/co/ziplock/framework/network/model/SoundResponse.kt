package co.ziplock.framework.network.model

import android.media.MediaMetadataRetriever
import android.os.Parcelable
import android.util.Log
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.util.Constant
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class SoundResponse(
    @SerializedName("id")
    val id: String = "",
    @SerializedName("name")
    val name: String = "",
    @SerializedName("file")
    val fileUrl: String = "",
    @SerializedName("preview_thumbnail")
    val thumbnailUrl: String = "",
    val adsItem: AdsItem? = null,
) : Parcelable {
    @IgnoredOnParcel
    val duration: String
        get() =
            extractMetadata { retriever ->
                val durationMs =
                    retriever
                        .extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                        ?.toLongOrNull()
                if (durationMs != null) {
                    formatDuration(durationMs)
                } else {
                    Constant.DEFAULT_DISPLAY_TIME
                }
            } ?: Constant.DEFAULT_DISPLAY_TIME

    private fun <T> extractMetadata(extractor: (MediaMetadataRetriever) -> T?): T? {
        if (fileUrl.isEmpty()) return null

        val retriever = MediaMetadataRetriever()
        return try {
            retriever.setDataSource(fileUrl)
            extractor(retriever)
        } catch (e: Exception) {
            Log.e("SoundResponse", "Error extracting metadata from $fileUrl", e)
            null
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.e("SoundResponse", "Error releasing MediaMetadataRetriever", e)
            }
        }
    }

    private fun formatDuration(durationMs: Long): String {
        val seconds = (durationMs / 1000) % 60
        val minutes = (durationMs / (1000 * 60)) % 60
        val hours = (durationMs / (1000 * 60 * 60))

        return if (hours > 0) {
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }

    companion object {
        fun createNoSound(): SoundResponse =
            SoundResponse(
                id = Constant.NO_SOUND_ID,
                name = "No Sound", // Default name, will be replaced with localized version in adapters
                fileUrl = Constant.NO_SOUND_URL,
                thumbnailUrl = "",
            )
    }
}
