package co.ziplock.framework.network.model

import com.google.gson.annotations.SerializedName

data class WallpaperCategoryResponse(
    @SerializedName(value = "id_wallpaper")
    val wallpaperId: String?,
    @SerializedName(value = "is_onboarding")
    val isOnboarding: Boolean?,
    @SerializedName(value = "name")
    val name: String?,
    @SerializedName(value = "thumbnail")
    val thumbnailUrl: String?
)
