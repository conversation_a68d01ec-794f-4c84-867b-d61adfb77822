package co.ziplock.framework.network.model

import android.os.Parcelable
import co.ziplock.framework.presentation.model.AdsItem
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ZipperResponse(
    @SerializedName("id")
    val id: String?,
    @SerializedName(value = "file")
    val fileUrl: String?,
    @SerializedName("draw_height")
    val drawHeight: Int = 60,
    @SerializedName("is_pro")
    val isPro: Boolean?,
    val adsItem: AdsItem? = null,
) : Parcelable
