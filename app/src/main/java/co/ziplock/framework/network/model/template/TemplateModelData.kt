package co.ziplock.framework.network.model.template

import com.google.gson.annotations.SerializedName

data class TemplateModelData<T>(
    @SerializedName("id")
    val id: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("status")
    val status: <PERSON><PERSON>an,
    @SerializedName("is_pro")
    val isPro: Boolean? = null,
    @SerializedName("custom_fields")
    val customField: T
)


