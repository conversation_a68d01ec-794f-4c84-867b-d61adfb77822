package co.ziplock.framework.otpworkmanager

import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.Worker
import androidx.work.WorkerParameters
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import co.ziplock.util.PrefUtil

@HiltWorker
class ResetLimitOtpWorker @AssistedInject constructor(
    @Assisted private val prefUtil: PrefUtil,
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters
) : Worker(context, workerParams) {

    override fun doWork(): Result {
        prefUtil.countRequestOtp = 0
        return Result.success()
    }

}