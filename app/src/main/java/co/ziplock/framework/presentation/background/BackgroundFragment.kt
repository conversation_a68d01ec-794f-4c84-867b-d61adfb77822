package co.ziplock.framework.presentation.background

import android.view.View
import co.ziplock.databinding.FragmentBackgroundBinding
import co.ziplock.framework.presentation.background.adapter.BackgroundAdapter
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.PermissionManager
import co.ziplock.util.BundleKey
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BackgroundFragment :
    BaseFragment<FragmentBackgroundBinding, BackgroundViewModel>(
        FragmentBackgroundBinding::inflate,
        BackgroundViewModel::class.java,
    ) {
    lateinit var backgroundAdapter: BackgroundAdapter

    val permissionManager by lazy {
        PermissionManager(requireContext())
    }

    val isReEditFlow by lazy {
        arguments?.getBoolean(BundleKey.KEY_FROM_RE_EDIT_FLOW) ?: false
    }

    var isTabsInitialized = false

    override fun init(view: View) {
        setupRecyclerView()
        setupTabLayout()
        setupSystemBackEvent()
        setupBackButtonClickListener()
        setupFloatingActionButton()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeBackgroundCategories()
        observeBackgroundData()
        observeSelectedCategory()
        observeLoadingState()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isTabsInitialized = false
    }

    companion object {
        const val TAG = "BackgroundFragment"

        fun newInstance() = BackgroundFragment()
    }
}
