package co.ziplock.framework.presentation.background

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.presentation.background.adapter.BackgroundAdapter
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.dialog.FilesPermissionRequiredDialogFragment
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.framework.presentation.model.background.BackgroundItem
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.disableTooltipsForTabs
import co.ziplock.util.setPreventDoubleClick
import com.google.android.material.tabs.TabLayout
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun BackgroundFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun BackgroundFragment.setupSystemBackEvent() {
    onSystemBackEvent {
        backEvent()
    }
}

fun BackgroundFragment.backEvent() {
    findNavController().navigateUp()
}

fun BackgroundFragment.navigateToEditLayout(background: BackgroundResponse) {
    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.BACKGROUND_DATA, background)
        }
    safeNavInter(
        R.id.backgroundFragment,
        R.id.action_backgroundFragment_to_editLayoutFragment,
        bundle,
    )
}

fun BackgroundFragment.navigateToEditLayoutWithLocalBackground(localBackground: LocalImageData) {
    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.LOCAL_BACKGROUND_DATA, localBackground)
        }
    safeNavInter(
        R.id.backgroundFragment,
        R.id.action_backgroundFragment_to_editLayoutFragment,
        bundle,
    )
}

fun BackgroundFragment.navigateToPickPhoto() {
    showLoadedInter(
        spaceNameConfig = "listBG-gallery",
        spaceName = "list-1ID_interstitial",
        destinationToShowAds = R.id.backgroundFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            val bundle =
                Bundle().apply {
                    putString(BundleKey.KEY_SAVE_CONTEXT, Constant.SAVE_CONTEXT_BACKGROUND)
                }
            safeNavInter(
                R.id.backgroundFragment,
                R.id.action_backgroundFragment_to_pickPhotoFragment,
                bundle,
            )
        },
        onCloseAds = {},
    )
}

fun BackgroundFragment.setupFloatingActionButton() {
    binding.fabFromGallery.setPreventDoubleClick {
        val isFilesPermissionGranted = permissionManager.isMediaPermissionGranted()
        if (isFilesPermissionGranted) {
            navigateToPickPhoto()
        } else {
            FilesPermissionRequiredDialogFragment
                .Builder()
                .setOnClickAllowButton {
                    permissionManager.requestPermission(
                        fragment = this,
                        permission = permissionManager.getMediaPermission(),
                        onAllPermissionGranted = {
                            // navigateToPickPhoto()
                        },
                    )
                }.build()
                .show(parentFragmentManager, BackgroundFragment.TAG)
        }
    }
}

fun BackgroundFragment.showInterChooseItem(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "listBG-choosect",
        spaceName = "listBG-choosect_interstitial",
        destinationToShowAds = R.id.backgroundFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = action,
        onCloseAds = {},
    )
}

fun BackgroundFragment.setupRecyclerView() {
    backgroundAdapter =
        BackgroundAdapter(
            onRemoteBackgroundClick = { background ->
                if (isReEditFlow) {
                    // If coming from EditLayoutFragment, set data to CommonViewModel and go back
                    showInterChooseItem {
                        commonViewModel.setEditLayoutBackground(background)
                        safeNavigateUp(R.id.backgroundFragment)
                    }
                } else {
                    // Normal flow - set data to CommonViewModel and navigate to EditLayout
                    showInterChooseItem {
                        commonViewModel.setEditLayoutBackground(background)
                        navigateToEditLayout(background)
                    }
                }
            },
            onLocalBackgroundClick = { localBackground ->
                if (isReEditFlow) {
                    // If coming from EditLayoutFragment, set data to CommonViewModel and go back
                    showInterChooseItem {
                        commonViewModel.setEditLayoutLocalBackground(localBackground)
                        safeNavigateUp(R.id.backgroundFragment)
                    }
                } else {
                    // Normal flow - set data to CommonViewModel and navigate to EditLayout
                    showInterChooseItem {
                        commonViewModel.setEditLayoutLocalBackground(localBackground)
                        navigateToEditLayoutWithLocalBackground(localBackground)
                    }
                }
            },
        )
    backgroundAdapter.setFragment(this)
    val gridLayoutManager = GridLayoutManager(requireContext(), 3)
    gridLayoutManager.spanSizeLookup =
        object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                val item = backgroundAdapter.currentList.getOrNull(position)
                return if (item is BackgroundItem.AdsBackgroundItem) 3 else 1
            }
        }

    binding.rvBackgrounds.apply {
        layoutManager = gridLayoutManager
        adapter = backgroundAdapter
    }
}

fun BackgroundFragment.setupTabLayout() {
    binding.tabLayout.disableTooltipsForTabs()

    binding.tabLayout.addOnTabSelectedListener(
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                val tabText = tab.text.toString()
                val category =
                    when (tabText) {
                        getString(R.string.all) -> Constant.ALL
                        getString(R.string.local_files) -> Constant.LOCAL_FILES
                        else -> tabText
                    }
                if (!isTabsInitialized) {
                    isTabsInitialized = true
                    commonViewModel.selectBackgroundCategory(category)
                } else {
                    showLoadedInter(
                        spaceNameConfig = "listBG-choosecate",
                        spaceName = "list-1ID_interstitial",
                        destinationToShowAds = R.id.backgroundFragment,
                        isShowLoadingView = true,
                        isScreenType = false,
                        navOrBack = {
                            commonViewModel.selectBackgroundCategory(category)
                        },
                        onCloseAds = {},
                    )
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {}

            override fun onTabReselected(tab: TabLayout.Tab) {}
        },
    )
}

fun BackgroundFragment.observeBackgroundCategories() {
    commonViewModel.backgroundUiState
        .map { it.categories }
        .distinctUntilChanged()
        .onEach { categories ->
            updateTabLayout(categories)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun BackgroundFragment.observeBackgroundData() {
    commonViewModel.backgroundUiState
        .map {
            Triple(
                it.currentCategoryBackgrounds,
                it.currentLocalBackgrounds,
                it.selectedCategory,
            )
        }.onEach { (remoteBackgrounds, localBackgrounds, selectedCategory) ->
            Timber.d("observeBackgroundData: remote=${remoteBackgrounds.size}, local=${localBackgrounds.size}, category=$selectedCategory")
        }.distinctUntilChanged()
        .onEach { (remoteBackgrounds, localBackgrounds, selectedCategory) ->
            selectedCategory?.let { category ->
                // Submit backgrounds to the optimized adapter
                backgroundAdapter.submitList(
                    filterListAds(
                        createListFromBackground(
                            remoteBackgrounds,
                            localBackgrounds,
                            category,
                        ),
                    ),
                )

                // Show/hide empty state based on category
                val isEmpty =
                    when (category) {
                        Constant.ALL -> remoteBackgrounds.isEmpty() && localBackgrounds.isEmpty()
                        Constant.LOCAL_FILES -> localBackgrounds.isEmpty()
                        else -> remoteBackgrounds.isEmpty()
                    }
                binding.emptyState.isVisible =
                    isEmpty &&
                    !commonViewModel.backgroundUiState.value.isLoading
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun BackgroundFragment.observeSelectedCategory() {
    commonViewModel.backgroundUiState
        .map { it.selectedCategory }
        .distinctUntilChanged()
        .onEach { selectedCategory ->
            selectedCategory?.let { category ->
                selectTabByCategory(category)
                // Refresh local backgrounds when switching to All or Local Files tab
                if (category == Constant.ALL || category == Constant.LOCAL_FILES) {
                    commonViewModel.refreshLocalBackgrounds()
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun createListFromBackground(
    remoteBackgrounds: List<BackgroundResponse>,
    localBackgrounds: List<LocalImageData>,
    category: String,
): List<BackgroundItem> {
    val items = mutableListOf<BackgroundItem>()

    when (category) {
        Constant.ALL -> {
            // Show both remote and local backgrounds
            items.addAll(remoteBackgrounds.map { BackgroundItem.RemoteBackground(it) })
            items.addAll(localBackgrounds.map { BackgroundItem.LocalBackgroundItem(it) })
        }

        Constant.LOCAL_FILES -> {
            // Show only local backgrounds
            items.addAll(localBackgrounds.map { BackgroundItem.LocalBackgroundItem(it) })
        }

        else -> {
            // Show only remote backgrounds for specific categories
            items.addAll(remoteBackgrounds.map { BackgroundItem.RemoteBackground(it) })
        }
    }
    return items
}

fun BackgroundFragment.observeLoadingState() {
    commonViewModel.backgroundUiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressBar.isVisible = isLoading
            binding.rvBackgrounds.isVisible = !isLoading
            // Hide empty state when loading
            if (isLoading) {
                binding.emptyState.isVisible = false
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

private fun BackgroundFragment.updateTabLayout(categories: List<String>) {
    binding.tabLayout.removeAllTabs()

    categories.forEach { category ->
        val tab = binding.tabLayout.newTab()
        tab.text =
            when (category) {
                Constant.ALL -> getString(R.string.all)
                Constant.LOCAL_FILES -> getString(R.string.local_files)
                else -> category
            }
        binding.tabLayout.addTab(tab)
    }
}

private fun BackgroundFragment.selectTabByCategory(category: String) {
    val tabCount = binding.tabLayout.tabCount
    for (i in 0 until tabCount) {
        val tab = binding.tabLayout.getTabAt(i)
        val tabText = tab?.text.toString()

        // Check if this tab matches the category
        val matches =
            when (category) {
                Constant.ALL -> tabText == getString(R.string.all)
                Constant.LOCAL_FILES -> tabText == getString(R.string.local_files)
                else -> tabText == category
            }

        if (matches) {
            tab?.select()
            break
        }
    }
}

fun BackgroundFragment.filterListAds(rawList: List<BackgroundItem>): List<BackgroundItem> {
    val tag = "filterListBackground"
    if (!checkConditionShowAds(
            context = requireContext(),
            spaceNameConfig = "listBG-ct",
        )
    ) {
        Timber.tag(tag).d("rawList $rawList")
        return rawList
    }

    val editedList = mutableListOf<BackgroundItem>()
    var count = 0
    var isLastTimeAddAds1 = false

    val itemAds1 =
        AdsItem(
            configName = "listBG-ct",
            admobIdName = "listBG-ct_native1",
        )
    val zipItemAds1 =
        BackgroundItem.AdsBackgroundItem(
            adsItem = itemAds1,
        )

    val itemAds2 =
        AdsItem(
            configName = "listBG-ct",
            admobIdName = "listBG-ct_native2",
        )

    val zipItemAds2 =
        BackgroundItem.AdsBackgroundItem(
            adsItem = itemAds2,
        )

    rawList.forEachIndexed { index, item ->
        editedList.add(item)
        count++
        if (index == 2 || count == Constant.numberOfContentBetweenBackgroundList * 3) {
            if (!isLastTimeAddAds1) {
                editedList.add(zipItemAds1)
                isLastTimeAddAds1 = true
            } else {
                editedList.add(zipItemAds2)
                isLastTimeAddAds1 = false
            }
            count = 0
        }
    }
    Timber.tag(tag).d("editedList $editedList")
    return editedList
}

fun BackgroundFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "listBG-ct",
        spaceNameAds = "listBG-ct_native1",
        includeHasBeenOpened = false,
    )

    safePreloadAds(
        spaceNameConfig = "listBG-ct",
        spaceNameAds = "listBG-ct_native2",
        includeHasBeenOpened = false,
    )

    safePreloadAds(
        listSpaceNameConfig = listOf("listBG-choosecate", "listBG-gallery"),
        spaceNameAds = "list-1ID_interstitial",
    )

    safePreloadAds(
        spaceNameConfig = "listBG-choosect",
        spaceNameAds = "listBG-choosect_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "listBG",
        spaceName = "listBG_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
