package co.ziplock.framework.presentation.background

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import dagger.hilt.android.lifecycle.HiltViewModel
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import kotlinx.parcelize.IgnoredOnParcel
import javax.inject.Inject

@HiltViewModel
class BackgroundViewModel @Inject constructor() : BaseViewModel() {

}

@Parcelize
data class BackgroundUiState(
    val isLoading: Boolean = false,
    val categories: List<String> = emptyList(),
    val backgroundCategories: Map<String, List<BackgroundResponse>> = emptyMap(),
    val allBackgrounds: List<BackgroundResponse> = emptyList(),
    val localBackgrounds: List<LocalImageData> = emptyList(),
    val selectedCategory: String? = null,
    val currentCategoryBackgrounds: List<BackgroundResponse> = emptyList(),
    val currentLocalBackgrounds: List<LocalImageData> = emptyList(),
    val selectedBackground: BackgroundResponse? = null,
    @IgnoredOnParcel
    val error: Throwable? = null
) : Parcelable