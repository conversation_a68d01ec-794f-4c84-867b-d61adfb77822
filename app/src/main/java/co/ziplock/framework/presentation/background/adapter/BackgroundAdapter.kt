package co.ziplock.framework.presentation.background.adapter

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import co.ziplock.R
import co.ziplock.databinding.ItemWallpaperBinding
import co.ziplock.databinding.ViewItemNativeHotTrendingBinding
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.presentation.model.background.BackgroundItem
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import java.io.File

/**
 * Optimized background adapter that handles both remote and local backgrounds
 * Uses a single adapter instead of multiple adapters
 */
class BackgroundAdapter(
    private val onRemoteBackgroundClick: (BackgroundResponse) -> Unit,
    private val onLocalBackgroundClick: (LocalImageData) -> Unit,
) : ListAdapter<BackgroundItem, RecyclerView.ViewHolder>(BackgroundDiffCallback()) {
    private var selectedItem: BackgroundItem? = null

    override fun getItemViewType(position: Int): Int =
        when (getItem(position)) {
            is BackgroundItem.AdsBackgroundItem -> ITEM_TYPE_ADS
            else -> ITEM_TYPE_NORMAL
        }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RecyclerView.ViewHolder {
        if (viewType == ITEM_TYPE_NORMAL) {
            val binding =
                ItemWallpaperBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            return BackgroundViewHolder(binding)
        } else {
            // Inflate ads item layout if needed
            val binding =
                ViewItemNativeHotTrendingBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                )
            return AdsViewHolder(binding)
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
    ) {
        if (holder is AdsViewHolder) {
            holder.bind(getItem(position))
        } else if (holder is BackgroundViewHolder) {
            holder.bind(getItem(position))
        }
    }

    /**
     * Submit backgrounds for different categories
     * @param remoteBackgrounds List of remote backgrounds from network
     * @param localBackgrounds List of local backgrounds from device storage
     * @param category Current selected category to determine which backgrounds to show
     */
    fun submitBackgrounds(
        remoteBackgrounds: List<BackgroundResponse>,
        localBackgrounds: List<LocalImageData>,
        category: String,
    ) {
        val items = mutableListOf<BackgroundItem>()

        when (category) {
            Constant.ALL -> {
                // Show both remote and local backgrounds
                items.addAll(remoteBackgrounds.map { BackgroundItem.RemoteBackground(it) })
                items.addAll(localBackgrounds.map { BackgroundItem.LocalBackgroundItem(it) })
            }

            Constant.LOCAL_FILES -> {
                // Show only local backgrounds
                items.addAll(localBackgrounds.map { BackgroundItem.LocalBackgroundItem(it) })
            }

            else -> {
                // Show only remote backgrounds for specific categories
                items.addAll(remoteBackgrounds.map { BackgroundItem.RemoteBackground(it) })
            }
        }

        submitList(items)
    }

    /**
     * Set selected background item with visual feedback
     */
    fun setSelectedItem(item: BackgroundItem?) {
        val oldSelected = selectedItem
        selectedItem = item

        // Notify changes for old and new selected items to update overlay
        oldSelected?.let { old ->
            currentList.indexOf(old).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
        item?.let { new ->
            currentList.indexOf(new).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
    }

    /**
     * Set selected remote background by creating BackgroundItem wrapper
     */
    fun setSelectedRemoteBackground(background: BackgroundResponse?) {
        val item = background?.let { BackgroundItem.RemoteBackground(it) }
        setSelectedItem(item)
    }

    /**
     * Set selected local background by creating BackgroundItem wrapper
     */
    fun setSelectedLocalBackground(background: LocalImageData?) {
        val item = background?.let { BackgroundItem.LocalBackgroundItem(it) }
        setSelectedItem(item)
    }

    private var fragment: Fragment? = null

    fun setFragment(fragment: Fragment) {
        this.fragment = fragment
    }

    inner class AdsViewHolder(
        private val binding: ViewItemNativeHotTrendingBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BackgroundItem) {
            if (item is BackgroundItem.AdsBackgroundItem) {
                binding.layoutAds.post {
                    val view =
                        LayoutInflater
                            .from(binding.root.context)
                            .inflate(R.layout.layout_native_trending_list, null)
                    fragment?.showLoadedNative(
                        spaceNameConfig = item.adsItem.configName,
                        spaceName = item.adsItem.admobIdName,
                        includeHasBeenOpened = true,
                        viewAdsInflateFromXml = view,
                        ratioView = "328:136",
                        layoutToAttachAds = binding.adViewGroup,
                        layoutContainAds = binding.layoutAds,
                        onAdsClick = {},
                    )
                }
            }
        }
    }

    inner class BackgroundViewHolder(
        private val binding: ItemWallpaperBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(backgroundItem: BackgroundItem) {
            when (backgroundItem) {
                is BackgroundItem.RemoteBackground -> {
                    bindRemoteBackground(backgroundItem.background)
                }

                is BackgroundItem.LocalBackgroundItem -> {
                    bindLocalBackground(backgroundItem.background)
                }

                is BackgroundItem.AdsBackgroundItem -> {
                    // Ads items should not be handled by BackgroundViewHolder
                    // This case should not occur, but we handle it gracefully
                }
            }

            // Show selection overlay if this item is selected (only for non-ads items)
            binding.overlay.isVisible = selectedItem == backgroundItem
        }

        private fun bindRemoteBackground(background: BackgroundResponse) {
            // Load remote background image from network
            Glide
                .with(binding.root.context)
                .load(background.previewThumbnail)
                .placeholder(android.R.drawable.ic_menu_gallery)
                .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .skipMemoryCache(false)
                .centerCrop()
                .into(binding.ivWallpaper)

            // Set click listener for remote background
            binding.root.setPreventDoubleClick {
                setSelectedRemoteBackground(background)
                onRemoteBackgroundClick(background)
            }
        }

        private fun bindLocalBackground(localBackground: LocalImageData) {
            // Load local background image from device storage
            val file = File(localBackground.filePath)
            val uri = Uri.fromFile(file)

            Glide
                .with(binding.root.context)
                .load(uri)
                .placeholder(android.R.drawable.ic_menu_gallery)
                .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.DATA)
                .skipMemoryCache(false)
                .centerCrop()
                .into(binding.ivWallpaper)

            // Set click listener for local background
            binding.root.setPreventDoubleClick {
                setSelectedLocalBackground(localBackground)
                onLocalBackgroundClick(localBackground)
            }
        }
    }

    private class BackgroundDiffCallback : DiffUtil.ItemCallback<BackgroundItem>() {
        override fun areItemsTheSame(
            oldItem: BackgroundItem,
            newItem: BackgroundItem,
        ): Boolean =
            when {
                oldItem is BackgroundItem.RemoteBackground && newItem is BackgroundItem.RemoteBackground -> {
                    oldItem.background.fileUrl == newItem.background.fileUrl
                }

                oldItem is BackgroundItem.LocalBackgroundItem && newItem is BackgroundItem.LocalBackgroundItem -> {
                    oldItem.background.filePath == newItem.background.filePath
                }

                oldItem is BackgroundItem.AdsBackgroundItem && newItem is BackgroundItem.AdsBackgroundItem -> {
                    oldItem.adsItem.admobIdName == newItem.adsItem.admobIdName
                }

                else -> false
            }

        override fun areContentsTheSame(
            oldItem: BackgroundItem,
            newItem: BackgroundItem,
        ): Boolean = oldItem == newItem
    }

    companion object {
        const val ITEM_TYPE_ADS = 1
        const val ITEM_TYPE_NORMAL = 0
    }
}
