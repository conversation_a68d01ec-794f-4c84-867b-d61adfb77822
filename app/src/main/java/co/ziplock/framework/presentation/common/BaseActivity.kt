package co.ziplock.framework.presentation.common

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding
import co.ziplock.util.LocaleHelper
import co.ziplock.util.PrefUtil
import javax.inject.Inject

abstract class BaseActivity<VB : ViewBinding> : AppCompatActivity() {

    private var _binding: VB? = null
    val binding: VB
        get() = checkNotNull(_binding) {
            "Activity $this binding cannot be accessed before onCreate or after onDestroy"
        }

    @Inject
    lateinit var prefUtil: PrefUtil

    abstract fun inflateViewBinding(inflater: LayoutInflater): VB
    
    abstract fun initView()

    override fun attachBaseContext(newBase: Context) {
        val context = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // For Android 13 and above, we use the new Per-app language preferences
            newBase
        } else {
            // For Android 12 and below, we need to manually update the configuration
            LocaleHelper.createLocalizedContext(newBase)
        }
        super.attachBaseContext(context)
    }

    open fun configBeforeSettingBinding() {
        // Hide navigation bar
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // For Android 11 (API 30) and above
            window.setDecorFitsSystemWindows(false)
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        } else {
            // For older versions
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
                    View.SYSTEM_UI_FLAG_FULLSCREEN
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        configBeforeSettingBinding()
        _binding = inflateViewBinding(layoutInflater)
        setContentView(binding.root)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // For Android 13+, set the app language using AppCompatDelegate
            LocaleHelper.applyAppLanguage(this)
        }
        
        initView()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }



    companion object {
        private const val TAG = "BaseActivity"
    }
}