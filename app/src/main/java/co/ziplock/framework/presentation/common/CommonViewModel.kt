package co.ziplock.framework.presentation.common

import android.app.Application
import android.os.Parcelable
import androidx.lifecycle.SavedStateHandle
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import co.ziplock.BuildConfig
import co.ziplock.R
import co.ziplock.framework.network.ApiOtpInterface
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.OtpRequestBody
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.otpworkmanager.ResetLimitOtpWorker
import co.ziplock.framework.presentation.background.BackgroundUiState
import co.ziplock.framework.presentation.model.OnboardingFavoriteStyle
import co.ziplock.framework.presentation.model.home.HotTrendingUiState
import co.ziplock.framework.presentation.model.remoteConfig.RemoteConfigData
import co.ziplock.framework.presentation.model.row.RowUiState
import co.ziplock.framework.presentation.model.sound.SoundUiState
import co.ziplock.framework.presentation.model.template.TemplateDataResult
import co.ziplock.framework.presentation.model.template.getAllRows
import co.ziplock.framework.presentation.model.template.toHotTrendingThemeModels
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.framework.presentation.wallpaper.WallpaperUiState
import co.ziplock.framework.presentation.zipper.ZipperUiState
import co.ziplock.repository.ApiRepository
import co.ziplock.repository.FileRepository
import co.ziplock.repository.RemoteConfigRepository
import co.ziplock.util.Constant
import co.ziplock.util.PrefUtil
import co.ziplock.util.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.parcelize.Parcelize
import timber.log.Timber
import java.io.File
import java.util.Calendar
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@Parcelize
data class EditLayoutUiState(
    val zipperResponse: ZipperResponse? = null,
    val rowResponse: RowResponse? = null,
    val wallpaperResponse: WallpaperResponse? = null,
    val localWallpaper: LocalImageData? = null,
    val backgroundResponse: BackgroundResponse? = null,
    val localBackground: LocalImageData? = null,
    val soundResponse: SoundResponse? = null,
) : Parcelable

@HiltViewModel
class CommonViewModel
    @Inject
    constructor(
        private val application: Application,
        private val apiOtpInterface: ApiOtpInterface,
        private val prefUtil: PrefUtil,
        private val remoteConfigRepository: RemoteConfigRepository,
        private val apiRepository: ApiRepository,
        private val fileRepository: FileRepository,
        private val savedStateHandle: SavedStateHandle,
    ) : BaseViewModel() {
        companion object {
            private const val KEY_REMOTE_CONFIG_DATA = "remote_config_data"
            private const val KEY_ONBOARDING_UI_STATE = "onboarding_ui_state"
            private const val KEY_HOME_SCREEN_UI_STATE = "home_screen_ui_state"
            private const val KEY_HOT_TRENDING_UI_STATE = "hot_trending_ui_state"
            private const val KEY_ZIPPER_UI_STATE = "zipper_ui_state"
            private const val KEY_ROW_UI_STATE = "row_ui_state"
            private const val KEY_WALLPAPER_UI_STATE = "wallpaper_ui_state"
            private const val KEY_BACKGROUND_UI_STATE = "background_ui_state"
            private const val KEY_SOUND_UI_STATE = "sound_ui_state"
            private const val KEY_EDIT_LAYOUT_UI_STATE = "edit_layout_ui_state"
            private const val KEY_UNLOCKED_HOT_TRENDING_THEMES = "unlocked_hot_trending_themes"
            private const val KEY_UNLOCKED_ROWS = "unlocked_rows"
            private const val KEY_UNLOCKED_ZIPPERS = "unlocked_zippers"
        }

        private val _remoteConfigDataStateFlow =
            MutableStateFlow(
                savedStateHandle.get<RemoteConfigData?>(KEY_REMOTE_CONFIG_DATA),
            )
        val remoteConfigDataStateFlow = _remoteConfigDataStateFlow.asStateFlow()

        // Onboarding
        private val _onboardingUiEvent: MutableSharedFlow<OnboardUiEvent> = MutableSharedFlow()
        val onboardingUiEvent: SharedFlow<OnboardUiEvent> = _onboardingUiEvent.asSharedFlow()

        private val _onboardingUiState: MutableStateFlow<OnboardingUiState> =
            MutableStateFlow(
                savedStateHandle.get<OnboardingUiState>(KEY_ONBOARDING_UI_STATE) ?: OnboardingUiState(),
            )
        val onboardingUiState = _onboardingUiState.asStateFlow()

        private val _homeScreenUiState: MutableStateFlow<HomeScreenUiState> =
            MutableStateFlow(
                savedStateHandle.get<HomeScreenUiState>(KEY_HOME_SCREEN_UI_STATE) ?: HomeScreenUiState(),
            )
        val homeScreenUiState = _homeScreenUiState.asStateFlow()

        private val _hotTrendingUiState =
            MutableStateFlow(
                savedStateHandle.get<HotTrendingUiState>(KEY_HOT_TRENDING_UI_STATE) ?: HotTrendingUiState(),
            )
        val hotTrendingUiState = _hotTrendingUiState.asStateFlow()

        private val _sendOtpStatus = MutableStateFlow<SendOtpStatus>(SendOtpStatus.None)
        val sendOtpStatus = _sendOtpStatus.asStateFlow()

        private val _zipperUiState =
            MutableStateFlow(
                savedStateHandle.get<ZipperUiState>(KEY_ZIPPER_UI_STATE) ?: ZipperUiState(),
            )
        val zipperUiState = _zipperUiState.asStateFlow()

        private val _rowUiState =
            MutableStateFlow(
                savedStateHandle.get<RowUiState>(KEY_ROW_UI_STATE) ?: RowUiState(),
            )
        val rowUiState = _rowUiState.asStateFlow()

        private val _wallpaperUiState =
            MutableStateFlow(
                savedStateHandle.get<WallpaperUiState>(KEY_WALLPAPER_UI_STATE) ?: WallpaperUiState(),
            )
        val wallpaperUiState = _wallpaperUiState.asStateFlow()

        private val _backgroundUiState =
            MutableStateFlow(
                savedStateHandle.get<BackgroundUiState>(KEY_BACKGROUND_UI_STATE) ?: BackgroundUiState(),
            )
        val backgroundUiState = _backgroundUiState.asStateFlow()

        private val _soundUiState =
            MutableStateFlow(
                savedStateHandle.get<SoundUiState>(KEY_SOUND_UI_STATE) ?: SoundUiState(),
            )
        val soundUiState = _soundUiState.asStateFlow()

        private val _editLayoutUiState =
            MutableStateFlow(
                savedStateHandle.get<EditLayoutUiState>(KEY_EDIT_LAYOUT_UI_STATE) ?: EditLayoutUiState(),
            )
        val editLayoutUiState = _editLayoutUiState.asStateFlow()

        // Unlocked items management
        private val _unlockedHotTrendingThemes =
            MutableStateFlow(
                savedStateHandle.get<List<String>>(KEY_UNLOCKED_HOT_TRENDING_THEMES) ?: emptyList(),
            )
        val unlockedHotTrendingThemes = _unlockedHotTrendingThemes.asStateFlow()

        private val _unlockedRows =
            MutableStateFlow(
                savedStateHandle.get<List<String>>(KEY_UNLOCKED_ROWS) ?: emptyList(),
            )
        val unlockedRows = _unlockedRows.asStateFlow()

        private val _unlockedZippers =
            MutableStateFlow(
                savedStateHandle.get<List<String>>(KEY_UNLOCKED_ZIPPERS) ?: emptyList(),
            )
        val unlockedZippers = _unlockedZippers.asStateFlow()

        private var otpCode: String? = null

        init {
            fetchRemoteConfigData()
        }

        // Helper function to update StateFlow and SavedStateHandle simultaneously
        private fun <T> updateStateAndSave(
            stateFlow: MutableStateFlow<T>,
            key: String,
            update: (T) -> T,
        ) {
            val newValue = update(stateFlow.value)
            stateFlow.update { newValue }
            savedStateHandle[key] = newValue
        }

        fun sendGoToNextOnboardingScreenEvent() {
            launchMain {
                _onboardingUiEvent.emit(OnboardUiEvent.GoToNextScreen)
            }
        }

        fun sendGoToPreviousOnboardingScreenEvent() {
            launchMain {
                _onboardingUiEvent.emit(OnboardUiEvent.GoToPreviousScreen)
            }
        }

        fun updateSelectedFavoriteStyles(selectedStyles: List<OnboardingFavoriteStyle>) {
            updateStateAndSave(_onboardingUiState, KEY_ONBOARDING_UI_STATE) {
                it.copy(selectedFavoriteStyles = selectedStyles)
            }
        }

        fun resetOtpRequestStatus() {
            _sendOtpStatus.update { SendOtpStatus.None }
        }

        fun setOtpCode(code: String?) {
            otpCode = code
        }

        fun submitOtpCode(
            code: String,
            onSuccess: () -> Unit,
            onFailure: () -> Unit,
        ) {
            if (code == otpCode) {
                otpCode = null
                onSuccess.invoke()
            } else {
                onFailure.invoke()
            }
        }

        fun requestOtp(email: String?) {
            if (prefUtil.testOTPMode) {
                _sendOtpStatus.update { SendOtpStatus.Success("123456") }
                return
            }

            launchIO(
                exceptionHandler =
                    CoroutineExceptionHandler { _, throwable ->
                        Timber.e("${throwable.message}")
                        _sendOtpStatus.update { SendOtpStatus.Error }
                    },
            ) {
                if (prefUtil.countRequestOtp >= Constant.TIMES_LIMITS_REQUEST_OTP) {
                    _sendOtpStatus.update { SendOtpStatus.Limited }
                    return@launchIO
                }

                if (otpCode != null) {
                    _sendOtpStatus.update { SendOtpStatus.Success(otpCode!!) }
                    return@launchIO
                }

                _sendOtpStatus.update { SendOtpStatus.Standby }

                if (email == null) {
                    _sendOtpStatus.update { SendOtpStatus.Error }
                    return@launchIO
                }

                val otp = generateOtp()
                val body = OtpRequestBody(email = email, otp = otp)
                val response = apiOtpInterface.sendOtp(apiKey = BuildConfig.API_KEY, request = body)

                if (prefUtil.countRequestOtp == 0) {
                    resetLimitOtp()
                }

                prefUtil.countRequestOtp += 1

                _sendOtpStatus.update {
                    if (response.status == 200) {
                        SendOtpStatus.Success(otp)
                    } else {
                        SendOtpStatus.Error
                    }
                }
            }
        }

        /***
         *  Reset limit OTP every day at midnight using WorkManager
         */
        private fun resetLimitOtp() {
            // Thời gian hiện tại
            val now = Calendar.getInstance()

            // Đặt thời gian đến 0h00 ngày hôm sau
            val nextMidnight =
                Calendar.getInstance().apply {
                    timeInMillis = now.timeInMillis
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                    add(Calendar.DAY_OF_YEAR, 1) // sang ngày mai
                }

            val initialDelay = nextMidnight.timeInMillis - now.timeInMillis

            val workRequest =
                PeriodicWorkRequestBuilder<ResetLimitOtpWorker>(1, TimeUnit.DAYS)
                    .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
                    .build()

            WorkManager
                .getInstance(application.applicationContext)
                .enqueueUniquePeriodicWork(
                    "ResetLimitOtpDaily",
                    ExistingPeriodicWorkPolicy.REPLACE,
                    workRequest,
                )
        }

        private fun generateOtp(): String {
            val randomNumbers = (1..6).map { (0..9).random() }
            return randomNumbers.joinToString("")
        }

        fun fetchRemoteConfigData() {
            launchIO(
                exceptionHandler =
                    CoroutineExceptionHandler { _, throwable ->
                        Timber.e("${throwable.message}")
                    },
            ) {
                val remoteConfigData = remoteConfigRepository.fetchRemoteConfig()
                _remoteConfigDataStateFlow.update { remoteConfigData }
                savedStateHandle[KEY_REMOTE_CONFIG_DATA] = remoteConfigData
            }
        }

        fun loadAllTemplateDataWithCategories() {
            launchIO {
                // Set loading state
                updateStateAndSave(
                    _hotTrendingUiState,
                    KEY_HOT_TRENDING_UI_STATE,
                ) { it.copy(isLoading = true) }
                updateStateAndSave(_zipperUiState, KEY_ZIPPER_UI_STATE) { it.copy(isLoading = true) }
                updateStateAndSave(_rowUiState, KEY_ROW_UI_STATE) { it.copy(isLoading = true) }
                updateStateAndSave(
                    _wallpaperUiState,
                    KEY_WALLPAPER_UI_STATE,
                ) { it.copy(isLoading = true) }
                updateStateAndSave(
                    _backgroundUiState,
                    KEY_BACKGROUND_UI_STATE,
                ) { it.copy(isLoading = true) }
                updateStateAndSave(_soundUiState, KEY_SOUND_UI_STATE) { it.copy(isLoading = true) }
                updateStateAndSave(
                    _onboardingUiState,
                    KEY_ONBOARDING_UI_STATE,
                ) { it.copy(isStyleLoading = true) }

                // Load all hot trending data
                apiRepository.loadAllTemplateDataWithCategories().collect { result ->
                    when (result) {
                        is Result.Success -> {
                            // Process hot trending data
                            updateStateAndSave(_hotTrendingUiState, KEY_HOT_TRENDING_UI_STATE) {
                                it.copy(
                                    isLoading = false,
                                    hotTrendingThemes = result.data.toHotTrendingThemeModels(),
                                    error = null,
                                )
                            }

                            // Process zipper data
                            processZipperData(result.data)

                            // Process row data
                            processRowData(result.data)

                            // Process wallpaper data
                            processWallpaperData(result.data)

                            // Process background data
                            processBackgroundData(result.data)

                            // Process sound data
                            processSoundData(result.data)

                            updateStateAndSave(_onboardingUiState, KEY_ONBOARDING_UI_STATE) {
                                val onboardingStyles = result.data.baseTemplateData.onboardingStyles
                                it.copy(
                                    isStyleLoading = false,
                                    onboardingFavoriteStyles =
                                        onboardingStyles.ifEmpty {
                                            getDefaultOnboardingFavoriteStyles()
                                        },
                                )
                            }
                        }

                        is Result.Error -> {
                            updateStateAndSave(_hotTrendingUiState, KEY_HOT_TRENDING_UI_STATE) {
                                it.copy(
                                    isLoading = false,
                                    error = result.error,
                                )
                            }

                            updateStateAndSave(_zipperUiState, KEY_ZIPPER_UI_STATE) {
                                it.copy(
                                    isLoading = false,
                                    error = result.error,
                                )
                            }

                            updateStateAndSave(_rowUiState, KEY_ROW_UI_STATE) {
                                it.copy(
                                    isLoading = false,
                                    error = result.error,
                                )
                            }

                            updateStateAndSave(_wallpaperUiState, KEY_WALLPAPER_UI_STATE) {
                                it.copy(
                                    isLoading = false,
                                    error = result.error,
                                )
                            }

                            updateStateAndSave(_backgroundUiState, KEY_BACKGROUND_UI_STATE) {
                                it.copy(
                                    isLoading = false,
                                    error = result.error,
                                )
                            }

                            updateStateAndSave(_soundUiState, KEY_SOUND_UI_STATE) {
                                it.copy(
                                    isLoading = false,
                                    error = result.error,
                                )
                            }

                            updateStateAndSave(_onboardingUiState, KEY_ONBOARDING_UI_STATE) {
                                it.copy(
                                    isStyleLoading = false,
                                    onboardingFavoriteStyles = getDefaultOnboardingFavoriteStyles(),
                                )
                            }
                        }
                    }
                }
            }
        }

        fun processZipperData(templateData: TemplateDataResult) {
            val zipperCategories = templateData.categoryTemplateData.zipperCategories
            val zipperMap = templateData.baseTemplateData.zipperMap

            // Create categories list with Constant.ALL as first item
            val categoryNames =
                mutableListOf(Constant.ALL).apply {
                    addAll(zipperCategories.keys)
                }

            // Get all zippers from zipperMap
            val allZippers = zipperMap.values.toList()

            updateStateAndSave(_zipperUiState, KEY_ZIPPER_UI_STATE) { currentState ->
                val selectedCategory = currentState.selectedCategory ?: Constant.ALL
                val currentZippers =
                    when (selectedCategory) {
                        Constant.ALL -> allZippers
                        else -> zipperCategories[selectedCategory] ?: emptyList()
                    }

                currentState.copy(
                    isLoading = false,
                    categories = categoryNames,
                    zipperCategories = zipperCategories,
                    allZippers = allZippers,
                    selectedCategory = selectedCategory,
                    currentCategoryZippers = currentZippers,
                    error = null,
                )
            }
        }

        fun selectZipperCategory(category: String) {
            val currentState = _zipperUiState.value
            val zippers =
                when (category) {
                    Constant.ALL -> currentState.allZippers
                    else -> currentState.zipperCategories[category] ?: emptyList()
                }

            updateStateAndSave(_zipperUiState, KEY_ZIPPER_UI_STATE) {
                it.copy(
                    selectedCategory = category,
                    currentCategoryZippers = zippers,
                )
            }
        }

        fun processRowData(templateData: TemplateDataResult) {
            // Get all rows from API data
            val allRows = templateData.getAllRows()

            updateStateAndSave(_rowUiState, KEY_ROW_UI_STATE) {
                it.copy(
                    isLoading = false,
                    allRows = allRows,
                    error = null,
                )
            }
        }

        fun processWallpaperData(templateData: TemplateDataResult) {
            val wallpaperCategories = templateData.categoryTemplateData.wallpaperCategories
            val wallpaperMap = templateData.baseTemplateData.wallpaperMap

            // Create categories list with Constant.ALL and Constant.LOCAL_FILES as first items
            val categoryNames =
                mutableListOf(Constant.ALL, Constant.LOCAL_FILES).apply {
                    addAll(wallpaperCategories.keys)
                }

            // Get all wallpapers from wallpaperMap
            val allWallpapers = wallpaperMap.values.toList()

            // Load local wallpapers
            loadLocalWallpapers()

            updateStateAndSave(_wallpaperUiState, KEY_WALLPAPER_UI_STATE) { currentState ->
                val selectedCategory = currentState.selectedCategory ?: Constant.ALL
                val currentWallpapers =
                    when (selectedCategory) {
                        Constant.ALL -> allWallpapers
                        Constant.LOCAL_FILES -> emptyList() // Local wallpapers will be shown separately
                        else -> wallpaperCategories[selectedCategory] ?: emptyList()
                    }

                currentState.copy(
                    isLoading = false,
                    categories = categoryNames,
                    wallpaperCategories = wallpaperCategories,
                    allWallpapers = allWallpapers,
                    selectedCategory = selectedCategory,
                    currentCategoryWallpapers = currentWallpapers,
                    error = null,
                )
            }
        }

        fun processBackgroundData(templateData: TemplateDataResult) {
            // For now, using wallpaper data as background data
            // In future, you can add separate backgroundCategories and backgroundMap in TemplateDataResult
            val backgroundCategories = templateData.categoryTemplateData.wallpaperCategories
            val backgroundMap = templateData.baseTemplateData.wallpaperMap

            // Create categories list with Constant.ALL and Constant.LOCAL_FILES as first items
            val categoryNames =
                mutableListOf(Constant.ALL, Constant.LOCAL_FILES).apply {
                    addAll(backgroundCategories.keys)
                }

            // Get all backgrounds from backgroundMap (using wallpaper data for now)
            val allBackgrounds =
                backgroundMap.values.map { wallpaper ->
                    BackgroundResponse(
                        fileUrl = wallpaper.fileUrl,
                        previewThumbnail = wallpaper.previewThumbnail,
                    )
                }

            // Convert wallpaper categories to background categories
            val backgroundCategoriesMap =
                backgroundCategories.mapValues { (_, wallpapers) ->
                    wallpapers.map { wallpaper ->
                        BackgroundResponse(
                            fileUrl = wallpaper.fileUrl,
                            previewThumbnail = wallpaper.previewThumbnail,
                        )
                    }
                }

            // Load local backgrounds
            loadLocalBackgrounds()

            updateStateAndSave(_backgroundUiState, KEY_BACKGROUND_UI_STATE) { currentState ->
                val selectedCategory = currentState.selectedCategory ?: Constant.ALL
                val currentBackgrounds =
                    when (selectedCategory) {
                        Constant.ALL -> allBackgrounds
                        Constant.LOCAL_FILES -> emptyList() // Local backgrounds will be shown separately
                        else -> backgroundCategoriesMap[selectedCategory] ?: emptyList()
                    }

                currentState.copy(
                    isLoading = false,
                    categories = categoryNames,
                    backgroundCategories = backgroundCategoriesMap,
                    allBackgrounds = allBackgrounds,
                    selectedCategory = selectedCategory,
                    currentCategoryBackgrounds = currentBackgrounds,
                    error = null,
                )
            }
        }

        private fun loadLocalWallpapers() {
            launchIO {
                try {
                    val wallpaperPaths = fileRepository.loadWallpaperImages()
                    val localWallpapers =
                        wallpaperPaths.map { path ->
                            val file = File(path)
                            LocalImageData(
                                filePath = path,
                                fileName = file.name,
                                lastModified = file.lastModified(),
                            )
                        }

                    Timber.d("Loaded ${localWallpapers.size} local wallpapers")

                    updateStateAndSave(_wallpaperUiState, KEY_WALLPAPER_UI_STATE) { currentState ->
                        val currentLocalWallpapers =
                            if (currentState.selectedCategory == Constant.LOCAL_FILES) {
                                localWallpapers
                            } else {
                                currentState.currentLocalWallpapers
                            }

                        currentState.copy(
                            localWallpapers = localWallpapers,
                            currentLocalWallpapers = currentLocalWallpapers,
                        )
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error loading local wallpapers")
                }
            }
        }

        fun selectWallpaperCategory(category: String) {
            val currentState = _wallpaperUiState.value
            val wallpapers =
                when (category) {
                    Constant.ALL -> currentState.allWallpapers
                    Constant.LOCAL_FILES -> emptyList() // Local wallpapers will be shown separately
                    else -> currentState.wallpaperCategories[category] ?: emptyList()
                }

            val localWallpapers =
                when (category) {
                    Constant.ALL -> currentState.localWallpapers // Show local wallpapers in "All" tab too
                    Constant.LOCAL_FILES -> currentState.localWallpapers
                    else -> emptyList()
                }

            updateStateAndSave(_wallpaperUiState, KEY_WALLPAPER_UI_STATE) {
                it.copy(
                    selectedCategory = category,
                    currentCategoryWallpapers = wallpapers,
                    currentLocalWallpapers = localWallpapers,
                )
            }
        }

        fun refreshLocalWallpapers() {
            loadLocalWallpapers()
        }

        // Background methods - similar to wallpaper methods
        fun selectBackgroundCategory(category: String) {
            val currentState = _backgroundUiState.value
            val backgrounds =
                when (category) {
                    Constant.ALL -> currentState.allBackgrounds
                    Constant.LOCAL_FILES -> emptyList() // Local backgrounds will be shown separately
                    else -> currentState.backgroundCategories[category] ?: emptyList()
                }

            val localBackgrounds =
                when (category) {
                    Constant.ALL -> currentState.localBackgrounds // Show local backgrounds in "All" tab too
                    Constant.LOCAL_FILES -> currentState.localBackgrounds
                    else -> emptyList()
                }

            updateStateAndSave(_backgroundUiState, KEY_BACKGROUND_UI_STATE) {
                it.copy(
                    selectedCategory = category,
                    currentCategoryBackgrounds = backgrounds,
                    currentLocalBackgrounds = localBackgrounds,
                )
            }
        }

        fun refreshLocalBackgrounds() {
            loadLocalBackgrounds()
        }

        private fun loadLocalBackgrounds() {
            launchIO {
                try {
                    val backgroundPaths =
                        fileRepository.loadBackgroundImages() // Load from Background folder specifically
                    val localBackgrounds =
                        backgroundPaths.map { path ->
                            val file = File(path)
                            LocalImageData( // Using same model as wallpaper
                                filePath = path,
                                fileName = file.name,
                                lastModified = file.lastModified(),
                            )
                        }

                    Timber.d("Loaded ${localBackgrounds.size} local backgrounds from Background folder")

                    updateStateAndSave(_backgroundUiState, KEY_BACKGROUND_UI_STATE) { currentState ->
                        val currentLocalBackgrounds =
                            if (currentState.selectedCategory == Constant.LOCAL_FILES) {
                                localBackgrounds
                            } else {
                                currentState.currentLocalBackgrounds
                            }

                        currentState.copy(
                            localBackgrounds = localBackgrounds,
                            currentLocalBackgrounds = currentLocalBackgrounds,
                        )
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error loading local backgrounds")
                }
            }
        }

        fun processSoundData(templateData: TemplateDataResult) {
            val soundCategories = templateData.categoryTemplateData.soundCategories
            val soundMap = templateData.baseTemplateData.soundMap

            // Create categories list with Constant.ALL as first item
            val categoryNames =
                mutableListOf(Constant.ALL).apply {
                    addAll(soundCategories.keys)
                }

            // Get all sounds from soundMap
            val allSounds = soundMap.values.toList()

            updateStateAndSave(_soundUiState, KEY_SOUND_UI_STATE) { currentState ->
                val selectedCategory = currentState.selectedCategory ?: Constant.ALL
                val currentSounds =
                    when (selectedCategory) {
                        Constant.ALL -> allSounds
                        else -> soundCategories[selectedCategory] ?: emptyList()
                    }

                currentState.copy(
                    isLoading = false,
                    categories = categoryNames,
                    soundCategories = soundCategories,
                    allSounds = allSounds,
                    selectedCategory = selectedCategory,
                    currentCategorySounds = currentSounds,
                    error = null,
                )
            }
        }

        fun selectSoundCategory(category: String) {
            val currentState = _soundUiState.value
            val sounds =
                when (category) {
                    Constant.ALL -> currentState.allSounds
                    else -> currentState.soundCategories[category] ?: emptyList()
                }

            updateStateAndSave(_soundUiState, KEY_SOUND_UI_STATE) {
                it.copy(
                    selectedCategory = category,
                    currentCategorySounds = sounds,
                )
            }
        }

        // Theme customization methods - temporarily store in EditLayoutUiState
        fun setSelectedZipperResponse(zipperResponse: ZipperResponse?) {
            zipperResponse?.let { setEditLayoutZipper(it) }
        }

        fun setSelectedWallpaperImagePath(
            wallpaperResponse: WallpaperResponse? = null,
            localWallpaper: LocalImageData? = null,
        ) {
            when {
                wallpaperResponse != null -> setEditLayoutWallpaper(wallpaperResponse)
                localWallpaper != null -> setEditLayoutLocalWallpaper(localWallpaper)
            }
        }

        fun setSelectedBackgroundImagePath(
            backgroundResponse: BackgroundResponse? = null,
            localBackground: LocalImageData? = null,
        ) {
            when {
                backgroundResponse != null -> setEditLayoutBackground(backgroundResponse)
                localBackground != null -> setEditLayoutLocalBackground(localBackground)
            }
        }

        fun setSelectedSound(soundResponse: SoundResponse?) {
            soundResponse?.let { setEditLayoutSound(it) }
        }

        fun setSelectedRowResponse(rowResponse: RowResponse?) {
            rowResponse?.let { setEditLayoutRow(it) }
        }

        // Save all temporary data to PrefUtil when going to SaveSuccess screen
        fun saveEditLayoutDataToPref() {
            val currentEditState = _editLayoutUiState.value

            // Save zipper
            currentEditState.zipperResponse?.let { zipper ->
                prefUtil.selectedZipperResponse = zipper
            }

            // Save wallpaper
            when {
                currentEditState.wallpaperResponse != null -> {
                    prefUtil.selectedWallpaperResponse = currentEditState.wallpaperResponse
                }

                currentEditState.localWallpaper != null -> {
                    // For local wallpapers, create a WallpaperResponse with the file path
                    prefUtil.selectedWallpaperResponse = WallpaperResponse(
                        fileUrl = currentEditState.localWallpaper.filePath,
                        previewThumbnail = null
                    )
                }
            }

            // Save background
            when {
                currentEditState.backgroundResponse != null -> {
                    prefUtil.selectedBackgroundResponse = currentEditState.backgroundResponse
                }

                currentEditState.localBackground != null -> {
                    // For local backgrounds, create a BackgroundResponse with the file path
                    prefUtil.selectedBackgroundResponse = BackgroundResponse(
                        fileUrl = currentEditState.localBackground.filePath,
                        previewThumbnail = null
                    )
                }
            }

            // Save sound
            currentEditState.soundResponse?.let { sound ->
                if (sound.id == Constant.NO_SOUND_ID) {
                    // Clear sound selection for "No Sound"
                    prefUtil.selectedSoundUrl = null
                    prefUtil.selectedSoundImagePreviewPath = null
                } else {
                    // Set sound selection for regular sounds
                    prefUtil.selectedSoundUrl = sound.fileUrl
                    prefUtil.selectedSoundImagePreviewPath = sound.thumbnailUrl
                }
            }

            // Save row
            currentEditState.rowResponse?.let { row ->
                prefUtil.selectedRowResponse = row
            }
        }

        fun requestOpenHomeTabScreen() {
            updateStateAndSave(_homeScreenUiState, KEY_HOME_SCREEN_UI_STATE) {
                it.copy(
                    currentPageIndex = 1,
                )
            }
        }

        fun requestOpenSettingsTabScreen() {
            updateStateAndSave(_homeScreenUiState, KEY_HOME_SCREEN_UI_STATE) {
                it.copy(
                    currentPageIndex = 0,
                )
            }
        }

        // EditLayoutUiState management functions
        fun setEditLayoutZipper(zipperResponse: ZipperResponse) {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) {
                it.copy(zipperResponse = zipperResponse)
            }
        }

        fun setEditLayoutRow(rowResponse: RowResponse) {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) {
                it.copy(rowResponse = rowResponse)
            }
        }

        fun setEditLayoutWallpaper(wallpaperResponse: WallpaperResponse) {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) {
                it.copy(
                    wallpaperResponse = wallpaperResponse,
                    localWallpaper = null, // Clear local wallpaper when setting network wallpaper
                )
            }
        }

        fun setEditLayoutLocalWallpaper(localWallpaper: LocalImageData) {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) {
                it.copy(
                    localWallpaper = localWallpaper,
                    wallpaperResponse = null, // Clear network wallpaper when setting local wallpaper
                )
            }
        }

        fun setEditLayoutBackground(backgroundResponse: BackgroundResponse) {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) {
                it.copy(
                    backgroundResponse = backgroundResponse,
                    localBackground = null, // Clear local background when setting network background
                )
            }
        }

        fun setEditLayoutLocalBackground(localBackground: LocalImageData) {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) {
                it.copy(
                    localBackground = localBackground,
                    backgroundResponse = null, // Clear network background when setting local background
                )
            }
        }

        fun setEditLayoutSound(soundResponse: SoundResponse) {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) {
                it.copy(soundResponse = soundResponse)
            }
        }

        fun clearEditLayoutUiState() {
            updateStateAndSave(_editLayoutUiState, KEY_EDIT_LAYOUT_UI_STATE) { EditLayoutUiState() }
        }

        fun refreshEditLayoutUiStateFromPrefUtil() {
            // This method is used to sync EditLayoutUiState with PrefUtil data
            // Useful when coming back from fragments that save directly to PrefUtil (like EditImageFragment)
            val currentState = _editLayoutUiState.value

            // Only update if EditLayoutUiState doesn't have the data but PrefUtil does
            if (currentState.wallpaperResponse == null && currentState.localWallpaper == null) {
                prefUtil.selectedWallpaperResponse?.let { wallpaperResponse ->
                    val fileUrl = wallpaperResponse.fileUrl
                    if (fileUrl != null) {
                        if (fileUrl.startsWith("http")) {
                            // It's a network wallpaper
                            setEditLayoutWallpaper(wallpaperResponse)
                        } else {
                            // It's a local wallpaper
                            val localWallpaper = LocalImageData(
                                filePath = fileUrl,
                                fileName = fileUrl.substringAfterLast("/"),
                                lastModified = System.currentTimeMillis(),
                            )
                            setEditLayoutLocalWallpaper(localWallpaper)
                        }
                    }
                }
            }

            if (currentState.backgroundResponse == null && currentState.localBackground == null) {
                prefUtil.selectedBackgroundResponse?.let { backgroundResponse ->
                    val fileUrl = backgroundResponse.fileUrl
                    if (fileUrl != null) {
                        if (fileUrl.startsWith("http")) {
                            // It's a network background
                            setEditLayoutBackground(backgroundResponse)
                        } else {
                            // It's a local background
                            val localBackground = LocalImageData(
                                filePath = fileUrl,
                                fileName = fileUrl.substringAfterLast("/"),
                                lastModified = System.currentTimeMillis(),
                            )
                            setEditLayoutLocalBackground(localBackground)
                        }
                    }
                }
            }
        }

        fun loadDefaultDataFromPrefUtil() {
            // Load all default data from PrefUtil to EditLayoutUiState
            // This is called when HomeTabFragment resumes to ensure EditLayoutUiState is synced with saved preferences

            // Load zipper data
            prefUtil.selectedZipperResponse?.let { zipperResponse ->
                setEditLayoutZipper(zipperResponse)
            }

            // Load wallpaper data
            prefUtil.selectedWallpaperResponse?.let { wallpaperResponse ->
                val fileUrl = wallpaperResponse.fileUrl
                if (fileUrl != null) {
                    if (fileUrl.startsWith("http")) {
                        // It's a network wallpaper
                        setEditLayoutWallpaper(wallpaperResponse)
                    } else {
                        // It's a local wallpaper
                        val localWallpaper = LocalImageData(
                            filePath = fileUrl,
                            fileName = fileUrl.substringAfterLast("/"),
                            lastModified = System.currentTimeMillis(),
                        )
                        setEditLayoutLocalWallpaper(localWallpaper)
                    }
                }
            }

            // Load background data
            prefUtil.selectedBackgroundResponse?.let { backgroundResponse ->
                val fileUrl = backgroundResponse.fileUrl
                if (fileUrl != null) {
                    if (fileUrl.startsWith("http")) {
                        // It's a network background
                        setEditLayoutBackground(backgroundResponse)
                    } else {
                        // It's a local background
                        val localBackground = LocalImageData(
                            filePath = fileUrl,
                            fileName = fileUrl.substringAfterLast("/"),
                            lastModified = System.currentTimeMillis(),
                        )
                        setEditLayoutLocalBackground(localBackground)
                    }
                }
            }

            // Load sound data
            prefUtil.selectedSoundUrl?.let { soundUrl ->
                val soundResponse =
                    SoundResponse(
                        fileUrl = soundUrl,
                        thumbnailUrl = prefUtil.selectedSoundImagePreviewPath ?: "",
                    )
                setEditLayoutSound(soundResponse)
            }

            // Load row data
            prefUtil.selectedRowResponse?.let { rowResponse ->
                setEditLayoutRow(rowResponse)
            }
        }

        // Unlocked items management functions
        fun addUnlockedHotTrendingTheme(id: String) {
            if (id.isNotEmpty() && !_unlockedHotTrendingThemes.value.contains(id)) {
                updateStateAndSave(_unlockedHotTrendingThemes, KEY_UNLOCKED_HOT_TRENDING_THEMES) {
                    it + id
                }
            }
        }

        fun addUnlockedRow(id: String) {
            if (id.isNotEmpty() && !_unlockedRows.value.contains(id)) {
                updateStateAndSave(_unlockedRows, KEY_UNLOCKED_ROWS) {
                    it + id
                }
            }
        }

        fun addUnlockedZipper(id: String) {
            if (id.isNotEmpty() && !_unlockedZippers.value.contains(id)) {
                updateStateAndSave(_unlockedZippers, KEY_UNLOCKED_ZIPPERS) {
                    it + id
                }
            }
        }

        fun isHotTrendingThemeUnlocked(id: String?): Boolean {
            return id?.let { _unlockedHotTrendingThemes.value.contains(it) } ?: false
        }

        fun isRowUnlocked(id: String?): Boolean {
            return id?.let { _unlockedRows.value.contains(it) } ?: false
        }

        fun isZipperUnlocked(id: String?): Boolean {
            return id?.let { _unlockedZippers.value.contains(it) } ?: false
        }

        private fun getDefaultOnboardingFavoriteStyles(): List<OnboardingFavoriteStyle> =
            listOf(
                OnboardingFavoriteStyle(
                    name = "King of Sports",
                    thumbnailUrl = null,
                    isShow = true,
                    idThemes = null,
                    defaultDrawable = R.drawable.king_of_sports,
                ),
                OnboardingFavoriteStyle(
                    name = "Glam & Sparkle",
                    thumbnailUrl = null,
                    isShow = true,
                    idThemes = null,
                    defaultDrawable = R.drawable.glam_and_sparkle,
                ),
                OnboardingFavoriteStyle(
                    name = "Gaming",
                    thumbnailUrl = null,
                    isShow = true,
                    idThemes = null,
                    defaultDrawable = R.drawable.gaming,
                ),
                OnboardingFavoriteStyle(
                    name = "Music & idols",
                    thumbnailUrl = null,
                    isShow = true,
                    idThemes = null,
                    defaultDrawable = R.drawable.music_and_idols,
                ),
                OnboardingFavoriteStyle(
                    name = "Mystic & Calm",
                    thumbnailUrl = null,
                    isShow = true,
                    idThemes = null,
                    defaultDrawable = R.drawable.mystic_and_calm,
                ),
                OnboardingFavoriteStyle(
                    name = "Super Hero",
                    thumbnailUrl = null,
                    isShow = true,
                    idThemes = null,
                    defaultDrawable = R.drawable.super_hero,
                ),
            )
    }

sealed class SendOtpStatus {
    data object Limited : SendOtpStatus()

    data object None : SendOtpStatus()

    data object Standby : SendOtpStatus()

    data class Success(
        val otp: String,
    ) : SendOtpStatus()

    data object Error : SendOtpStatus()
}

sealed class OnboardUiEvent {
    data object GoToPreviousScreen : OnboardUiEvent()

    data object GoToNextScreen : OnboardUiEvent()
}

@Parcelize
data class OnboardingUiState(
    val isStyleLoading: Boolean = false,
    val onboardingFavoriteStyles: List<OnboardingFavoriteStyle> = emptyList(),
    val selectedFavoriteStyles: List<OnboardingFavoriteStyle> = emptyList(),
) : Parcelable

@Parcelize
data class HomeScreenUiState(
    val currentPageIndex: Int = Constant.NO_INDEX,
) : Parcelable
