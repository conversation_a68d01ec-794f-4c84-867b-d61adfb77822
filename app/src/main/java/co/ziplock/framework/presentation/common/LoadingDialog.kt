package co.ziplock.framework.presentation.common

import co.ziplock.R
import co.ziplock.databinding.DialogLoadingBinding

class LoadingDialog : BaseDialogFragment<DialogLoadingBinding>(R.layout.dialog_loading) {
    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo(
        screenWidthPercent = 50f,
        screenHeightPercent = 50f,
    )

    companion object {
        @Volatile
        private var instance: LoadingDialog? = null

        fun getInstance(): LoadingDialog {
            return instance ?: synchronized(this) {
                instance ?: LoadingDialog().also { instance = it }
            }
        }
    }
}

