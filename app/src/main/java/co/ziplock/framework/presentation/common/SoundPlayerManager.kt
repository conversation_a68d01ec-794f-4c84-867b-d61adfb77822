package co.ziplock.framework.presentation.common

import android.media.MediaPlayer
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SoundPlayerManager @Inject constructor() {
    
    private var mediaPlayer: MediaPlayer? = null
    private var currentSoundUrl: String? = null
    
    private val _playingState = MutableStateFlow<PlayingState>(PlayingState.Stopped)
    val playingState: StateFlow<PlayingState> = _playingState.asStateFlow()
    
    private val _currentPlayingSoundUrl = MutableStateFlow<String?>(null)
    val currentPlayingSoundUrl: StateFlow<String?> = _currentPlayingSoundUrl.asStateFlow()
    
    sealed class PlayingState {
        object Stopped : PlayingState()
        object Loading : PlayingState()
        object Playing : PlayingState()
        object Paused : PlayingState()
        data class Error(val message: String) : PlayingState()
    }
    
    fun playSound(soundUrl: String) {
        if (currentSoundUrl == soundUrl) {
            // Same sound - toggle play/pause
            togglePlayPause()
            return
        }
        
        // Different sound - stop current and play new
        stopSound()
        startNewSound(soundUrl)
    }
    
    private fun startNewSound(soundUrl: String) {
        _playingState.value = PlayingState.Loading
        _currentPlayingSoundUrl.value = soundUrl
        currentSoundUrl = soundUrl
        
        try {
            mediaPlayer = MediaPlayer().apply {
                setDataSource(soundUrl)
                setOnPreparedListener { mp ->
                    _playingState.value = PlayingState.Playing
                    mp.start()
                    Log.d("SoundPlayerManager", "MediaPlayer prepared and playing: $soundUrl")
                }
                setOnCompletionListener {
                    _playingState.value = PlayingState.Stopped
                    _currentPlayingSoundUrl.value = null
                    currentSoundUrl = null
                    Log.d("SoundPlayerManager", "MediaPlayer completed")
                }
                setOnErrorListener { _, what, extra ->
                    Log.e("SoundPlayerManager", "MediaPlayer error: what=$what, extra=$extra")
                    _playingState.value = PlayingState.Error("Failed to play sound")
                    _currentPlayingSoundUrl.value = null
                    currentSoundUrl = null
                    false
                }
                prepareAsync()
            }
        } catch (e: Exception) {
            Log.e("SoundPlayerManager", "Error initializing MediaPlayer", e)
            _playingState.value = PlayingState.Error("Failed to initialize player")
            _currentPlayingSoundUrl.value = null
            currentSoundUrl = null
        }
    }
    
    private fun togglePlayPause() {
        mediaPlayer?.let { mp ->
            try {
                if (mp.isPlaying) {
                    mp.pause()
                    _playingState.value = PlayingState.Paused
                    Log.d("SoundPlayerManager", "MediaPlayer paused")
                } else {
                    mp.start()
                    _playingState.value = PlayingState.Playing
                    Log.d("SoundPlayerManager", "MediaPlayer resumed")
                }
            } catch (e: Exception) {
                Log.e("SoundPlayerManager", "Error toggling play/pause", e)
                _playingState.value = PlayingState.Error("Failed to toggle playback")
            }
        }
    }
    
    fun stopSound() {
        try {
            mediaPlayer?.apply {
                if (isPlaying) {
                    stop()
                }
                release()
            }
        } catch (e: Exception) {
            Log.e("SoundPlayerManager", "Error stopping MediaPlayer", e)
        } finally {
            mediaPlayer = null
            _playingState.value = PlayingState.Stopped
            _currentPlayingSoundUrl.value = null
            currentSoundUrl = null
            Log.d("SoundPlayerManager", "MediaPlayer stopped and released")
        }
    }
    
    fun pauseSound() {
        try {
            mediaPlayer?.let { mp ->
                if (mp.isPlaying) {
                    mp.pause()
                    _playingState.value = PlayingState.Paused
                    Log.d("SoundPlayerManager", "MediaPlayer paused")
                }
            }
        } catch (e: Exception) {
            Log.e("SoundPlayerManager", "Error pausing MediaPlayer", e)
        }
    }
    
    fun isPlaying(soundUrl: String): Boolean {
        return currentSoundUrl == soundUrl && _playingState.value == PlayingState.Playing
    }
    
    fun isPaused(soundUrl: String): Boolean {
        return currentSoundUrl == soundUrl && _playingState.value == PlayingState.Paused
    }
    
    fun isLoading(soundUrl: String): Boolean {
        return currentSoundUrl == soundUrl && _playingState.value == PlayingState.Loading
    }
    
    fun getCurrentPlayingUrl(): String? = currentSoundUrl
}