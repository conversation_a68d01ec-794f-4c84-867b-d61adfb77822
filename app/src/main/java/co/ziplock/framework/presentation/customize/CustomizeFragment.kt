package co.ziplock.framework.presentation.customize

import android.view.View
import co.ziplock.R
import co.ziplock.databinding.FragmentCustomizeBinding
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.common.CommonViewModel
import co.ziplock.framework.presentation.common.SoundPlayerManager
import co.ziplock.framework.presentation.customize.adapter.RowPreviewAdapter
import co.ziplock.framework.presentation.customize.adapter.SoundPreviewAdapter
import co.ziplock.framework.presentation.customize.adapter.ZipperPreviewAdapter
import co.ziplock.framework.presentation.row.logicClickRow
import co.ziplock.framework.presentation.row.showInterWhenClickRow
import co.ziplock.framework.presentation.theme.dialog.RewardContentDialog
import co.ziplock.framework.presentation.zipper.logicClickZipper
import co.ziplock.framework.presentation.zipper.showInterWhenClickZipper
import co.ziplock.util.displayToast
import dagger.hilt.android.AndroidEntryPoint
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.showLoadedRewardVideo
import javax.inject.Inject

@AndroidEntryPoint
class CustomizeFragment :
    BaseFragment<FragmentCustomizeBinding, CommonViewModel>(
        FragmentCustomizeBinding::inflate,
        CommonViewModel::class.java,
    ),
    RowPreviewAdapter.Listener,
    ZipperPreviewAdapter.Listener {
    @Inject
    lateinit var soundPlayerManager: SoundPlayerManager

    val zipperAdapter = ZipperPreviewAdapter()
    var currentZip: ZipperResponse? = null

    val rowAdapter = RowPreviewAdapter()
    var currentRow: RowResponse? = null

    var soundAdapter: SoundPreviewAdapter? = null

    override fun init(view: View) {
        commonViewModel.refreshLocalWallpapers()
        commonViewModel.refreshLocalBackgrounds()
        setupBackButton()
        setupZipperSection()
        setupRowSection()
        setupWallpaperSection()
        setupBackgroundSection()
        setupSoundSection()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeZipperData()
        observeRowData()
        observeWallpaperData()
        observeBackgroundData()
        observeSoundData()
        observeLoadingState()
        observeSoundPlayerState()
    }

    override fun onPause() {
        super.onPause()
        // Pause sound when fragment is paused
        soundPlayerManager.pauseSound()
    }

    override fun onRowClick(row: RowResponse) {
        currentRow = row
        if (checkConditionShowAds(
                context = requireContext(),
                spaceNameConfig = "rowpro",
            ) &&
            row.isPro == true &&
            !commonViewModel.isRowUnlocked(row.id)
        ) {
            val dialog = RewardContentDialog()
            dialog.setListener(
                object : RewardContentDialog.Listener {
                    override fun onWatchVideoEvent() {
                        showLoadedRewardVideo(
                            spaceNameConfig = "rowpro",
                            spaceName = "rowpro_rewarded",
                            destinationToShowAds = R.id.customizeFragment,
                            isShowLoadingView = true,
                            isScreenType = false,
                            onRewardDone = { isSuccess ->
                                if (!isSuccess) {
                                    showInterWhenClickRow()
                                }
                            },
                            onGetReward = {
                                if (currentRow == null) {
                                    displayToast(R.string.something_error)
                                    return@showLoadedRewardVideo
                                }
                                logicClickRow()
                            },
                        )
                    }

                    override fun onBuyVipVersion() {
                        safeNav(R.id.customizeFragment, R.id.action_to_iapFragment)
                    }
                },
            )
            dialog.show(childFragmentManager)
        } else {
            showInterWhenClickRow()
        }
    }

    override fun onZipperClick(zipper: ZipperResponse) {
        currentZip = zipper
        if (checkConditionShowAds(
                context = requireContext(),
                spaceNameConfig = "zippro",
            ) &&
            zipper.isPro == true &&
            !commonViewModel.isZipperUnlocked(zipper.id)
        ) {
            val dialog = RewardContentDialog()
            dialog.setListener(
                object : RewardContentDialog.Listener {
                    override fun onWatchVideoEvent() {
                        showLoadedRewardVideo(
                            spaceNameConfig = "zippro",
                            spaceName = "zippro_rewarded",
                            destinationToShowAds = R.id.customizeFragment,
                            isShowLoadingView = true,
                            isScreenType = false,
                            onRewardDone = { isSuccess ->
                                if (!isSuccess) {
                                    showInterWhenClickZipper()
                                }
                            },
                            onGetReward = {
                                if (currentZip == null) {
                                    displayToast(R.string.something_error)
                                    return@showLoadedRewardVideo
                                }
                                logicClickZipper()
                            },
                        )
                    }

                    override fun onBuyVipVersion() {
                        safeNav(R.id.customizeFragment, R.id.action_to_iapFragment)
                    }
                },
            )
            dialog.show(childFragmentManager)
        } else {
            showInterWhenClickZipper()
        }
    }

    companion object {
        const val TAG = "CustomizeFragment"

        fun newInstance() = CustomizeFragment()
    }
}
