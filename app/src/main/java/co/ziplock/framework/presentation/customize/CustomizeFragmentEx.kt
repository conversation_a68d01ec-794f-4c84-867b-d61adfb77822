package co.ziplock.framework.presentation.customize

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import co.ziplock.R
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.customize.adapter.BackgroundPreviewAdapter
import co.ziplock.framework.presentation.customize.adapter.RowPreviewAdapter
import co.ziplock.framework.presentation.customize.adapter.SoundPreviewAdapter
import co.ziplock.framework.presentation.customize.adapter.WallpaperPreviewAdapter
import co.ziplock.framework.presentation.customize.adapter.ZipperPreviewAdapter
import co.ziplock.framework.presentation.zipper.logicClickZipper
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun CustomizeFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        findNavController().navigateUp()
    }

    onSystemBackEvent {
        findNavController().navigateUp()
    }
}

fun CustomizeFragment.showInterWhenClickZipper() {
    showLoadedInter(
        spaceNameConfig = "zip-nopro",
        spaceName = "content_1ID_interstitial",
        destinationToShowAds = R.id.customizeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            logicClickZipper()
        },
        onCloseAds = {},
    )
}

fun CustomizeFragment.logicClickZipper() {
    if (currentZip == null) {
        displayToast(R.string.something_error)
        return
    }
    if (currentZip!!.isPro == true) {
        commonViewModel.addUnlockedZipper(currentZip!!.id ?: "")
    }
    commonViewModel.setEditLayoutZipper(currentZip!!)
    // Navigate to EditLayoutFragment with selected zipper
    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.ZIPPER_DATA, currentZip)
        }
    safeNavInter(
        R.id.customizeFragment,
        R.id.action_customizeFragment_to_editLayoutFragment,
        bundle,
    )
}

fun CustomizeFragment.setupZipperSection() {
    zipperAdapter.setListener(this)
    binding.rvZipperPreview.apply {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        adapter = zipperAdapter
    }

    binding.btnZipperMore.setPreventDoubleClick {
        // Navigate to full Zipper fragment
        showInterMoreAndDoAction {
            safeNavInter(
                R.id.customizeFragment,
                R.id.action_customizeFragment_to_zipperFragment,
            )
        }
    }
}

fun CustomizeFragment.logicClickRow() {
    if (currentRow == null) {
        displayToast(R.string.something_error)
        return
    }
    if (currentRow!!.isPro == true) {
        commonViewModel.addUnlockedRow(currentRow!!.id ?: "")
    }
    commonViewModel.setSelectedRowResponse(currentRow)
    // Navigate to EditLayoutFragment with selected row
    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.ROW_DATA, currentRow)
        }
    safeNavInter(
        R.id.customizeFragment,
        R.id.action_customizeFragment_to_editLayoutFragment,
        bundle,
    )
}

fun CustomizeFragment.showInterWhenClickRow() {
    showLoadedInter(
        spaceNameConfig = "row-nopro",
        spaceName = "content_1ID_interstitial",
        destinationToShowAds = R.id.customizeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            logicClickRow()
        },
        onCloseAds = {},
    )
}

fun CustomizeFragment.setupRowSection() {
    rowAdapter.setListener(this)

    binding.rvRowPreview.apply {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        adapter = rowAdapter
    }

    binding.btnRowMore.setPreventDoubleClick {
        // Navigate to Row fragment
        showInterMoreAndDoAction {
            safeNavInter(
                R.id.customizeFragment,
                R.id.action_customizeFragment_to_rowFragment,
            )
        }
    }
}

fun CustomizeFragment.setupWallpaperSection() {
    val wallpaperAdapter =
        WallpaperPreviewAdapter(
            onWallpaperClick = { wallpaper ->
                showInternChooseContentAndDoAction {
                    commonViewModel.setSelectedWallpaperImagePath(wallpaperResponse = wallpaper)
                    // Navigate to EditLayoutFragment with selected network wallpaper
                    val bundle =
                        Bundle().apply {
                            putParcelable(BundleKey.WALLPAPER_DATA, wallpaper)
                            putString(BundleKey.KEY_SAVE_CONTEXT, Constant.SAVE_CONTEXT_WALLPAPER)
                        }
                    safeNavInter(
                        R.id.customizeFragment,
                        R.id.action_customizeFragment_to_editLayoutFragment,
                        bundle,
                    )
                }
            },
            onLocalWallpaperClick = { localWallpaper ->
                showInternChooseContentAndDoAction {
                    commonViewModel.setSelectedWallpaperImagePath(localWallpaper = localWallpaper)
                    // Navigate to EditLayoutFragment with selected local wallpaper
                    val bundle =
                        Bundle().apply {
                            putParcelable(BundleKey.LOCAL_WALLPAPER_DATA, localWallpaper)
                            putString(BundleKey.KEY_SAVE_CONTEXT, Constant.SAVE_CONTEXT_WALLPAPER)
                        }
                    safeNavInter(
                        R.id.customizeFragment,
                        R.id.action_customizeFragment_to_editLayoutFragment,
                        bundle,
                    )
                }
            },
            onChooseFromGalleryClick = {
                // Navigate to pick photo for wallpaper
                showInternChooseContentAndDoAction {
                    safeNavInter(
                        R.id.customizeFragment,
                        R.id.action_customizeFragment_to_pickPhotoFragment,
                        bundleOf(
                            BundleKey.KEY_SAVE_CONTEXT to Constant.SAVE_CONTEXT_WALLPAPER,
                        ),
                    )
                }
            },
        )

    binding.rvWallpaperPreview.apply {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        adapter = wallpaperAdapter
    }

    binding.btnWallpaperMore.setPreventDoubleClick {
        // Navigate to full Wallpaper fragment
        showInterMoreAndDoAction {
            safeNavInter(
                R.id.customizeFragment,
                R.id.action_customizeFragment_to_wallpaperFragment,
            )
        }
    }
}

fun CustomizeFragment.setupBackgroundSection() {
    val backgroundAdapter =
        BackgroundPreviewAdapter(
            onBackgroundClick = { background ->
                showInternChooseContentAndDoAction {
                    commonViewModel.setSelectedBackgroundImagePath(backgroundResponse = background)
                    // Navigate to EditLayoutFragment with selected network background
                    val bundle =
                        Bundle().apply {
                            putParcelable(BundleKey.BACKGROUND_DATA, background)
                            putString(BundleKey.KEY_SAVE_CONTEXT, Constant.SAVE_CONTEXT_BACKGROUND)
                        }
                    safeNavInter(
                        R.id.customizeFragment,
                        R.id.action_customizeFragment_to_editLayoutFragment,
                        bundle,
                    )
                }
            },
            onLocalBackgroundClick = { localBackground ->
                showInternChooseContentAndDoAction {
                    commonViewModel.setSelectedBackgroundImagePath(localBackground = localBackground)
                    // Navigate to EditLayoutFragment with selected local background
                    val bundle =
                        Bundle().apply {
                            putParcelable(BundleKey.LOCAL_BACKGROUND_DATA, localBackground)
                            putString(BundleKey.KEY_SAVE_CONTEXT, Constant.SAVE_CONTEXT_BACKGROUND)
                        }
                    safeNavInter(
                        R.id.customizeFragment,
                        R.id.action_customizeFragment_to_editLayoutFragment,
                        bundle,
                    )
                }
            },
            onChooseFromGalleryClick = {
                // Navigate to pick photo for background
                showInternChooseContentAndDoAction {
                    safeNavInter(
                        R.id.customizeFragment,
                        R.id.action_customizeFragment_to_pickPhotoFragment,
                        bundleOf(
                            BundleKey.KEY_SAVE_CONTEXT to Constant.SAVE_CONTEXT_BACKGROUND,
                        ),
                    )
                }
            },
        )

    binding.rvBackgroundPreview.apply {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        adapter = backgroundAdapter
    }

    binding.btnBackgroundMore.setPreventDoubleClick {
        // Navigate to full Background fragment
        showInterMoreAndDoAction {
            safeNavInter(
                R.id.customizeFragment,
                R.id.action_customizeFragment_to_backgroundFragment,
            )
        }
    }
}

fun CustomizeFragment.setupSoundSection() {
    soundAdapter =
        SoundPreviewAdapter(
            onSoundClick = { sound ->
                showInternChooseContentAndDoAction {
                    // Handle sound selection - save to EditLayoutUiState instead of PrefUtil
                    commonViewModel.setEditLayoutSound(sound)

                    // Navigate to EditLayoutFragment with selected sound
                    val bundle =
                        Bundle().apply {
                            putParcelable(BundleKey.SOUND_DATA, sound)
                        }
                    safeNavInter(
                        R.id.customizeFragment,
                        R.id.action_customizeFragment_to_editLayoutFragment,
                        bundle,
                    )
                }
            },
            onPlayPauseClick = { sound ->
                // Handle play/pause click
                handleSoundPlayPause(sound)
            },
        )

    binding.rvSoundPreview.apply {
        layoutManager = LinearLayoutManager(context)
        adapter = soundAdapter
    }

    // Load selected sound from preferences
    val selectedSoundUrl = prefUtil.selectedSoundUrl
    soundAdapter?.setSelectedSoundUrl(selectedSoundUrl)

    binding.btnSoundMore.setPreventDoubleClick {
        // Navigate to full Sound fragment
        showInterMoreAndDoAction {
            safeNavInter(
                R.id.customizeFragment,
                R.id.action_customizeFragment_to_soundFragment,
            )
        }
    }
}

fun CustomizeFragment.observeZipperData() {
    // Observe zipper data from CommonViewModel
    commonViewModel.zipperUiState
        .map { it.allZippers }
        .distinctUntilChanged()
        .onEach { zippers ->
            val zipperAdapter = binding.rvZipperPreview.adapter as? ZipperPreviewAdapter
            zipperAdapter?.submitList(zippers)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CustomizeFragment.observeRowData() {
    // Observe row data from CommonViewModel
    commonViewModel.rowUiState
        .map { it.allRows }
        .distinctUntilChanged()
        .onEach { rows ->
            val rowAdapter = binding.rvRowPreview.adapter as? RowPreviewAdapter
            rowAdapter?.submitList(rows)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CustomizeFragment.observeWallpaperData() {
    // Observe wallpaper data from CommonViewModel - combine network and local wallpapers like "All" tab
    commonViewModel.wallpaperUiState
        .map { uiState ->
            // Combine network wallpapers and local wallpapers like in "All" tab
            Pair(uiState.allWallpapers, uiState.localWallpapers)
        }.distinctUntilChanged()
        .onEach { (networkWallpapers, localWallpapers) ->
            val adapter = binding.rvWallpaperPreview.adapter as? WallpaperPreviewAdapter
            adapter?.submitWallpapersWithLocal(networkWallpapers, localWallpapers)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CustomizeFragment.observeBackgroundData() {
    // Observe background data from CommonViewModel - combine network and local backgrounds like "All" tab
    commonViewModel.backgroundUiState
        .map { uiState ->
            // Combine network backgrounds and local backgrounds like in "All" tab
            Pair(uiState.allBackgrounds, uiState.localBackgrounds)
        }.distinctUntilChanged()
        .onEach { (networkBackgrounds, localBackgrounds) ->
            val adapter = binding.rvBackgroundPreview.adapter as? BackgroundPreviewAdapter
            adapter?.submitBackgroundsWithLocal(networkBackgrounds, localBackgrounds)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CustomizeFragment.observeSoundData() {
    // Observe sound data from CommonViewModel
    commonViewModel.soundUiState
        .map { it.allSounds }
        .distinctUntilChanged()
        .onEach { sounds ->
            val adapter = binding.rvSoundPreview.adapter as? SoundPreviewAdapter

            // Add "No Sound" option at the beginning of the list
            val soundsWithNoSound =
                buildList {
                    add(SoundResponse.createNoSound())
                    addAll(sounds)
                }

            adapter?.submitList(soundsWithNoSound)

            // Load selected sound from preferences after data is loaded
            val selectedSoundUrl = prefUtil.selectedSoundUrl
            adapter?.setSelectedSoundUrl(selectedSoundUrl)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CustomizeFragment.observeLoadingState() {
    // Observe loading state from any of the UI states
    commonViewModel.zipperUiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressBar.isVisible = isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CustomizeFragment.handleSoundPlayPause(sound: SoundResponse) {
    if (sound.id == Constant.NO_SOUND_ID || sound.fileUrl.isEmpty()) {
        return
    }

    soundPlayerManager.playSound(sound.fileUrl)
}

fun CustomizeFragment.observeSoundPlayerState() {
    // Observe playing state changes
    soundPlayerManager.playingState
        .onEach { state ->
            val currentUrl = soundPlayerManager.getCurrentPlayingUrl()
            soundAdapter?.updatePlayingState(currentUrl, state)
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    // Observe current playing URL changes
    soundPlayerManager.currentPlayingSoundUrl
        .onEach { playingUrl ->
            val state = soundPlayerManager.playingState.value
            soundAdapter?.updatePlayingState(playingUrl, state)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CustomizeFragment.showInterMoreAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "custom-more",
        spaceName = "custom-1ID_interstitial",
        destinationToShowAds = R.id.customizeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun CustomizeFragment.showInternChooseContentAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "custom-choosect",
        spaceName = "custom-1ID_interstitial",
        destinationToShowAds = R.id.customizeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun CustomizeFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("custom-more", "custom-choosect"),
        spaceNameAds = "custom-1ID_interstitial",
    )
    safePreloadAds(
        spaceNameConfig = "themepro",
        spaceNameAds = "themepro_rewarded",
    )
    safePreloadAds(
        spaceNameConfig = "zippro",
        spaceNameAds = "zippro_rewarded",
    )
    safePreloadAds(
        spaceNameConfig = "rowpro",
        spaceNameAds = "rowpro_rewarded",
    )

    safePreloadAds(
        listSpaceNameConfig = listOf("theme-nopro", "zip-nopro", "row-nopro"),
        spaceNameAds = "content_1ID_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "custom-bot",
        spaceName = "custom-bot_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )

    showLoadedNative(
        spaceNameConfig = "custom-top",
        spaceName = "custom-top_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {},
    )
}
