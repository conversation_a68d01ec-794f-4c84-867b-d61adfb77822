package co.ziplock.framework.presentation.customize

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import javax.inject.Inject

@HiltViewModel
class CustomizeViewModel @Inject constructor(
) : ViewModel() {

    private val _uiState = MutableStateFlow(CustomizeUiState())
    val uiState: StateFlow<CustomizeUiState> = _uiState.asStateFlow()

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class CustomizeUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val previewZippers: List<ZipperResponse> = emptyList(),
    val previewRows: List<ZipperResponse> = emptyList(),
    val previewWallpapers: List<WallpaperResponse> = emptyList(),
    val previewBackgrounds: List<BackgroundResponse> = emptyList(),
    val previewSounds: List<SoundResponse> = emptyList()
)