package co.ziplock.framework.presentation.customize.adapter

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import co.ziplock.databinding.ItemChooseFromGalleryBinding
import co.ziplock.databinding.ItemWallpaperPreviewBinding
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.setPreventDoubleClick
import java.io.File

sealed class BackgroundPreviewItem {
    object ChooseFromGallery : BackgroundPreviewItem()
    data class NetworkBackground(val background: BackgroundResponse) : BackgroundPreviewItem()
    data class LocalBackgroundItem(val background: LocalImageData) : BackgroundPreviewItem()
}

class BackgroundPreviewAdapter(
    private val onBackgroundClick: (BackgroundResponse) -> Unit,
    private val onLocalBackgroundClick: (LocalImageData) -> Unit,
    private val onChooseFromGalleryClick: () -> Unit
) : ListAdapter<BackgroundPreviewItem, RecyclerView.ViewHolder>(BackgroundPreviewDiffCallback()) {

    companion object {
        private const val TYPE_CHOOSE_FROM_GALLERY = 0
        private const val TYPE_NETWORK_BACKGROUND = 1
        private const val TYPE_LOCAL_BACKGROUND = 2
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is BackgroundPreviewItem.ChooseFromGallery -> TYPE_CHOOSE_FROM_GALLERY
            is BackgroundPreviewItem.NetworkBackground -> TYPE_NETWORK_BACKGROUND
            is BackgroundPreviewItem.LocalBackgroundItem -> TYPE_LOCAL_BACKGROUND
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_CHOOSE_FROM_GALLERY -> {
                val binding = ItemChooseFromGalleryBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ChooseFromGalleryViewHolder(binding)
            }
            TYPE_NETWORK_BACKGROUND, TYPE_LOCAL_BACKGROUND -> {
                val binding = ItemWallpaperPreviewBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                BackgroundPreviewViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is BackgroundPreviewItem.ChooseFromGallery -> {
                (holder as ChooseFromGalleryViewHolder).bind()
            }
            is BackgroundPreviewItem.NetworkBackground -> {
                (holder as BackgroundPreviewViewHolder).bindNetworkBackground(item.background)
            }
            is BackgroundPreviewItem.LocalBackgroundItem -> {
                (holder as BackgroundPreviewViewHolder).bindLocalBackground(item.background)
            }
        }
    }

    fun submitBackgrounds(backgrounds: List<BackgroundResponse>) {
        val items = mutableListOf<BackgroundPreviewItem>()
        items.add(BackgroundPreviewItem.ChooseFromGallery)
        items.addAll(backgrounds.map { BackgroundPreviewItem.NetworkBackground(it) })
        submitList(items)
    }
    
    fun submitBackgroundsWithLocal(networkBackgrounds: List<BackgroundResponse>, localBackgrounds: List<LocalImageData>) {
        val items = mutableListOf<BackgroundPreviewItem>()
        items.add(BackgroundPreviewItem.ChooseFromGallery)
        // Add network backgrounds first
        items.addAll(networkBackgrounds.map { BackgroundPreviewItem.NetworkBackground(it) })
        // Then add local backgrounds
        items.addAll(localBackgrounds.map { BackgroundPreviewItem.LocalBackgroundItem(it) })
        submitList(items)
    }

    inner class ChooseFromGalleryViewHolder(
        private val binding: ItemChooseFromGalleryBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind() {
            binding.root.setPreventDoubleClick {
                onChooseFromGalleryClick()
            }
        }
    }

    inner class BackgroundPreviewViewHolder(
        private val binding: ItemWallpaperPreviewBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bindNetworkBackground(background: BackgroundResponse) {
            // Load network background image
            Glide.with(binding.root.context)
                .load(background.previewThumbnail)
                .placeholder(android.R.drawable.ic_menu_gallery)
                .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .skipMemoryCache(false)
                .centerCrop()
                .into(binding.ivWallpaper)

            // Set click listener for network background
            binding.root.setPreventDoubleClick {
                onBackgroundClick(background)
            }
        }
        
        fun bindLocalBackground(localBackground: LocalImageData) {
            // Load local background image from device storage
            val file = File(localBackground.filePath)
            val uri = Uri.fromFile(file)
            
            Glide.with(binding.root.context)
                .load(uri)
                .placeholder(android.R.drawable.ic_menu_gallery)
                .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.DATA)
                .skipMemoryCache(false)
                .centerCrop()
                .into(binding.ivWallpaper)

            // Set click listener for local background
            binding.root.setPreventDoubleClick {
                onLocalBackgroundClick(localBackground)
            }
        }
    }

    private class BackgroundPreviewDiffCallback : DiffUtil.ItemCallback<BackgroundPreviewItem>() {
        override fun areItemsTheSame(
            oldItem: BackgroundPreviewItem,
            newItem: BackgroundPreviewItem
        ): Boolean {
            return when {
                oldItem is BackgroundPreviewItem.ChooseFromGallery && newItem is BackgroundPreviewItem.ChooseFromGallery -> true
                oldItem is BackgroundPreviewItem.NetworkBackground && newItem is BackgroundPreviewItem.NetworkBackground -> 
                    oldItem.background.fileUrl == newItem.background.fileUrl
                oldItem is BackgroundPreviewItem.LocalBackgroundItem && newItem is BackgroundPreviewItem.LocalBackgroundItem -> 
                    oldItem.background.filePath == newItem.background.filePath
                else -> false
            }
        }

        override fun areContentsTheSame(
            oldItem: BackgroundPreviewItem,
            newItem: BackgroundPreviewItem
        ): Boolean {
            return when {
                oldItem is BackgroundPreviewItem.ChooseFromGallery && newItem is BackgroundPreviewItem.ChooseFromGallery -> true
                oldItem is BackgroundPreviewItem.NetworkBackground && newItem is BackgroundPreviewItem.NetworkBackground -> 
                    oldItem.background == newItem.background
                oldItem is BackgroundPreviewItem.LocalBackgroundItem && newItem is BackgroundPreviewItem.LocalBackgroundItem -> 
                    oldItem.background == newItem.background
                else -> false
            }
        }
    }
}