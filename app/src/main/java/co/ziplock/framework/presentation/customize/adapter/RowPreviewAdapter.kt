package co.ziplock.framework.presentation.customize.adapter

import co.ziplock.R
import co.ziplock.databinding.ItemRowPreviewBinding
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy

class RowPreviewAdapter :
    BaseListAdapter<RowResponse, ItemRowPreviewBinding>(
        createDiffCallback(
            areItemsTheSame = { oldItem, newItem -> oldItem.previewThumbnail == newItem.previewThumbnail },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
        ),
    ) {
    interface Listener {
        fun onRowClick(row: RowResponse)
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_row_preview

    override fun bindView(
        binding: ItemRowPreviewBinding,
        item: RowResponse,
        position: Int,
    ) {
        // Load preview thumbnail
        item.previewThumbnail?.let { thumbnail ->
            Glide
                .with(binding.root.context)
                .load(thumbnail)
                .override(binding.ivRowPreview.width, binding.ivRowPreview.height)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .skipMemoryCache(false)
                .fitCenter()
                .into(binding.ivRowPreview)
        }

        // Set click listener
        binding.root.setPreventDoubleClick {
            listener?.onRowClick(item)
        }
    }
}
