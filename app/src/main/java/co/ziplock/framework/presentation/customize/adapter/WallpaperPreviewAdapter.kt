package co.ziplock.framework.presentation.customize.adapter

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import co.ziplock.databinding.ItemChooseFromGalleryBinding
import co.ziplock.databinding.ItemWallpaperPreviewBinding
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.setPreventDoubleClick
import java.io.File

sealed class WallpaperPreviewItem {
    object ChooseFromGallery : WallpaperPreviewItem()
    data class NetworkWallpaper(val wallpaper: WallpaperResponse) : WallpaperPreviewItem()
    data class LocalWallpaperItem(val wallpaper: LocalImageData) : WallpaperPreviewItem()
}

class WallpaperPreviewAdapter(
    private val onWallpaperClick: (WallpaperResponse) -> Unit,
    private val onLocalWallpaperClick: (LocalImageData) -> Unit,
    private val onChooseFromGalleryClick: () -> Unit
) : ListAdapter<WallpaperPreviewItem, RecyclerView.ViewHolder>(WallpaperPreviewDiffCallback()) {

    companion object {
        private const val TYPE_CHOOSE_FROM_GALLERY = 0
        private const val TYPE_NETWORK_WALLPAPER = 1
        private const val TYPE_LOCAL_WALLPAPER = 2
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is WallpaperPreviewItem.ChooseFromGallery -> TYPE_CHOOSE_FROM_GALLERY
            is WallpaperPreviewItem.NetworkWallpaper -> TYPE_NETWORK_WALLPAPER
            is WallpaperPreviewItem.LocalWallpaperItem -> TYPE_LOCAL_WALLPAPER
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_CHOOSE_FROM_GALLERY -> {
                val binding = ItemChooseFromGalleryBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ChooseFromGalleryViewHolder(binding)
            }
            TYPE_NETWORK_WALLPAPER, TYPE_LOCAL_WALLPAPER -> {
                val binding = ItemWallpaperPreviewBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                WallpaperPreviewViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is WallpaperPreviewItem.ChooseFromGallery -> {
                (holder as ChooseFromGalleryViewHolder).bind()
            }
            is WallpaperPreviewItem.NetworkWallpaper -> {
                (holder as WallpaperPreviewViewHolder).bindNetworkWallpaper(item.wallpaper)
            }
            is WallpaperPreviewItem.LocalWallpaperItem -> {
                (holder as WallpaperPreviewViewHolder).bindLocalWallpaper(item.wallpaper)
            }
        }
    }

    fun submitWallpapers(wallpapers: List<WallpaperResponse>) {
        val items = mutableListOf<WallpaperPreviewItem>()
        items.add(WallpaperPreviewItem.ChooseFromGallery)
        items.addAll(wallpapers.map { WallpaperPreviewItem.NetworkWallpaper(it) })
        submitList(items)
    }
    
    fun submitWallpapersWithLocal(networkWallpapers: List<WallpaperResponse>, localWallpapers: List<LocalImageData>) {
        val items = mutableListOf<WallpaperPreviewItem>()
        items.add(WallpaperPreviewItem.ChooseFromGallery)
        // Add network wallpapers first
        items.addAll(networkWallpapers.map { WallpaperPreviewItem.NetworkWallpaper(it) })
        // Then add local wallpapers
        items.addAll(localWallpapers.map { WallpaperPreviewItem.LocalWallpaperItem(it) })
        submitList(items)
    }

    inner class ChooseFromGalleryViewHolder(
        private val binding: ItemChooseFromGalleryBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind() {
            binding.root.setPreventDoubleClick {
                onChooseFromGalleryClick()
            }
        }
    }

    inner class WallpaperPreviewViewHolder(
        private val binding: ItemWallpaperPreviewBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bindNetworkWallpaper(wallpaper: WallpaperResponse) {
            // Load network wallpaper image
            Glide.with(binding.root.context)
                .load(wallpaper.previewThumbnail)
                .placeholder(android.R.drawable.ic_menu_gallery)
                .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .skipMemoryCache(false)
                .centerCrop()
                .into(binding.ivWallpaper)

            // Set click listener for network wallpaper
            binding.root.setPreventDoubleClick {
                onWallpaperClick(wallpaper)
            }
        }
        
        fun bindLocalWallpaper(localWallpaper: LocalImageData) {
            // Load local wallpaper image from device storage
            val file = File(localWallpaper.filePath)
            val uri = Uri.fromFile(file)
            
            Glide.with(binding.root.context)
                .load(uri)
                .placeholder(android.R.drawable.ic_menu_gallery)
                .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.DATA)
                .skipMemoryCache(false)
                .centerCrop()
                .into(binding.ivWallpaper)

            // Set click listener for local wallpaper
            binding.root.setPreventDoubleClick {
                onLocalWallpaperClick(localWallpaper)
            }
        }
    }

    private class WallpaperPreviewDiffCallback : DiffUtil.ItemCallback<WallpaperPreviewItem>() {
        override fun areItemsTheSame(
            oldItem: WallpaperPreviewItem,
            newItem: WallpaperPreviewItem
        ): Boolean {
            return when {
                oldItem is WallpaperPreviewItem.ChooseFromGallery && newItem is WallpaperPreviewItem.ChooseFromGallery -> true
                oldItem is WallpaperPreviewItem.NetworkWallpaper && newItem is WallpaperPreviewItem.NetworkWallpaper -> 
                    oldItem.wallpaper.fileUrl == newItem.wallpaper.fileUrl
                oldItem is WallpaperPreviewItem.LocalWallpaperItem && newItem is WallpaperPreviewItem.LocalWallpaperItem -> 
                    oldItem.wallpaper.filePath == newItem.wallpaper.filePath
                else -> false
            }
        }

        override fun areContentsTheSame(
            oldItem: WallpaperPreviewItem,
            newItem: WallpaperPreviewItem
        ): Boolean {
            return when {
                oldItem is WallpaperPreviewItem.ChooseFromGallery && newItem is WallpaperPreviewItem.ChooseFromGallery -> true
                oldItem is WallpaperPreviewItem.NetworkWallpaper && newItem is WallpaperPreviewItem.NetworkWallpaper -> 
                    oldItem.wallpaper == newItem.wallpaper
                oldItem is WallpaperPreviewItem.LocalWallpaperItem && newItem is WallpaperPreviewItem.LocalWallpaperItem -> 
                    oldItem.wallpaper == newItem.wallpaper
                else -> false
            }
        }
    }
}