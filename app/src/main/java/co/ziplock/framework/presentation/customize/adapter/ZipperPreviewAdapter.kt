package co.ziplock.framework.presentation.customize.adapter

import co.ziplock.R
import co.ziplock.databinding.ItemZipperPreviewBinding
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide

class ZipperPreviewAdapter :
    BaseListAdapter<ZipperResponse, ItemZipperPreviewBinding>(
        createDiffCallback(
            areItemsTheSame = { oldItem, newItem -> oldItem.fileUrl == newItem.fileUrl },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
        ),
    ) {
    interface Listener {
        fun onZipperClick(zipper: ZipperResponse)
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_zipper_preview

    override fun bindView(
        binding: ItemZipperPreviewBinding,
        item: ZipperResponse,
        position: Int,
    ) {
        // Load zipper image
        Glide
            .with(binding.root.context)
            .load(item.fileUrl)
            .override(binding.ivZipper.width, binding.ivZipper.height)
            .fitCenter()
            .into(binding.ivZipper)

        // Set click listener
        binding.root.setPreventDoubleClick {
            listener?.onZipperClick(item)
        }
    }
}
