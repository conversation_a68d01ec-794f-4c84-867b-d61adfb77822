package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.FragmentDialogConfirmCancelSavingSecurityQuestionBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.setPreventDoubleClick

class CancelSavingSecurityQuestionDialogFragment :
    BaseDialogFragment<FragmentDialogConfirmCancelSavingSecurityQuestionBinding>(R.layout.fragment_dialog_confirm_cancel_saving_security_question) {

    private var onClickContinueSetup: (() -> Unit)? = null
    private var onClickExit: (() -> Unit)? = null

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo(
        isDialogCancelable = false
    )

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        binding.apply {
            btnContinueSetup.setPreventDoubleClick {
                onClickContinueSetup?.invoke()
                dismiss()
            }
            btnExit.setPreventDoubleClick {
                onClickExit?.invoke()
                dismiss()
            }
        }
    }

    class Builder {
        private var onClickContinueSetupListener: () -> Unit = {}
        private var onClickExitListener: () -> Unit = {}

        fun setOnClickContinueSetupButton(onClickDoIt: () -> Unit) =
            apply { this.onClickContinueSetupListener = onClickDoIt }

        fun setOnClickExitButton(onClickCancel: () -> Unit) =
            apply { this.onClickExitListener = onClickCancel }

        fun build() = CancelSavingSecurityQuestionDialogFragment().apply {
            <EMAIL> = onClickContinueSetupListener
            <EMAIL> = onClickExitListener
        }
    }
}