package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.DialogEnableLockScreenBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.setPreventDoubleClick

class EnableLockScreenDialogFragment : BaseDialogFragment<DialogEnableLockScreenBinding>(
    R.layout.dialog_enable_lock_screen
) {
    var onClickEnableButton: () -> Unit = {}

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo()

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        setupClickListeners()
    }

    private fun setupClickListeners() {
        binding.ivClose.setPreventDoubleClick {
            dismiss()
        }

        binding.tvEnable.setPreventDoubleClick {
            onClickEnableButton.invoke()
            dismiss()
        }
    }

    companion object {
        const val TAG = "EnableLockScreenDialogFragment"
    }
}