package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.FragmentDialogRequiresFilesPermissonBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.setPreventDoubleClick

class FilesPermissionRequiredDialogFragment :
    BaseDialogFragment<FragmentDialogRequiresFilesPermissonBinding>(R.layout.fragment_dialog_requires_files_permisson) {

    private var onClickDenyLockScreen: (() -> Unit)? = null
    private var onClickAllow: (() -> Unit)? = null

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo(
        isDialogCancelable = false
    )

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        binding.apply {
            btnDeny.setPreventDoubleClick {
                onClickDenyLockScreen?.invoke()
                dismiss()
            }
            btnAllow.setPreventDoubleClick {
                onClickAllow?.invoke()
                dismiss()
            }
        }
    }

    class Builder {
        private var onClickDenyLockScreenListener: () -> Unit = {}
        private var onClickAllowListener: () -> Unit = {}

        fun setOnClickDenyLockScreenButton(onClickDoIt: () -> Unit) =
            apply { this.onClickDenyLockScreenListener = onClickDoIt }

        fun setOnClickAllowButton(onClickAllow: () -> Unit) =
            apply { this.onClickAllowListener = onClickAllow }

        fun build() = FilesPermissionRequiredDialogFragment().apply {
            <EMAIL> = onClickDenyLockScreenListener
            <EMAIL> = onClickAllowListener
        }
    }
}