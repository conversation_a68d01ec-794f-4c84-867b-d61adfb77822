package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentManager
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.R
import co.ziplock.databinding.DialogFragmentForgotPasswordBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import javax.inject.Inject

@AndroidEntryPoint
class ForgotPasswordDialogFragment : BaseDialogFragment<DialogFragmentForgotPasswordBinding>(
    R.layout.dialog_fragment_forgot_password
) {
    @Inject
    lateinit var securityManager: SecurityManager
    
    private var selectedMethod: RecoveryMethod = RecoveryMethod.SECURITY_QUESTION
    
    enum class RecoveryMethod {
        SECURITY_QUESTION, EMAIL
    }

    override fun getDialogFragmentInfo(): DialogFragmentInfo {
        return DialogFragmentInfo(
            isDialogCancelable = false
        )
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        // Override dialog size to use wrap_content for height
        setDialogCanCancel()
        setupInitialSelection()
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        setupClickListeners()
    }

    private fun setupInitialSelection() {
        // Default to security question option
        selectSecurityQuestionOption()
    }

    private fun setupClickListeners() {
        binding.apply {
            // Security Question option click
            layoutSecurityQuestionOption.setPreventDoubleClick {
                selectSecurityQuestionOption()
            }

            radioSecurityQuestion.setPreventDoubleClick {
                selectSecurityQuestionOption()
            }

            // Email option click
            layoutEmailOption.setPreventDoubleClick {
                selectEmailOption()
            }

            radioEmailRecovery.setPreventDoubleClick {
                selectEmailOption()
            }

            // Cancel button
            btnCancel.setPreventDoubleClick {
                onCancel?.invoke()
                dismiss()
            }

            // Continue button
            btnContinue.setPreventDoubleClick {
                when (selectedMethod) {
                    RecoveryMethod.SECURITY_QUESTION -> {
                        onSecurityQuestionSelected?.invoke()
                        dismiss()
                    }
                    RecoveryMethod.EMAIL -> {
                        if (securityManager.hasRecoveryEmail()) {
                            onEmailRecoverySelected?.invoke()
                            dismiss()
                        } else {
                            displayToast(getString(R.string.user_has_not_set_up_recovery_email))
                        }
                    }
                }
            }
        }
    }

    private fun selectSecurityQuestionOption() {
        binding.radioSecurityQuestion.isChecked = true
        binding.radioEmailRecovery.isChecked = false
        selectedMethod = RecoveryMethod.SECURITY_QUESTION
    }

    private fun selectEmailOption() {
        binding.radioSecurityQuestion.isChecked = false
        binding.radioEmailRecovery.isChecked = true
        selectedMethod = RecoveryMethod.EMAIL
    }

    // Callback interfaces
    var onSecurityQuestionSelected: (() -> Unit)? = null
    var onEmailRecoverySelected: (() -> Unit)? = null
    var onCancel: (() -> Unit)? = null

    companion object {
        const val TAG = "PasswordRecoveryMethodDialogFragment"

        fun newInstance(): ForgotPasswordDialogFragment {
            return ForgotPasswordDialogFragment().apply {
                arguments = bundleOf()
            }
        }
        
        fun show(fragmentManager: FragmentManager, 
                 onSecurityQuestionSelected: () -> Unit,
                 onEmailRecoverySelected: () -> Unit,
                 onCancel: () -> Unit = {}): ForgotPasswordDialogFragment {
            return newInstance().apply {
                this.onSecurityQuestionSelected = onSecurityQuestionSelected
                this.onEmailRecoverySelected = onEmailRecoverySelected
                this.onCancel = onCancel
                show(fragmentManager, TAG)
            }
        }
    }
}