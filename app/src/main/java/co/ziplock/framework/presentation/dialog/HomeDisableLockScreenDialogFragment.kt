package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.FragmentDialogConfirmDisableLockScreenBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.setPreventDoubleClick

class HomeDisableLockScreenDialogFragment :
    BaseDialogFragment<FragmentDialogConfirmDisableLockScreenBinding>(R.layout.fragment_dialog_confirm_disable_lock_screen) {

    private var onClickDisableLockScreen: (() -> Unit)? = null
    private var onClickCancel: (() -> Unit)? = null

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo(
        isDialogCancelable = false
    )

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        binding.apply {
            btnDisable.setPreventDoubleClick {
                onClickDisableLockScreen?.invoke()
                dismiss()
            }
            btnCancel.setPreventDoubleClick {
                onClickCancel?.invoke()
                dismiss()
            }
        }
    }

    class Builder {
        private var onClickDisableLockScreenListener: () -> Unit = {}
        private var onClickCancelListener: () -> Unit = {}

        fun setOnClickDisableLockScreenButton(onClickDoIt: () -> Unit) =
            apply { this.onClickDisableLockScreenListener = onClickDoIt }

        fun setOnClickCancelButton(onClickCancel: () -> Unit) =
            apply { this.onClickCancelListener = onClickCancel }

        fun build() = HomeDisableLockScreenDialogFragment().apply {
            <EMAIL> = onClickDisableLockScreenListener
            <EMAIL> = onClickCancelListener
        }
    }
}