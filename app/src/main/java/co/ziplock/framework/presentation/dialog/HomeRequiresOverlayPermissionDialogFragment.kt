package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.FragmentDialogRequiresOverlayPermissionBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.setPreventDoubleClick

class HomeRequiresOverlayPermissionDialogFragment :
    BaseDialogFragment<FragmentDialogRequiresOverlayPermissionBinding>(R.layout.fragment_dialog_requires_overlay_permission) {

    private var onClickDoIt: (() -> Unit)? = null
    private var onClickCancel: (() -> Unit)? = null

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo(
        isDialogCancelable = false
    )

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        binding.apply {
            btnDoIt.setPreventDoubleClick {
                onClickDoIt?.invoke()
                dismiss()
            }
            btnCancel.setPreventDoubleClick {
                onClickCancel?.invoke()
                dismiss()
            }
        }
    }

    class Builder {
        private var onClickDoItListener: () -> Unit = {}
        private var onClickCancelListener: () -> Unit = {}

        fun setOnClickDoIt(onClickDoIt: () -> Unit) =
            apply { this.onClickDoItListener = onClickDoIt }

        fun setOnClickCancel(onClickCancel: () -> Unit) =
            apply { this.onClickCancelListener = onClickCancel }

        fun build() = HomeRequiresOverlayPermissionDialogFragment().apply {
            <EMAIL> = onClickDoItListener
            <EMAIL> = onClickCancelListener
        }
    }
}