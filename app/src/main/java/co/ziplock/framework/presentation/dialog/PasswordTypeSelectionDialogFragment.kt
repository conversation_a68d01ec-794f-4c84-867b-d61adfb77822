package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import androidx.core.os.bundleOf
import co.ziplock.R
import co.ziplock.databinding.DialogPasswordTypeSelectionBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.BundleKey
import co.ziplock.util.setPreventDoubleClick
import dagger.hilt.android.AndroidEntryPoint
import pion.datlt.libads.utils.adsuntils.showLoadedNative

@AndroidEntryPoint
class PasswordTypeSelectionDialogFragment :
    BaseDialogFragment<DialogPasswordTypeSelectionBinding>(
        R.layout.dialog_password_type_selection,
    ) {
    private var selectedPasswordType: PasswordType =
        PasswordType.PIN

    override fun getDialogFragmentInfo(): DialogFragmentInfo =
        DialogFragmentInfo(
            isDialogCancelable = false,
        )

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        // Override dialog size to use wrap_content for height
        setDialogCanCancel()
        setupInitialSelection()
        showAds()
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        setupClickListeners()
    }

    private fun showAds() {
        showLoadedNative(
            spaceNameConfig = "Home-dlg-passtype",
            spaceName = "Home-dlg-passtype_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }

    private fun setupInitialSelection() {
        val initialPasswordType =
            arguments?.getString(BundleKey.KEY_SECURITY_TYPE) ?: PasswordType.PIN.name
        if (initialPasswordType == PasswordType.PATTERN.name) {
            selectPatternOption()
        } else {
            selectPinOption()
        }
    }

    private fun setupClickListeners() {
        binding.apply {
            // PIN option click
            layoutPinOption.setPreventDoubleClick {
                selectPinOption()
            }

            radioPinOption.setPreventDoubleClick {
                selectPinOption()
            }

            // Pattern option click
            layoutPatternOption.setPreventDoubleClick {
                selectPatternOption()
            }

            radioPatternOption.setPreventDoubleClick {
                selectPatternOption()
            }

            // Cancel button
            btnCancel.setPreventDoubleClick {
                onCancel?.invoke()
                dismiss()
            }

            // Apply button
            btnApply.setPreventDoubleClick {
                applySelection()
            }
        }
    }

    private fun selectPinOption() {
        binding.radioPinOption.isChecked = true
        binding.radioPatternOption.isChecked = false
        selectedPasswordType = PasswordType.PIN
    }

    private fun selectPatternOption() {
        binding.radioPinOption.isChecked = false
        binding.radioPatternOption.isChecked = true
        selectedPasswordType = PasswordType.PATTERN
    }

    private fun applySelection() {
        // Dismiss the dialog
        dismiss()

        // Notify the listener if needed
        onPasswordTypeSelected?.invoke(selectedPasswordType)
    }

    // Callback interface for password type selection
    var onPasswordTypeSelected: ((PasswordType) -> Unit)? = null
    var onCancel: (() -> Unit)? = null

    companion object {
        const val TAG = "PasswordTypeSelectionDialogFragment"

        fun newInstance(initialPasswordType: PasswordType): PasswordTypeSelectionDialogFragment =
            PasswordTypeSelectionDialogFragment().apply {
                arguments =
                    bundleOf(
                        BundleKey.KEY_SECURITY_TYPE to initialPasswordType.name,
                    )
            }
    }
}

enum class PasswordType {
    PIN,
    PATTERN,
}
