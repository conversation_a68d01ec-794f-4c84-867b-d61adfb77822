package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.andrognito.patternlockview.PatternLockView
import com.andrognito.patternlockview.listener.PatternLockViewListener
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import co.ziplock.R
import co.ziplock.databinding.DialogPatternVerificationBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.setPreventDoubleClick
import javax.inject.Inject

@AndroidEntryPoint
class PatternVerificationDialogFragment : BaseDialogFragment<DialogPatternVerificationBinding>(
    R.layout.dialog_pattern_verification
) {
    @Inject
    lateinit var securityManager: SecurityManager

    private var errorClearJob: Job? = null
    private var attemptCount = 0
    private val MAX_ATTEMPTS = 5
    private var isLockoutActive = false
    private var lockoutJob: Job? = null

    override fun getDialogFragmentInfo(): DialogFragmentInfo {
        return DialogFragmentInfo(
            isDialogCancelable = false
        )
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        setDialogCanCancel()
        setupPatternLockView()

        // Check if we're in a lockout period
        checkLockoutStatus()
    }
    
    private fun checkLockoutStatus() {
        if (securityManager.isLockedOut()) {
            // We're in a lockout period, start the countdown
            isLockoutActive = true
            binding.patternLockView.isEnabled = false
            startLockoutCountdown()
        }
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        setupClickListeners()
    }

    private fun setupPatternLockView() {
        binding.patternLockView.apply {
            addPatternLockListener(object : PatternLockViewListener {
                override fun onStarted() {
                    if (!isLockoutActive) {
                        clearErrorState()
                    }
                }

                override fun onProgress(progressPattern: MutableList<PatternLockView.Dot>?) {
                    if (isLockoutActive) {
                        // If in lockout, clear the pattern immediately
                        binding.patternLockView.clearPattern()
                        return
                    }
                    
                    progressPattern?.let { dots ->
                        val patternIndices = dots.map { it.id }
                        // No need to do anything during progress
                    }
                }

                override fun onComplete(pattern: MutableList<PatternLockView.Dot>?) {
                    if (isLockoutActive) {
                        // If in lockout, clear the pattern immediately
                        binding.patternLockView.clearPattern()
                        return
                    }
                    
                    pattern?.let { dots ->
                        val patternIndices = dots.map { it.id }
                        verifyPattern(patternIndices)
                    }
                }

                override fun onCleared() {
                    // No action needed
                }
            })
        }
    }

    private fun setupClickListeners() {
        binding.apply {
            btnCancel.setPreventDoubleClick {
                onCancel?.invoke()
                dismiss()
            }

            btnForgotPassword.setPreventDoubleClick {
                onForgotPassword?.invoke()
                dismiss()
            }
        }
    }

    private fun verifyPattern(pattern: List<Int>) {
        if (pattern.size < 4) {
            showError(getString(R.string.connect_at_least_4_dots))
            resetPatternAfterDelay()
            return
        }

        if (securityManager.verifyPattern(pattern)) {
            // Pattern is correct
            onPatternVerified?.invoke()
            dismiss()
        } else {
            // Pattern is incorrect
            attemptCount++
            
            if (attemptCount >= MAX_ATTEMPTS) {
                // Too many failed attempts - start lockout
                startLockout()
            } else {
                // Show error message with remaining attempts
                val attemptsLeft = MAX_ATTEMPTS - attemptCount
                val errorMessage = getString(R.string.incorrect_pattern_attempts_left, attemptsLeft)
                showError(errorMessage)
                resetPatternAfterDelay()
            }
        }
    }

    private fun startLockout() {
        isLockoutActive = true
        
        // Set pattern view to disabled state
        binding.patternLockView.isEnabled = false
        
        // Cancel any existing error clear job
        errorClearJob?.cancel()
        
        // Start lockout countdown
        startLockoutCountdown()
    }
    
    private fun startLockoutCountdown() {
        // Cancel any existing lockout job
        lockoutJob?.cancel()
        
        // Start a new countdown job
        lockoutJob = lifecycleScope.launch {
            while (securityManager.isLockedOut()) {
                val remainingSeconds = securityManager.getRemainingLockoutTimeSeconds()
                
                // Show lockout message with countdown
                showError(getString(R.string.locked_out_countdown, remainingSeconds))
                
                delay(1000) // Update every second
            }
            
            // When lockout is over
            binding.patternLockView.isEnabled = true
            binding.patternLockView.clearPattern()
            clearErrorState()
            attemptCount = 0
            isLockoutActive = false
        }
    }

    private fun showError(message: String) {
        binding.apply {
            tvSubtitle.text = message
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
            // Set pattern to error state but don't clear it immediately
            binding.patternLockView.setViewMode(PatternLockView.PatternViewMode.WRONG)
        }
    }

    private fun resetPatternAfterDelay() {
        errorClearJob?.cancel()
        errorClearJob = lifecycleScope.launch {
            delay(2000)
            binding.patternLockView.clearPattern()
            clearErrorState()
        }
    }

    private fun clearErrorState() {
        binding.apply {
            tvSubtitle.text = getString(R.string.draw_your_current_pattern)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        errorClearJob?.cancel()
        lockoutJob?.cancel()
    }

    // Callback interfaces
    var onPatternVerified: (() -> Unit)? = null
    var onCancel: (() -> Unit)? = null
    var onForgotPassword: (() -> Unit)? = null
    //var onTooManyAttempts: (() -> Unit)? = null

    companion object {
        const val TAG = "PatternVerificationDialogFragment"

        fun newInstance(): PatternVerificationDialogFragment {
            return PatternVerificationDialogFragment()
        }
    }
}