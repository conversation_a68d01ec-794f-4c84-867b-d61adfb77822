package co.ziplock.framework.presentation.dialog

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.widget.EditText
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import co.ziplock.R
import co.ziplock.databinding.DialogPinVerificationBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.edittext.AsteriskPasswordTransformationMethod
import co.ziplock.util.setPreventDoubleClick
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import javax.inject.Inject

@AndroidEntryPoint
class PinVerificationDialogFragment :
    BaseDialogFragment<DialogPinVerificationBinding>(
        R.layout.dialog_pin_verification,
    ) {
    @Inject
    lateinit var securityManager: SecurityManager

    private lateinit var pinInputs: List<EditText>
    private var currentPin = ""
    private var isPinVisible = false
    private var errorClearJob: Job? = null
    private var attemptCount = 0
    private var isLockoutActive = false
    private var lockoutJob: Job? = null

    override fun getDialogFragmentInfo(): DialogFragmentInfo =
        DialogFragmentInfo(
            isDialogCancelable = false,
        )

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        setDialogCanCancel()
        setupPinInputs()
        // Check if we're in a lockout period
        checkLockoutStatus()
        showAds()
    }

    private fun showAds() {
        showLoadedNative(
            spaceNameConfig = "Home-dlg-passtype",
            spaceName = "Home-dlg-passtype_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }

    private fun checkLockoutStatus() {
        if (securityManager.isLockedOut()) {
            // We're in a lockout period, start the countdown
            isLockoutActive = true
            setPinInputsEnabled(false)
            startLockoutCountdown()
        }
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        setupClickListeners()
    }

    private fun setupPinInputs() {
        pinInputs =
            listOf(
                binding.etPin1,
                binding.etPin2,
                binding.etPin3,
                binding.etPin4,
            )

        pinInputs.forEach { editText ->
            editText.transformationMethod = AsteriskPasswordTransformationMethod()
        }

        pinInputs.forEachIndexed { index, editText ->
            editText.addTextChangedListener(
                object : TextWatcher {
                    override fun beforeTextChanged(
                        s: CharSequence?,
                        start: Int,
                        count: Int,
                        after: Int,
                    ) {
                    }

                    override fun onTextChanged(
                        s: CharSequence?,
                        start: Int,
                        before: Int,
                        count: Int,
                    ) {}

                    override fun afterTextChanged(s: Editable?) {
                        // Skip if we're in lockout period
                        if (isLockoutActive) return

                        val text = s.toString()

                        // Only allow digits
                        if (text.isNotEmpty() && !text.matches(Regex("\\d"))) {
                            editText.setText("")
                            return
                        }

                        // Move to next input if current is filled
                        if (text.isNotEmpty() && index < pinInputs.size - 1) {
                            pinInputs[index + 1].requestFocus()
                        }

                        // Update current PIN
                        updateCurrentPin()
                    }
                },
            )

            // Handle backspace to move to previous input
            editText.setOnKeyListener { _, keyCode, event ->
                if (isLockoutActive) return@setOnKeyListener true

                if (keyCode == KeyEvent.KEYCODE_DEL && editText.text.isEmpty() && index > 0) {
                    pinInputs[index - 1].requestFocus()
                    true
                } else {
                    false
                }
            }
        }
    }

    private fun setupClickListeners() {
        binding.apply {
            btnCancel.setPreventDoubleClick {
                onCancel?.invoke()
                dismiss()
            }

            btnTogglePinVisibility.setPreventDoubleClick {
                if (!isLockoutActive) {
                    togglePinVisibility()
                }
            }

            btnForgotPassword.setPreventDoubleClick {
                onForgotPassword?.invoke()
                dismiss()
            }

            btnApply.setPreventDoubleClick {
                verifyPin()
            }
        }
    }

    private fun updateCurrentPin() {
        currentPin = pinInputs.joinToString("") { it.text.toString() }
    }

    private fun verifyPin() {
        if (securityManager.verifyPin(currentPin)) {
            // PIN is correct
            onPinVerified?.invoke()
            dismiss()
        } else {
            // PIN is incorrect
            attemptCount++

            if (attemptCount >= MAX_PIN_ATTEMPTS) {
                // Too many failed attempts - start lockout
                startLockout()
            } else {
                // Show error message with remaining attempts
                val attemptsLeft = MAX_PIN_ATTEMPTS - attemptCount
                val errorMessage = getString(R.string.incorrect_pin_attempts_left, attemptsLeft)
                showError(errorMessage)

                // Cancel any existing error clear job
                errorClearJob?.cancel()

                // Clear PIN inputs after delay
                errorClearJob =
                    lifecycleScope.launch {
                        delay(2000)
                        clearPinInputs()
                        clearErrorState()
                    }
            }
        }
    }

    private fun startLockout() {
        isLockoutActive = true

        // Disable PIN inputs
        setPinInputsEnabled(false)

        // Cancel any existing error clear job
        errorClearJob?.cancel()

        // Start lockout countdown
        startLockoutCountdown()
    }

    private fun startLockoutCountdown() {
        // Cancel any existing lockout job
        lockoutJob?.cancel()

        // Start a new countdown job
        lockoutJob =
            lifecycleScope.launch {
                while (securityManager.isLockedOut()) {
                    val remainingSeconds = securityManager.getRemainingLockoutTimeSeconds()

                    // Show lockout message with countdown
                    showError(getString(R.string.locked_out_countdown, remainingSeconds))

                    delay(1000) // Update every second
                }

                // When lockout is over
                setPinInputsEnabled(true)
                clearPinInputs()
                clearErrorState()
                attemptCount = 0
                isLockoutActive = false
            }
    }

    private fun setPinInputsEnabled(enabled: Boolean) {
        pinInputs.forEach { it.isEnabled = enabled }
    }

    private fun showError(message: String) {
        binding.apply {
            tvTitle.text = message
            tvTitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))

            // Highlight incorrect inputs in PIN inputs
            pinInputs.forEach { editText ->
                editText.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
            }
        }
    }

    private fun clearPinInputs() {
        pinInputs.forEach {
            it.setText("")
            it.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        }
        pinInputs[0].requestFocus()
        currentPin = ""
    }

    private fun clearErrorState() {
        binding.apply {
            tvTitle.text = getString(R.string.verify_your_pin)
            tvTitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        }

        pinInputs.forEach { editText ->
            editText.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        }
    }

    private fun togglePinVisibility() {
        isPinVisible = !isPinVisible

        binding.btnTogglePinVisibility.setImageResource(
            if (isPinVisible) R.drawable.ic_visibility else R.drawable.ic_visibility_off,
        )

        pinInputs.forEach { editText ->
            editText.transformationMethod =
                if (isPinVisible) {
                    null
                } else {
                    AsteriskPasswordTransformationMethod()
                }
            // Move cursor to end
            editText.setSelection(editText.text.length)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        errorClearJob?.cancel()
        lockoutJob?.cancel()
    }

    // Callback interfaces
    var onPinVerified: (() -> Unit)? = null
    var onCancel: (() -> Unit)? = null
    var onForgotPassword: (() -> Unit)? = null
    // var onTooManyAttempts: (() -> Unit)? = null

    companion object {
        const val MAX_PIN_ATTEMPTS = 5
        const val TAG = "PinVerificationDialogFragment"

        fun newInstance(): PinVerificationDialogFragment = PinVerificationDialogFragment()
    }
}
