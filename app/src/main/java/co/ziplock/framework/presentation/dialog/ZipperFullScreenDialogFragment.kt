package co.ziplock.framework.presentation.dialog

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.DialogZipperFullScreenPreviewBinding
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.framework.presentation.model.FontData
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.BundleKey
import co.ziplock.util.displayToast
import co.ziplock.util.parcelable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ZipperFullScreenDialogFragment : BaseDialogFragment<DialogZipperFullScreenPreviewBinding>(
    R.layout.dialog_zipper_full_screen_preview
) {
    
    private var selectedZipper: ZipperResponse? = null
    private var selectedWallpaper: WallpaperResponse? = null
    private var selectedLocalWallpaper: LocalImageData? = null
    private var selectedBackground: BackgroundResponse? = null
    private var selectedLocalBackground: LocalImageData? = null
    private var selectedSound: SoundResponse? = null
    private var selectedRow: RowResponse? = null
    private var selectedFontFamilyId: String? = null
    private var selectedFontColorId: String? = null

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo(
        screenWidthPercent = 100f,
        screenHeightPercent = 100f,
        isDialogCancelable = true
    )

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        
        // Get data from arguments
        selectedZipper = arguments?.parcelable(BundleKey.ZIPPER_DATA)
        selectedWallpaper = arguments?.parcelable(BundleKey.WALLPAPER_DATA)
        selectedLocalWallpaper = arguments?.parcelable(BundleKey.LOCAL_WALLPAPER_DATA)
        selectedBackground = arguments?.parcelable(BundleKey.BACKGROUND_DATA)
        selectedLocalBackground = arguments?.parcelable(BundleKey.LOCAL_BACKGROUND_DATA)
        selectedSound = arguments?.parcelable(BundleKey.SOUND_DATA)
        selectedRow = arguments?.parcelable(BundleKey.ROW_DATA)
        selectedFontFamilyId = arguments?.getString(BundleKey.FONT_FAMILY_ID)
        selectedFontColorId = arguments?.getString(BundleKey.FONT_COLOR_ID)
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        setupZipperPreview()
        loadPreviewData()
        setupClickListeners()
    }

    private fun setupClickListeners() {
    }

    private fun setupZipperPreview() {
        
        // Apply saved settings
        binding.zipperPreview.setVibrationEnabled(prefUtil.vibrationEnabled)
        binding.zipperPreview.setSoundEnabled(prefUtil.soundEnabled)
        binding.zipperPreview.setPrefUtil(prefUtil)
        binding.zipperPreview.setShowFlashlightIcon(show = false)

        binding.zipperPreview.setOnUnlockListener {
            // Unlock the zipper
            dismiss()
            displayToast(getString(R.string.unlocked))
        }
    }

    private fun loadPreviewData() {
        loadZipperImage()
        loadWallpaperImage()
        loadBackgroundImage()
        loadSoundData()
        loadRowData()
        loadFontAndColorSettings()
    }

    private fun loadZipperImage() {
        selectedZipper?.let { zipperResponse ->
            binding.zipperPreview.setZipperResponse(zipperResponse)
        } ?: run {
            // Fallback to saved zipper response
            val savedZipperResponse = prefUtil.selectedZipperResponse
            if (savedZipperResponse != null) {
                binding.zipperPreview.setZipperResponse(savedZipperResponse)
            }
        }
    }

    private fun loadWallpaperImage() {
        selectedWallpaper?.fileUrl?.let { wallpaperUrl ->
            loadWallpaperImageFromUrl(wallpaperUrl)
        } ?: selectedLocalWallpaper?.filePath?.let { wallpaperPath ->
            loadWallpaperImageFromPath(wallpaperPath)
        } ?: run {
            // Fallback to saved wallpaper response
            val savedWallpaperResponse = prefUtil.selectedWallpaperResponse
            savedWallpaperResponse?.fileUrl?.let { wallpaperPath ->
                if (wallpaperPath.startsWith("http")) {
                    loadWallpaperImageFromUrl(wallpaperPath)
                } else {
                    loadWallpaperImageFromPath(wallpaperPath)
                }
            }
        }
    }

    private fun loadBackgroundImage() {
        selectedBackground?.fileUrl?.let { backgroundUrl ->
            loadBackgroundImageFromUrl(backgroundUrl)
        } ?: selectedLocalBackground?.filePath?.let { backgroundPath ->
            loadBackgroundImageFromPath(backgroundPath)
        } ?: run {
            // Fallback to saved background response
            val savedBackgroundResponse = prefUtil.selectedBackgroundResponse
            savedBackgroundResponse?.fileUrl?.let { backgroundPath ->
                if (backgroundPath.startsWith("http")) {
                    loadBackgroundImageFromUrl(backgroundPath)
                } else {
                    loadBackgroundImageFromPath(backgroundPath)
                }
            }
        }
    }

    private fun loadSoundData() {
        selectedSound?.fileUrl?.let { soundUrl ->
            binding.zipperPreview.setSoundUrl(soundUrl)
        } ?: run {
            // Fallback to saved sound URL
            val savedSoundUrl = prefUtil.selectedSoundUrl
            if (!savedSoundUrl.isNullOrEmpty()) {
                binding.zipperPreview.setSoundUrl(savedSoundUrl)
            }
        }
    }

    private fun loadRowData() {
        selectedRow?.let { row ->
            binding.zipperPreview.setRowResponse(row)
        } ?: run {
            // Fallback to saved row data
            prefUtil.selectedRowResponse?.let { row ->
                binding.zipperPreview.setRowResponse(row)
            }
        }
    }

    private fun loadFontAndColorSettings() {
        // Load font family
        val fontFamilyId = selectedFontFamilyId ?: prefUtil.selectedFontFamilyId
        val fontFamily = fontFamilyId?.let { id ->
            FontData.fontFamilies.find { it.id == id }
        }

        // Load font color
        val fontColorId = selectedFontColorId ?: prefUtil.selectedFontColorId
        val fontColor = fontColorId?.let { id ->
            FontData.fontColors.find { it.id == id }
        }

        // Apply font and color settings
        binding.zipperPreview.applyFontAndColorSettings(
            fontFamily?.fontResource,
            fontColor?.colorRes
        )
    }

    private fun loadWallpaperImageFromUrl(imageUrl: String) {
        Glide.with(this)
            .asBitmap()
            .load(imageUrl)
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .skipMemoryCache(false)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setWallpaperBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            })
    }

    private fun loadWallpaperImageFromPath(imagePath: String) {
        Glide.with(this)
            .asBitmap()
            .load(imagePath)
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .skipMemoryCache(true)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setWallpaperBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            })
    }

    private fun loadBackgroundImageFromUrl(imageUrl: String) {
        Glide.with(this)
            .asBitmap()
            .load(imageUrl)
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .skipMemoryCache(true)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setBackgroundBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            })
    }

    private fun loadBackgroundImageFromPath(imagePath: String) {
        Glide.with(this)
            .asBitmap()
            .load(imagePath)
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .skipMemoryCache(true)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setBackgroundBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            })
    }

    companion object {
        const val TAG = "ZipperFullScreenDialogFragment"

        fun newInstance(
            zipperResponse: ZipperResponse? = null,
            wallpaperResponse: WallpaperResponse? = null,
            localWallpaper: LocalImageData? = null,
            backgroundResponse: BackgroundResponse? = null,
            localBackground: LocalImageData? = null,
            soundResponse: SoundResponse? = null,
            rowResponse: RowResponse? = null,
            fontFamilyId: String? = null,
            fontColorId: String? = null,
        ): ZipperFullScreenDialogFragment {
            return ZipperFullScreenDialogFragment().apply {
                arguments = Bundle().apply {
                    zipperResponse?.let { putParcelable(BundleKey.ZIPPER_DATA, it) }
                    wallpaperResponse?.let { putParcelable(BundleKey.WALLPAPER_DATA, it) }
                    localWallpaper?.let { putParcelable(BundleKey.LOCAL_WALLPAPER_DATA, it) }
                    backgroundResponse?.let { putParcelable(BundleKey.BACKGROUND_DATA, it) }
                    localBackground?.let { putParcelable(BundleKey.LOCAL_BACKGROUND_DATA, it) }
                    soundResponse?.let { putParcelable(BundleKey.SOUND_DATA, it) }
                    rowResponse?.let { putParcelable(BundleKey.ROW_DATA, it) }
                    fontFamilyId?.let { putString(BundleKey.FONT_FAMILY_ID, it) }
                    fontColorId?.let { putString(BundleKey.FONT_COLOR_ID, it) }
                }
            }
        }
    }
}