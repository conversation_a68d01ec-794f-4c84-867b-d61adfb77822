package co.ziplock.framework.presentation.edit_image

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentEditImageBinding
import co.ziplock.framework.presentation.common.BaseFragment

@AndroidEntryPoint
class EditImageFragment : BaseFragment<FragmentEditImageBinding, EditImageViewModel>(
    FragmentEditImageBinding::inflate,
    EditImageViewModel::class.java
) {

    override fun init(view: View) {
        setupBackButton()
        loadSelectedPhoto()
        setupButtons()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeUiState()
    }

    companion object {
        const val TAG = "EditImageFragment"
    }
}