package co.ziplock.framework.presentation.edit_image

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.os.Bundle
import android.widget.Toast
import androidx.core.graphics.createBitmap
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import co.ziplock.R
import co.ziplock.framework.presentation.common.launchIO
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.pickphoto.DevicePhoto
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.parcelable
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun EditImageFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
    onSystemBackEvent {
        backEvent()
    }
}

fun EditImageFragment.backEvent() {
    safeNavigateUp(R.id.editImageFragment)
}

fun EditImageFragment.loadSelectedPhoto() {
    // Get selected photo from arguments
    val selectedPhoto = arguments?.parcelable<DevicePhoto>(BundleKey.KEY_SELECTED_PHOTO)
    selectedPhoto?.let {
        viewModel.setSelectedPhoto(it)
        displaySelectedPhoto(it)
    }

    // Get save context from arguments
    val saveContext = arguments?.getString(BundleKey.KEY_SAVE_CONTEXT)
    saveContext?.let {
        viewModel.setSaveContext(it)
    }
}

fun EditImageFragment.setupButtons() {
    binding.btnApply.setPreventDoubleClick {
        captureAndSavePhotoViewImage()
    }

    binding.btnCancel.setPreventDoubleClick {
        showLoadedInter(
            spaceNameConfig = "ptcrop-cancel",
            spaceName = "ptcrop-1ID_interstitial",
            destinationToShowAds = R.id.editImageFragment,
            isShowLoadingView = true,
            isScreenType = false,
            navOrBack = {
                backEvent()
            },
            onCloseAds = {},
        )
    }
}

fun EditImageFragment.observeUiState() {
    // Observe loading state
    viewModel.uiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressBar.isVisible = isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

private fun EditImageFragment.displaySelectedPhoto(photo: DevicePhoto) {
    launchIO {
        val bitmap =
            Glide
                .with(this@displaySelectedPhoto)
                .asBitmap()
                .load(photo.uri)
                .override(binding.ivSelectedPhoto.width, binding.ivSelectedPhoto.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .centerCrop()
                .submit()
                .get()

        withContext(Dispatchers.Main) {
            val copyBitmap = bitmap?.copy(Bitmap.Config.RGB_565, true)
            binding.ivSelectedPhoto.setImageBitmap(copyBitmap)
        }
    }
}

fun EditImageFragment.captureAndSavePhotoViewImage() {
    val photoView = binding.ivSelectedPhoto
    val drawable = photoView.drawable

    if (drawable == null) {
        Toast.makeText(requireContext(), "No image to save", Toast.LENGTH_SHORT).show()
        return
    }

    viewLifecycleOwner.lifecycleScope.launch {
        try {
            // Create bitmap with the displayed size and position
            val bitmap =
                withContext(Dispatchers.IO) {
                    getCurrentDisplayedBitmap()
                }

            if (bitmap != null) {
                // Save the bitmap using ViewModel based on context
                viewModel.saveImageBasedOnContext(
                    bitmap = bitmap,
                    onSuccess = { savedPath ->
                        val saveContext = viewModel.uiState.value.saveContext
                        val folderName =
                            when (saveContext) {
                                Constant.SAVE_CONTEXT_BACKGROUND -> Constant.FILES_DIRECTORY_BACKGROUND_LOCAL
                                else -> Constant.FILES_DIRECTORY_WALLPAPER_LOCAL
                            }
                        Toast
                            .makeText(
                                requireContext(),
                                "Image saved to $folderName folder",
                                Toast.LENGTH_SHORT,
                            ).show()

                        navigateToEditLayoutFragment(savedPath)
                    },
                    onError = {
                        Toast
                            .makeText(requireContext(), "Failed to save image", Toast.LENGTH_SHORT)
                            .show()
                    },
                )
            } else {
                Toast
                    .makeText(requireContext(), "Failed to create image", Toast.LENGTH_SHORT)
                    .show()
            }
        } catch (e: Exception) {
            Toast
                .makeText(requireContext(), "Error saving image: ${e.message}", Toast.LENGTH_SHORT)
                .show()
        }
    }
}

fun EditImageFragment.getCurrentDisplayedBitmap(): Bitmap? {
    val matrix = binding.ivSelectedPhoto.imageMatrix
    val displayRect = binding.ivSelectedPhoto.attacher.displayRect ?: return null

    // Calculate the actual displayed area
    val left = 0f.coerceAtLeast(displayRect.left).toInt()
    val top = 0f.coerceAtLeast(displayRect.top).toInt()
    val right =
        binding.ivSelectedPhoto.width
            .toFloat()
            .coerceAtMost(displayRect.right)
            .toInt()
    val bottom =
        binding.ivSelectedPhoto.height
            .toFloat()
            .coerceAtMost(displayRect.bottom)
            .toInt()

    // Skip if there's nothing to display
    if (right <= left || bottom <= top) return null

    // Create a bitmap with only the visible dimensions
    val result = createBitmap(right - left, bottom - top)
    val canvas = Canvas(result)

    // Adjust matrix to account for the cropped area
    val adjustedMatrix = Matrix(matrix)
    adjustedMatrix.postTranslate(-left.toFloat(), -top.toFloat())
    canvas.setMatrix(adjustedMatrix)

    // Draw the filtered bitmap with current transformations
    canvas.drawBitmap(binding.ivSelectedPhoto.originalBitmap, 0f, 0f, null)

    return result
}

fun EditImageFragment.navigateToEditLayoutFragment(savedPath: String?) {
    val bundle = Bundle()
    val saveContext = viewModel.uiState.value.saveContext

    if (saveContext == Constant.SAVE_CONTEXT_BACKGROUND) {
        // Create LocalImageData for background from saved path
        savedPath?.let { path ->
            val localBackground =
                LocalImageData(
                    filePath = path,
                    fileName = path.substringAfterLast("/"),
                    lastModified = System.currentTimeMillis(),
                )
            commonViewModel.setEditLayoutLocalBackground(localBackground)
            bundle.putParcelable(BundleKey.LOCAL_BACKGROUND_DATA, localBackground)
        }
    } else {
        // Create LocalImageData for wallpaper from saved path (default case)
        savedPath?.let { path ->
            val localWallpaper =
                LocalImageData(
                    filePath = path,
                    fileName = path.substringAfterLast("/"),
                    lastModified = System.currentTimeMillis(),
                )
            commonViewModel.setEditLayoutLocalWallpaper(localWallpaper)
            bundle.putParcelable(BundleKey.LOCAL_WALLPAPER_DATA, localWallpaper)
        }
    }
    showLoadedInter(
        spaceNameConfig = "ptcrop-done",
        spaceName = "ptcrop-1ID_interstitial",
        destinationToShowAds = R.id.editImageFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safeNavInter(
                R.id.editImageFragment,
                R.id.action_editImageFragment_to_editLayoutFragment,
                bundle,
            )
        },
        onCloseAds = {},
    )
}

fun EditImageFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("ptcrop-cancel", "ptcrop-done"),
        spaceNameAds = "ptcrop-1ID_interstitial",
    )
    showLoadedNative(
        spaceNameConfig = "ptcrop",
        spaceName = "ptcrop_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
