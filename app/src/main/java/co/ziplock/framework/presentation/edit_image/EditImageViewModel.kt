package co.ziplock.framework.presentation.edit_image

import android.graphics.Bitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import co.ziplock.framework.presentation.model.pickphoto.DevicePhoto
import co.ziplock.repository.FileRepository
import co.ziplock.util.Constant
import co.ziplock.util.PrefUtil
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class EditImageViewModel @Inject constructor(
    private val fileRepository: FileRepository,
    private val prefUtil: PrefUtil
) : ViewModel() {

    private val _uiState = MutableStateFlow(EditImageUiState())
    val uiState: StateFlow<EditImageUiState> = _uiState.asStateFlow()

    fun setSelectedPhoto(photo: DevicePhoto?) {
        _uiState.value = _uiState.value.copy(selectedPhoto = photo)
    }
    
    fun setSaveContext(context: String) {
        _uiState.value = _uiState.value.copy(saveContext = context)
    }

    fun saveImageToWallpaperFolder(bitmap: Bitmap, onSuccess: (String?) -> Unit, onError: () -> Unit) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            withContext(Dispatchers.IO) {
                try {
                    val filename = "wallpaper_${System.currentTimeMillis()}"
                    val savedPath = fileRepository.saveBitmapToWallpaperFolder(bitmap, filename)
                    
                    withContext(Dispatchers.Main) {
                        _uiState.value = _uiState.value.copy(isLoading = false)
                        if (savedPath != null) {
                            Timber.d("Image saved to: $savedPath")
                            // Save wallpaper path to preferences
                            //prefUtil.selectedWallpaperImagePath = savedPath
                            onSuccess(savedPath)
                        } else {
                            onError()
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error saving image to wallpaper folder")
                    withContext(Dispatchers.Main) {
                        _uiState.value = _uiState.value.copy(isLoading = false)
                        onError()
                    }
                }
            }
        }
    }
    
    fun saveImageToBackgroundFolder(bitmap: Bitmap, onSuccess: (String?) -> Unit, onError: () -> Unit) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            withContext(Dispatchers.IO) {
                try {
                    val filename = "background_${System.currentTimeMillis()}"
                    val savedPath = fileRepository.saveBitmapToBackgroundFolder(bitmap, filename)
                    
                    withContext(Dispatchers.Main) {
                        _uiState.value = _uiState.value.copy(isLoading = false)
                        if (savedPath != null) {
                            Timber.d("Image saved to Background folder: $savedPath")
                            //Save background path to preferences
                            //prefUtil.selectedBackgroundImagePath = savedPath
                            onSuccess(savedPath)
                        } else {
                            onError()
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error saving image to background folder")
                    withContext(Dispatchers.Main) {
                        _uiState.value = _uiState.value.copy(isLoading = false)
                        onError()
                    }
                }
            }
        }
    }
    
    fun saveImageBasedOnContext(bitmap: Bitmap, onSuccess: (String?) -> Unit, onError: () -> Unit) {
        val currentContext = _uiState.value.saveContext
        when (currentContext) {
            Constant.SAVE_CONTEXT_BACKGROUND -> {
                saveImageToBackgroundFolder(bitmap, onSuccess, onError)
            }
            Constant.SAVE_CONTEXT_WALLPAPER -> {
                saveImageToWallpaperFolder(bitmap, onSuccess, onError)
            }
            else -> {
                // Default to wallpaper folder if no context is set
                saveImageToWallpaperFolder(bitmap, onSuccess, onError)
            }
        }
    }

    data class EditImageUiState(
        val selectedPhoto: DevicePhoto? = null,
        val isLoading: Boolean = false,
        val saveContext: String = Constant.SAVE_CONTEXT_WALLPAPER // Default to wallpaper
    )
}