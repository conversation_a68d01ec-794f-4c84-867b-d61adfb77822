package co.ziplock.framework.presentation.edit_layout

import android.view.View
import co.ziplock.databinding.FragmentEditLayoutBinding
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.edit_layout.adapter.CustomizationOptionsAdapter
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.BundleKey
import co.ziplock.util.parcelable
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

@AndroidEntryPoint
class EditLayoutFragment :
    BaseFragment<FragmentEditLayoutBinding, EditLayoutViewModel>(
        FragmentEditLayoutBinding::inflate,
        EditLayoutViewModel::class.java,
    ) {
    var customizationAdapter: CustomizationOptionsAdapter? = null
    var selectedZipper: ZipperResponse? = null
    var selectedWallpaper: WallpaperResponse? = null
    var selectedLocalWallpaper: LocalImageData? = null
    var selectedBackground: BackgroundResponse? = null
    var selectedLocalBackground: LocalImageData? = null
    var selectedSound: SoundResponse? = null
    var selectedRow: RowResponse? = null

    // Hot Trending Theme variables
    var hotTrendingThemes: List<HotTrendingThemeModel> = emptyList()
    var currentThemeIndex: Int = 0

    // Loading state tracking using StateFlow
    val _isAdapterLoading = MutableStateFlow(false)
    val _isPreviewLoading = MutableStateFlow(false)

    // Public properties to update loading states
    var isAdapterLoading: Boolean
        get() = _isAdapterLoading.value
        set(value) {
            _isAdapterLoading.update { value }
        }

    var isPreviewLoading: Boolean
        get() = _isPreviewLoading.value
        set(value) {
            _isPreviewLoading.update { value }
        }

    override fun init(view: View) {
        // Initialize data from EditLayoutUiState first, then fallback to arguments
        initializeDataFromEditLayoutUiState()

        binding.ivNextTheme.visibility = View.GONE
        binding.ivPreviousTheme.visibility = View.GONE

        // Fallback to arguments if EditLayoutUiState is empty
        if (selectedZipper == null) {
            selectedZipper =
                arguments?.parcelable<ZipperResponse>(BundleKey.ZIPPER_DATA)
        }
        if (selectedWallpaper == null) {
            selectedWallpaper =
                arguments?.parcelable<WallpaperResponse>(BundleKey.WALLPAPER_DATA)
        }
        if (selectedLocalWallpaper == null) {
            selectedLocalWallpaper =
                arguments?.parcelable<LocalImageData>(BundleKey.LOCAL_WALLPAPER_DATA)
        }
        if (selectedBackground == null) {
            selectedBackground =
                arguments?.parcelable<BackgroundResponse>(BundleKey.BACKGROUND_DATA)
        }
        if (selectedLocalBackground == null) {
            selectedLocalBackground =
                arguments?.parcelable<LocalImageData>(BundleKey.LOCAL_BACKGROUND_DATA)
        }
        if (selectedSound == null) {
            selectedSound =
                arguments?.parcelable<SoundResponse>(BundleKey.SOUND_DATA)
        }
        if (selectedRow == null) {
            selectedRow =
                arguments?.parcelable<RowResponse>(BundleKey.ROW_DATA)
        }

        setupRecyclerView()
        setupClickListeners()
        setupSystemBackEvent()

        // Handle data and save to EditLayoutUiState (not PrefUtil yet)
        handleZipperData()
        handleWallpaperData()
        handleBackgroundData()
        handleSoundData()
        handleRowData()

        // Load preview data
        loadZipperPreview()
        loadZipperItemPreview()
        loadWallpaperPreview()
        loadBackgroundPreview()
        loadSoundPreview()
        loadRowPreview()
        loadSoundItemPreview()
        loadAllPreviewData()
        observeHotTrendingThemes()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        // Observe ViewModel data if needed
        observeSettingsChanges()
        observeEditLayoutUiState()
        observeLoadingStates()
    }

    override fun onResume() {
        super.onResume()
        // Refresh EditLayoutUiState from PrefUtil in case we're coming back from EditImageFragment
        commonViewModel.refreshEditLayoutUiStateFromPrefUtil()
    }

    companion object {
        const val TAG = "EditLayoutFragment"
    }
}
