package co.ziplock.framework.presentation.edit_layout

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.dialog.ZipperFullScreenDialogFragment
import co.ziplock.framework.presentation.edit_layout.adapter.CustomizationOptionsAdapter
import co.ziplock.framework.presentation.model.CustomizationOptionType
import co.ziplock.framework.presentation.model.FontData
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pion.datlt.libads.utils.DialogNative
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun EditLayoutFragment.setupSystemBackEvent() {
    onSystemBackEvent {
        findNavController().navigateUp()
    }
}

fun EditLayoutFragment.setupRecyclerView() {
    customizationAdapter =
        CustomizationOptionsAdapter(
            onOptionClick = { optionType ->
                handleCustomizationOptionClick(optionType)
            },
            onVibrationToggle = { isEnabled ->
                handleVibrationToggle(isEnabled)
            },
            isVibrationEnabled = {
                prefUtil.vibrationEnabled
            },
            onLoadingStateChanged = { isLoading ->
                isAdapterLoading = isLoading
            },
        )

    binding.rvCustomizationOptions.apply {
        layoutManager = GridLayoutManager(requireContext(), 3)
        adapter = customizationAdapter
    }

    // Set customization options
    customizationAdapter?.submitList(CustomizationOptionType.entries)
}

fun EditLayoutFragment.setupClickListeners() {
    binding.ivBack.setPreventDoubleClick {
        findNavController().navigateUp()
    }

    binding.tvNext.setPreventDoubleClick {
        if (!isAdapterLoading && !isPreviewLoading) {
            navigateToLockscreenInfo()
        }
    }

    binding.ivPreview.setPreventDoubleClick {
        if (!isAdapterLoading && !isPreviewLoading) {
            startPreviewService()
        }
    }

    binding.ivPreviousTheme.setPreventDoubleClick {
        if (!isAdapterLoading && !isPreviewLoading) {
            navigateToPreviousTheme()
        }
    }

    binding.ivNextTheme.setPreventDoubleClick {
        if (!isAdapterLoading && !isPreviewLoading) {
            navigateToNextTheme()
        }
    }
}

fun EditLayoutFragment.loadZipperPreview() {
    // Setup ZipperView in preview mode with 60% progress
    binding.zipperPreview.setPreviewMode(true, previewProgress = 0.6f)
    // Set style if available from selected zipper
    // binding.zipperPreview.setStyle(co.ziplock.customview.StyleRow.STYLE_1)
    // Apply saved settings
    binding.zipperPreview.setVibrationEnabled(prefUtil.vibrationEnabled)
    binding.zipperPreview.setSoundEnabled(prefUtil.soundEnabled)
    binding.zipperPreview.setPrefUtil(prefUtil)
}

fun EditLayoutFragment.handleZipperData() {
    // Handle selected zipper
    selectedZipper?.let { zipper ->
        // Save zipper to CommonViewModel EditLayoutUiState
        commonViewModel.setEditLayoutZipper(zipper)

        // Set zipper response to preview
        binding.zipperPreview.setZipperResponse(zipper)
    }
}

fun EditLayoutFragment.refreshPreview() {
    // Refresh all settings in preview
    binding.zipperPreview.refreshSettings()
}

fun EditLayoutFragment.showInterChooseFeatureAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "edit-choosefunction",
        spaceName = "edit-1ID_interstitial",
        destinationToShowAds = R.id.editLayoutFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun EditLayoutFragment.handleCustomizationOptionClick(optionType: CustomizationOptionType) {
    when (optionType) {
        CustomizationOptionType.ZIPPER -> {
            showInterChooseFeatureAndDoAction {
                safeNavInter(
                    R.id.editLayoutFragment,
                    R.id.action_editLayoutFragment_to_zipperFragment,
                    bundle =
                        bundleOf(
                            BundleKey.KEY_FROM_RE_EDIT_FLOW to true,
                        ),
                )
            }
        }

        CustomizationOptionType.ROW -> {
            showInterChooseFeatureAndDoAction {
                safeNavInter(
                    R.id.editLayoutFragment,
                    R.id.action_editLayoutFragment_to_rowFragment,
                    bundle =
                        bundleOf(
                            BundleKey.KEY_FROM_RE_EDIT_FLOW to true,
                        ),
                )
            }
        }

        CustomizationOptionType.SOUND -> {
            showInterChooseFeatureAndDoAction {
                safeNavInter(
                    R.id.editLayoutFragment,
                    R.id.action_editLayoutFragment_to_soundFragment,
                    bundle =
                        bundleOf(
                            BundleKey.KEY_FROM_RE_EDIT_FLOW to true,
                        ),
                )
            }
        }

        CustomizationOptionType.BACKGROUND -> {
            showInterChooseFeatureAndDoAction {
                safeNavInter(
                    R.id.editLayoutFragment,
                    R.id.action_editLayoutFragment_to_backgroundFragment,
                    bundle =
                        bundleOf(
                            BundleKey.KEY_FROM_RE_EDIT_FLOW to true,
                        ),
                )
            }
        }

        CustomizationOptionType.WALLPAPER -> {
            showInterChooseFeatureAndDoAction {
                safeNavInter(
                    R.id.editLayoutFragment,
                    R.id.action_editLayoutFragment_to_wallpaperFragment,
                    bundle =
                        bundleOf(
                            BundleKey.KEY_FROM_RE_EDIT_FLOW to true,
                        ),
                )
            }
        }

        CustomizationOptionType.VIBRATION -> {
            // Vibration is handled by the switch in the adapter
            // No additional navigation needed
        }
    }
}

fun EditLayoutFragment.handleVibrationToggle(isEnabled: Boolean) {
    // Save vibration preference
    prefUtil.vibrationEnabled = isEnabled

    // Update vibration setting in preview
    binding.zipperPreview.setVibrationEnabled(isEnabled)
}

fun EditLayoutFragment.navigateToLockscreenInfo() {
    showLoadedInter(
        spaceNameConfig = "edit-next",
        spaceName = "edit-1ID_interstitial",
        destinationToShowAds = R.id.editLayoutFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            val bundle =
                Bundle().apply {
                    selectedZipper?.let { putParcelable(BundleKey.ZIPPER_DATA, it) }
                    selectedWallpaper?.let { putParcelable(BundleKey.WALLPAPER_DATA, it) }
                    selectedLocalWallpaper?.let {
                        putParcelable(
                            BundleKey.LOCAL_WALLPAPER_DATA,
                            it,
                        )
                    }
                    selectedBackground?.let { putParcelable(BundleKey.BACKGROUND_DATA, it) }
                    selectedLocalBackground?.let {
                        putParcelable(
                            BundleKey.LOCAL_BACKGROUND_DATA,
                            it,
                        )
                    }
                    selectedSound?.let { putParcelable(BundleKey.SOUND_DATA, it) }
                    selectedRow?.let { putParcelable(BundleKey.ROW_DATA, it) }
                }
            safeNavInter(
                R.id.editLayoutFragment,
                R.id.action_editLayoutFragment_to_lockscreenInfoFragment,
                bundle,
            )
        },
        onCloseAds = {},
    )
}

fun EditLayoutFragment.startPreviewService() {
    showZipperFullScreenPreview()
}

fun EditLayoutFragment.showZipperFullScreenPreview() {
    showLoadedInter(
        spaceNameConfig = "edit-preview",
        spaceName = "edit-1ID_interstitial",
        destinationToShowAds = R.id.editLayoutFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            DialogNative.runWhenNativeDismiss {
                val dialog =
                    ZipperFullScreenDialogFragment.newInstance(
                        zipperResponse = selectedZipper,
                        wallpaperResponse = selectedWallpaper,
                        localWallpaper = selectedLocalWallpaper,
                        backgroundResponse = selectedBackground,
                        localBackground = selectedLocalBackground,
                        soundResponse = selectedSound,
                        rowResponse = selectedRow,
                    )

                dialog.show(parentFragmentManager, ZipperFullScreenDialogFragment.TAG)
            }
        },
        onCloseAds = {},
    )
}

fun EditLayoutFragment.handleWallpaperData() {
    // Handle remote wallpaper
    selectedWallpaper?.let { wallpaper ->
        // Save wallpaper to CommonViewModel EditLayoutUiState
        commonViewModel.setEditLayoutWallpaper(wallpaper)

        // Set wallpaper response object in the customization adapter
        customizationAdapter?.setWallpaperImage(wallpaper)
    }

    // Handle local wallpaper
    selectedLocalWallpaper?.let { localWallpaper ->
        // Save local wallpaper to CommonViewModel EditLayoutUiState
        commonViewModel.setEditLayoutLocalWallpaper(localWallpaper)

        // For local wallpaper, we don't have previewThumbnail, so pass null to use default icon
        customizationAdapter?.setWallpaperImage(null)
    }
}

fun EditLayoutFragment.handleBackgroundData() {
    // Handle remote background
    selectedBackground?.let { background ->
        // Save background to CommonViewModel EditLayoutUiState
        commonViewModel.setEditLayoutBackground(background)

        // Set background response object in the customization adapter
        customizationAdapter?.setBackgroundImage(background)
    }

    // Handle local background
    selectedLocalBackground?.let { localBackground ->
        // Save local background to CommonViewModel EditLayoutUiState
        commonViewModel.setEditLayoutLocalBackground(localBackground)

        // For local background, we don't have previewThumbnail, so pass null to use default icon
        customizationAdapter?.setBackgroundImage(null)
    }
}

fun EditLayoutFragment.handleSoundData() {
    // Handle selected sound
    selectedSound?.let { sound ->
        // Save sound to CommonViewModel EditLayoutUiState
        commonViewModel.setEditLayoutSound(sound)

        if (sound.id == Constant.NO_SOUND_ID) {
            // Handle "No Sound" selection
            customizationAdapter?.setSoundImage(null) // This will show the "No Sound" icon
            Timber.d("No Sound selected")
        } else {
            // Handle regular sound selection
            // Set sound preview thumbnail
            if (sound.thumbnailUrl.isNotEmpty()) {
                customizationAdapter?.setSoundImage(sound.thumbnailUrl)
            }
            Timber.d("Sound selected and saved: ${sound.name} - URL: ${sound.fileUrl} - Thumbnail: ${sound.thumbnailUrl}")
        }
    }
}

fun EditLayoutFragment.handleRowData() {
    // Handle selected row
    selectedRow?.let { row ->
        // Save row to CommonViewModel EditLayoutUiState
        commonViewModel.setEditLayoutRow(row)
        // Set row response in the customization adapter
        customizationAdapter?.setRowImage(row)
        Timber.d("Row selected and saved: Left: ${row.imageRowLeft}, Right: ${row.imageRowRight}, Thumbnail: ${row.previewThumbnail}")
    }
}

fun EditLayoutFragment.loadZipperItemPreview() {
    // Load zipper image from EditLayoutUiState first, then fallback to PrefUtil
    val editLayoutState = commonViewModel.editLayoutUiState.value
    val zipperResponse = editLayoutState.zipperResponse ?: prefUtil.selectedZipperResponse

    if (zipperResponse != null) {
        // Set zipper response in the customization adapter
        customizationAdapter?.setZipperImage(zipperResponse)

        // Set ZipperResponse for draw_height and image loading
        binding.zipperPreview.setZipperResponse(zipperResponse)
    }
}

fun EditLayoutFragment.loadWallpaperPreview() {
    // Load wallpaper image from EditLayoutUiState first, then fallback to PrefUtil
    val editLayoutState = commonViewModel.editLayoutUiState.value
    val wallpaperResponse = editLayoutState.wallpaperResponse ?: prefUtil.selectedWallpaperResponse
    val wallpaperPath =
        when {
            editLayoutState.wallpaperResponse != null -> editLayoutState.wallpaperResponse.fileUrl
            editLayoutState.localWallpaper != null -> editLayoutState.localWallpaper.filePath
            else -> prefUtil.selectedWallpaperResponse?.fileUrl
        }

    // Set wallpaper response object in the customization adapter for thumbnail loading
    customizationAdapter?.setWallpaperImage(wallpaperResponse)

    if (!wallpaperPath.isNullOrEmpty()) {
        // Load wallpaper image into preview section
        loadWallpaperIntoPreview(wallpaperPath)
    }
}

fun EditLayoutFragment.loadWallpaperIntoPreview(wallpaperPath: String) {
    if (wallpaperPath.isEmpty()) return

    isPreviewLoading = true
    Glide
        .with(this)
        .asBitmap()
        .load(wallpaperPath)
        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
        .skipMemoryCache(true)
        .into(
            object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?,
                ) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    // Load wallpaper into ZipperView instead of separate ImageView
                    binding.zipperPreview.setWallpaperBitmap(copyBitmap)
                    isPreviewLoading = false
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                    isPreviewLoading = false
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    isPreviewLoading = false
                }
            },
        )
}

fun EditLayoutFragment.loadBackgroundPreview() {
    // Load background image from EditLayoutUiState first, then fallback to PrefUtil
    val editLayoutState = commonViewModel.editLayoutUiState.value
    val backgroundResponse = editLayoutState.backgroundResponse ?: prefUtil.selectedBackgroundResponse
    val backgroundPath =
        when {
            editLayoutState.backgroundResponse != null -> editLayoutState.backgroundResponse.fileUrl
            editLayoutState.localBackground != null -> editLayoutState.localBackground.filePath
            else -> prefUtil.selectedBackgroundResponse?.fileUrl
        }

    // Set background response object in the customization adapter for thumbnail loading
    customizationAdapter?.setBackgroundImage(backgroundResponse)

    if (!backgroundPath.isNullOrEmpty()) {
        // Load background image into ZipperView preview
        loadBackgroundIntoPreview(backgroundPath)
    }
}

fun EditLayoutFragment.loadBackgroundIntoPreview(backgroundPath: String) {
    if (backgroundPath.isEmpty()) return

    isPreviewLoading = true
    Glide
        .with(this)
        .asBitmap()
        .load(backgroundPath)
        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
        .skipMemoryCache(true)
        .into(
            object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?,
                ) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    // Load background into ZipperView
                    binding.zipperPreview.setBackgroundBitmap(copyBitmap)
                    isPreviewLoading = false
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                    isPreviewLoading = false
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    // Handle error case
                    isPreviewLoading = false
                }
            },
        )
}

fun EditLayoutFragment.loadSoundPreview() {
    // Load sound URL from EditLayoutUiState first, then fallback to PrefUtil
    val editLayoutState = commonViewModel.editLayoutUiState.value
    val soundUrl = editLayoutState.soundResponse?.fileUrl ?: prefUtil.selectedSoundUrl

    if (!soundUrl.isNullOrEmpty()) {
        Timber.d("Loading sound URL: $soundUrl")
        // Apply sound URL to preview zipper if needed
        binding.zipperPreview.setSoundUrl(soundUrl)
    } else {
        Timber.d("No sound selected (No Sound option)")
        // Clear sound URL for "No Sound" option
        binding.zipperPreview.setSoundUrl(null)
    }
}

fun EditLayoutFragment.observeHotTrendingThemes() {
    lifecycleScope.launch {
        commonViewModel.hotTrendingUiState.collect { uiState ->
            if (!uiState.isLoading && uiState.hotTrendingThemes.isNotEmpty()) {
                val wasEmpty = hotTrendingThemes.isEmpty()
                hotTrendingThemes = uiState.hotTrendingThemes
                updateThemeNavigationVisibility()

                // Find current theme index based on selected items
                if (wasEmpty) {
                    currentThemeIndex = findCurrentThemeIndex()
                    if (currentThemeIndex == -1) {
                        // No matching theme found, just set index to 0 for navigation
                        currentThemeIndex = 0
                    }
                }
            }
        }
    }
}

fun EditLayoutFragment.updateThemeNavigationVisibility() {
    val hasThemes = hotTrendingThemes.isNotEmpty()
    //binding.ivPreviousTheme.visibility = if (hasThemes) View.VISIBLE else View.GONE
    //binding.ivNextTheme.visibility = if (hasThemes) View.VISIBLE else View.GONE

    // Hide theme navigation buttons
    binding.ivPreviousTheme.visibility = View.INVISIBLE
    binding.ivNextTheme.visibility = View.INVISIBLE

    // Update button states based on current position
    if (hasThemes) {
        updateThemeNavigationStates()
    }
}

fun EditLayoutFragment.updateThemeNavigationStates() {
    // Since we support wrapping, both buttons should always be enabled
    // But we can add visual feedback or other states if needed
    binding.ivPreviousTheme.alpha = 1.0f
    binding.ivNextTheme.alpha = 1.0f

    // Optional: Add theme counter or indicator
    Timber.d("Current theme: ${currentThemeIndex + 1}/${hotTrendingThemes.size}")
}

fun EditLayoutFragment.navigateToPreviousTheme() {
    if (hotTrendingThemes.isEmpty()) return

    currentThemeIndex =
        if (currentThemeIndex > 0) {
            currentThemeIndex - 1
        } else {
            hotTrendingThemes.size - 1 // Wrap to last theme
        }

    applyHotTrendingTheme(hotTrendingThemes[currentThemeIndex])
    updateThemeNavigationStates()
}

fun EditLayoutFragment.navigateToNextTheme() {
    if (hotTrendingThemes.isEmpty()) return

    currentThemeIndex =
        if (currentThemeIndex < hotTrendingThemes.size - 1) {
            currentThemeIndex + 1
        } else {
            0 // Wrap to first theme
        }

    applyHotTrendingTheme(hotTrendingThemes[currentThemeIndex])
    updateThemeNavigationStates()
}

fun EditLayoutFragment.applyHotTrendingTheme(theme: HotTrendingThemeModel) {
    // Apply zipper from theme
    theme.zipper?.let { zipper ->
        selectedZipper = zipper
        commonViewModel.setEditLayoutZipper(zipper)
        binding.zipperPreview.setZipperResponse(zipper)
        customizationAdapter?.setZipperImage(zipper)
    }

    // Apply wallpaper from theme
    theme.wallpaper?.let { wallpaper ->
        selectedWallpaper = wallpaper
        selectedLocalWallpaper = null // Clear local wallpaper when applying theme
        commonViewModel.setEditLayoutWallpaper(wallpaper)
        // Set wallpaper response object in the customization adapter for thumbnail loading
        customizationAdapter?.setWallpaperImage(wallpaper)
        wallpaper.fileUrl?.let { wallpaperUrl ->
            loadWallpaperIntoPreview(wallpaperUrl)
        }
    }

    // Apply background from theme
    theme.background?.let { background ->
        selectedBackground = background
        selectedLocalBackground = null // Clear local background when applying theme
        commonViewModel.setEditLayoutBackground(background)
        // Set background response object in the customization adapter for thumbnail loading
        customizationAdapter?.setBackgroundImage(background)
        background.fileUrl?.let { backgroundUrl ->
            // Load background into ZipperView preview
            loadBackgroundIntoPreview(backgroundUrl)
        }
    }

    // Apply sound from theme
    theme.sound?.let { sound ->
        selectedSound = sound
        commonViewModel.setEditLayoutSound(sound)
        binding.zipperPreview.setSoundUrl(sound.fileUrl)
        // Set sound preview thumbnail
        if (sound.thumbnailUrl.isNotEmpty()) {
            customizationAdapter?.setSoundImage(sound.thumbnailUrl)
        }
        Timber.d("Applied theme sound: ${sound.name} - URL: ${sound.fileUrl}")
    }

    // Apply row from theme
    theme.row?.let { row ->
        selectedRow = row
        // Load row data into ZipperView
        binding.zipperPreview.setRowResponse(row)
        commonViewModel.setEditLayoutRow(row)
        // Set row response in the customization adapter
        customizationAdapter?.setRowImage(row)
        Timber.d("Applied theme row: Thumbnail: ${row.previewThumbnail}")
    }

    Timber.d("Applied Hot Trending Theme: ${theme.id}")
}

fun EditLayoutFragment.findCurrentThemeIndex(): Int {
    // Try to find a theme that matches current selected items
    for (i in hotTrendingThemes.indices) {
        val theme = hotTrendingThemes[i]

        // Check if zipper matches
        val zipperMatches =
            when {
                selectedZipper != null && theme.zipper != null ->
                    selectedZipper!!.fileUrl == theme.zipper.fileUrl

                selectedZipper == null && theme.zipper == null -> true
                else -> false
            }

        // Check if wallpaper matches
        val wallpaperMatches =
            when {
                selectedWallpaper != null && theme.wallpaper != null ->
                    selectedWallpaper!!.fileUrl == theme.wallpaper.fileUrl

                selectedWallpaper == null && theme.wallpaper == null -> true
                else -> false
            }

        // Check if background matches
        val backgroundMatches =
            when {
                selectedBackground != null && theme.background != null ->
                    selectedBackground!!.fileUrl == theme.background.fileUrl

                selectedBackground == null && theme.background == null -> true
                else -> false
            }

        // Check if sound matches
        val soundMatches =
            when {
                selectedSound != null && theme.sound != null ->
                    selectedSound!!.fileUrl == theme.sound.fileUrl

                selectedSound == null && theme.sound == null -> true
                else -> false
            }

        // Check if row matches
        val rowMatches =
            when {
                selectedRow != null && theme.row != null ->
                    selectedRow!!.previewThumbnail == theme.row.previewThumbnail

                selectedRow == null && theme.row == null -> true
                else -> false
            }

        // If most components match, consider this the current theme
        if (zipperMatches && wallpaperMatches && backgroundMatches && soundMatches && rowMatches) {
            return i
        }
    }

    return -1 // No matching theme found
}

fun EditLayoutFragment.loadRowPreview() {
    // Load row data from EditLayoutUiState first, then fallback to PrefUtil
    val editLayoutState = commonViewModel.editLayoutUiState.value
    val rowResponse = editLayoutState.rowResponse ?: prefUtil.selectedRowResponse

    rowResponse?.let { row ->
        // Load row data into ZipperView
        binding.zipperPreview.setRowResponse(row)

        // Load row response into adapter
        customizationAdapter?.setRowImage(row)
    }
}

fun EditLayoutFragment.loadSoundItemPreview() {
    // Load sound preview image from EditLayoutUiState first, then fallback to PrefUtil
    val editLayoutState = commonViewModel.editLayoutUiState.value
    val soundResponse = editLayoutState.soundResponse

    when {
        soundResponse != null -> {
            if (soundResponse.id == Constant.NO_SOUND_ID) {
                // Show "No Sound" icon
                customizationAdapter?.setSoundImage(null)
            } else if (soundResponse.thumbnailUrl.isNotEmpty()) {
                // Use the thumbnail URL for sound preview
                customizationAdapter?.setSoundImage(soundResponse.thumbnailUrl)
            }
        }

        else -> {
            // Fallback to saved preferences
            val savedSoundImagePreviewPath = prefUtil.selectedSoundImagePreviewPath
            if (!savedSoundImagePreviewPath.isNullOrEmpty()) {
                customizationAdapter?.setSoundImage(savedSoundImagePreviewPath)
            } else {
                // Check if "No Sound" is selected (when selectedSoundUrl is null/empty)
                val selectedSoundUrl = prefUtil.selectedSoundUrl
                if (selectedSoundUrl.isNullOrEmpty()) {
                    // Show "No Sound" icon
                    customizationAdapter?.setSoundImage(null)
                }
            }
        }
    }
}

fun EditLayoutFragment.loadAllPreviewData() {
    // Comprehensive method to ensure all preview data is loaded into ZipperView
    val editLayoutState = commonViewModel.editLayoutUiState.value

    // Load row data from EditLayoutUiState first, then fallback to PrefUtil
    val rowResponse = editLayoutState.rowResponse ?: prefUtil.selectedRowResponse
    rowResponse?.let { row ->
        binding.zipperPreview.setRowResponse(row)
    }

    // Load sound data from EditLayoutUiState first, then fallback to PrefUtil
    val soundUrl = editLayoutState.soundResponse?.fileUrl ?: prefUtil.selectedSoundUrl
    binding.zipperPreview.setSoundUrl(soundUrl)

    // Ensure vibration setting is applied
    binding.zipperPreview.setVibrationEnabled(prefUtil.vibrationEnabled)

    // Apply any saved font and color settings
    val savedFontFamilyId = prefUtil.selectedFontFamilyId
    val savedFontColorId = prefUtil.selectedFontColorId

    if (savedFontFamilyId != null && savedFontColorId != null) {
        // Find font resources by ID and apply them
        val fontData = FontData
        val fontFamily = fontData.fontFamilies.find { it.id == savedFontFamilyId }
        val fontColor = fontData.fontColors.find { it.id == savedFontColorId }

        binding.zipperPreview.applyFontAndColorSettings(
            fontFamily?.fontResource,
            fontColor?.colorRes,
        )
    }
}

fun EditLayoutFragment.updateLoadingState(isLoading: Boolean) {
    binding.progressLoading.isVisible = isLoading

    // Enable/disable buttons based on loading state
    binding.tvNext.isEnabled = !isLoading
    binding.ivPreview.isEnabled = !isLoading
    binding.ivPreviousTheme.isEnabled = !isLoading
    binding.ivNextTheme.isEnabled = !isLoading

    // Update button alpha to show disabled state
    val alpha = if (isLoading) 0.5f else 1.0f
    binding.tvNext.alpha = alpha
    binding.ivPreview.alpha = alpha
    binding.ivPreviousTheme.alpha = alpha
    binding.ivNextTheme.alpha = alpha

    // Disable CustomizationAdapter clicks if adapter is initialized
    customizationAdapter?.setClickEnabled(!isLoading)
}

fun EditLayoutFragment.observeLoadingStates() {
    // Combine both loading states and observe changes
    combine(_isAdapterLoading, _isPreviewLoading) { adapterLoading, previewLoading ->
        adapterLoading || previewLoading
    }.flowWithLifecycle(viewLifecycleOwner.lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { isLoading ->
            updateLoadingState(isLoading)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLayoutFragment.observeSettingsChanges() {
    // Observe settings changes from EditLayoutViewModel
    viewModel.settingsTabUiState
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { settingsState ->
            // Refresh preview when settings change
            refreshPreview()
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLayoutFragment.observeEditLayoutUiState() {
    commonViewModel.editLayoutUiState
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { editLayoutState ->
            // Update selected items when EditLayoutUiState changes
            editLayoutState.zipperResponse?.let { zipper ->
                selectedZipper = zipper
                loadZipperPreview()
                loadZipperItemPreview()
            }

            editLayoutState.rowResponse?.let { row ->
                selectedRow = row
                loadRowPreview()
            }

            editLayoutState.wallpaperResponse?.let { wallpaper ->
                selectedWallpaper = wallpaper
                selectedLocalWallpaper = null // Clear local wallpaper
                loadWallpaperPreview()
            }

            editLayoutState.localWallpaper?.let { localWallpaper ->
                selectedLocalWallpaper = localWallpaper
                selectedWallpaper = null // Clear network wallpaper
                loadWallpaperPreview()
            }

            editLayoutState.backgroundResponse?.let { background ->
                selectedBackground = background
                selectedLocalBackground = null // Clear local background
                loadBackgroundPreview()
            }

            editLayoutState.localBackground?.let { localBackground ->
                selectedLocalBackground = localBackground
                selectedBackground = null // Clear network background
                loadBackgroundPreview()
            }

            editLayoutState.soundResponse?.let { sound ->
                selectedSound = sound
                loadSoundPreview()
                loadSoundItemPreview()
            }

            // Refresh all preview data
            loadAllPreviewData()
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLayoutFragment.initializeDataFromEditLayoutUiState() {
    val editLayoutState = commonViewModel.editLayoutUiState.value

    editLayoutState.zipperResponse?.let { selectedZipper = it }
    editLayoutState.rowResponse?.let { selectedRow = it }
    editLayoutState.wallpaperResponse?.let {
        selectedWallpaper = it
        selectedLocalWallpaper = null
    }
    editLayoutState.localWallpaper?.let {
        selectedLocalWallpaper = it
        selectedWallpaper = null
    }
    editLayoutState.backgroundResponse?.let {
        selectedBackground = it
        selectedLocalBackground = null
    }
    editLayoutState.localBackground?.let {
        selectedLocalBackground = it
        selectedBackground = null
    }
    editLayoutState.soundResponse?.let { selectedSound = it }
}

fun EditLayoutFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "edit-bot",
        spaceName = "edit-bot_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )

    showLoadedNative(
        spaceNameConfig = "edit-midd",
        spaceName = "edit-midd_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {},
    )

    safePreloadAds(
        listSpaceNameConfig = listOf("edit-next", "edit-preview", "edit-choosefunction"),
        spaceNameAds = "edit-1ID_interstitial",
    )
}
