package co.ziplock.framework.presentation.edit_layout

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SettingsManager
import co.ziplock.framework.presentation.model.CustomizationOptionType
import co.ziplock.framework.presentation.model.home.SettingsTabUiState
import co.ziplock.util.PrefUtil
import javax.inject.Inject

@HiltViewModel
class EditLayoutViewModel @Inject constructor(
    private val prefUtil: PrefUtil,
    private val settingsManager: SettingsManager
) : BaseViewModel() {
    
    private val _selectedZipper = MutableLiveData<ZipperResponse?>()
    val selectedZipper: LiveData<ZipperResponse?> = _selectedZipper
    
    private val _customizationOptions = MutableLiveData<List<CustomizationOptionType>>()
    val customizationOptions: LiveData<List<CustomizationOptionType>> = _customizationOptions
    
    private val _selectedCustomizationOption = MutableLiveData<CustomizationOptionType?>()
    val selectedCustomizationOption: LiveData<CustomizationOptionType?> = _selectedCustomizationOption
    
    // Settings UI State
    private val _settingsTabUiState = MutableStateFlow(SettingsTabUiState())
    val settingsTabUiState: StateFlow<SettingsTabUiState> = _settingsTabUiState.asStateFlow()
    
    init {
        loadCustomizationOptions()
        loadSettings()
        observeSettingsChanges()
    }
    
    private fun observeSettingsChanges() {
        settingsManager.settingsChanged
            .onEach { event ->
                // Refresh settings when changes are notified from other ViewModels
                refreshSettings()
            }
            .launchIn(viewModelScope)
    }
    
    private fun loadSettings() {
        // Load saved preferences
        val soundEnabled = prefUtil.soundEnabled
        val vibrationEnabled = prefUtil.vibrationEnabled
        val dateTimeEnabled = prefUtil.dateTimeEnabled
        val batteryWidgetEnabled = prefUtil.batteryWidgetEnabled

        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(
                soundEnabled = soundEnabled,
                vibrationEnabled = vibrationEnabled,
                dateTimeEnabled = dateTimeEnabled,
                batteryWidgetEnabled = batteryWidgetEnabled,
            )
        }
    }
    
    fun setSelectedZipper(zipper: ZipperResponse?) {
        _selectedZipper.value = zipper
    }
    
    fun selectCustomizationOption(option: CustomizationOptionType) {
        _selectedCustomizationOption.value = option
    }
    
    private fun loadCustomizationOptions() {
        _customizationOptions.value = CustomizationOptionType.values().toList()
    }
    
    fun getZipperPreviewUrl(): String? {
        return _selectedZipper.value?.fileUrl
    }

    fun setSoundEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(soundEnabled = enabled)
        }
        // Save to preferences
        prefUtil.soundEnabled = enabled
    }

    fun setVibrationEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(vibrationEnabled = enabled)
        }
        // Save to preferences
        prefUtil.vibrationEnabled = enabled
    }

    fun setDateTimeEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(dateTimeEnabled = enabled)
        }
        // Save to preferences
        prefUtil.dateTimeEnabled = enabled
    }

    fun setBatteryWidgetEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(batteryWidgetEnabled = enabled)
        }
        // Save to preferences
        prefUtil.batteryWidgetEnabled = enabled
    }

    fun refreshSettings() {
        loadSettings()
    }
}