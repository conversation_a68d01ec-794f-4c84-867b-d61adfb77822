package co.ziplock.framework.presentation.edit_layout.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import android.graphics.drawable.Drawable
import co.ziplock.R
import co.ziplock.databinding.ItemCustomizationOptionSingleBinding
import co.ziplock.databinding.ItemCustomizationOptionVibrationBinding
import co.ziplock.framework.presentation.model.CustomizationOptionType
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.load.engine.DiskCacheStrategy

class CustomizationOptionsAdapter(
    private val onOptionClick: (CustomizationOptionType) -> Unit,
    private val onVibrationToggle: (Boolean) -> Unit,
    private val isVibrationEnabled: () -> Boolean,
    private val onLoadingStateChanged: (Boolean) -> Unit
) : RecyclerView.Adapter<CustomizationOptionsAdapter.BaseViewHolder>() {

    private var items: List<CustomizationOptionType> = emptyList()
    private var zipperResponse: ZipperResponse? = null
    private var wallpaperResponse: WallpaperResponse? = null
    private var backgroundResponse: BackgroundResponse? = null
    private var rowResponse: RowResponse? = null
    private var soundImageUrl: String? = null
    
    // Track loading state for each image type
    private var loadingCount = 0
        set(value) {
            field = value
            onLoadingStateChanged(value > 0)
        }
    
    private var isClickEnabled = true

    companion object {
        private const val VIEW_TYPE_NORMAL = 0
        private const val VIEW_TYPE_VIBRATION = 1
    }
    
    private fun incrementLoadingCount() {
        loadingCount++
    }
    
    private fun decrementLoadingCount() {
        if (loadingCount > 0) {
            loadingCount--
        }
    }
    
    fun setClickEnabled(enabled: Boolean) {
        isClickEnabled = enabled
    }

    fun submitList(newItems: List<CustomizationOptionType>) {
        items = newItems
        notifyDataSetChanged()
    }
    
    fun setZipperImage(zipper: ZipperResponse?) {
        zipperResponse = zipper
        // Find and update only the zipper item
        val zipperIndex = items.indexOfFirst { it == CustomizationOptionType.ZIPPER }
        if (zipperIndex != -1) {
            notifyItemChanged(zipperIndex)
        }
    }

    fun setWallpaperImage(wallpaper: WallpaperResponse?) {
        wallpaperResponse = wallpaper
        // Find and update only the wallpaper item
        val wallpaperIndex = items.indexOfFirst { it == CustomizationOptionType.WALLPAPER }
        if (wallpaperIndex != -1) {
            notifyItemChanged(wallpaperIndex)
        }
    }

    fun setBackgroundImage(background: BackgroundResponse?) {
        backgroundResponse = background
        // Find and update only the background item
        val backgroundIndex = items.indexOfFirst { it == CustomizationOptionType.BACKGROUND }
        if (backgroundIndex != -1) {
            notifyItemChanged(backgroundIndex)
        }
    }
    
    fun setRowImage(row: RowResponse?) {
        rowResponse = row
        // Find and update only the row item
        val rowIndex = items.indexOfFirst { it == CustomizationOptionType.ROW }
        if (rowIndex != -1) {
            notifyItemChanged(rowIndex)
        }
    }
    
    fun setSoundImage(imageUrl: String?) {
        soundImageUrl = imageUrl
        // Find and update only the sound item
        val soundIndex = items.indexOfFirst { it == CustomizationOptionType.SOUND }
        if (soundIndex != -1) {
            notifyItemChanged(soundIndex)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (items[position]) {
            CustomizationOptionType.VIBRATION -> VIEW_TYPE_VIBRATION
            else -> VIEW_TYPE_NORMAL
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            VIEW_TYPE_VIBRATION -> {
                val binding = DataBindingUtil.inflate<ItemCustomizationOptionVibrationBinding>(
                    inflater, R.layout.item_customization_option_vibration, parent, false
                )
                VibrationViewHolder(binding)
            }
            else -> {
                val binding = DataBindingUtil.inflate<ItemCustomizationOptionSingleBinding>(
                    inflater, R.layout.item_customization_option_single, parent, false
                )
                NormalViewHolder(binding)
            }
        }
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int = items.size

    abstract class BaseViewHolder(binding: ViewDataBinding) : RecyclerView.ViewHolder(binding.root) {
        abstract fun bind(item: CustomizationOptionType)
    }

    inner class NormalViewHolder(
        private val binding: ItemCustomizationOptionSingleBinding
    ) : BaseViewHolder(binding) {
        override fun bind(item: CustomizationOptionType) {
            binding.tvLabel.text = binding.root.context.getString(item.titleId)
            
            // Handle specific item image loading
            when (item) {
                CustomizationOptionType.ZIPPER -> {
                    if (zipperResponse?.fileUrl != null) {
                        // Load zipper image from URL
                        incrementLoadingCount()
                        Glide.with(binding.root.context)
                            .load(zipperResponse!!.fileUrl)
                            .placeholder(getDefaultIcon(item))
                            .error(getDefaultIcon(item))
                            .listener(object : RequestListener<Drawable> {
                                override fun onLoadFailed(
                                    e: GlideException?,
                                    model: Any?,
                                    target: Target<Drawable?>,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }

                                override fun onResourceReady(
                                    resource: Drawable,
                                    model: Any,
                                    target: Target<Drawable?>?,
                                    dataSource: DataSource,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }
                            })
                            .into(binding.ivIcon)
                    } else {
                        binding.ivIcon.setImageResource(getDefaultIcon(item))
                    }
                }
                CustomizationOptionType.WALLPAPER -> {
                    if (wallpaperResponse?.previewThumbnail != null) {
                        // Load wallpaper preview thumbnail from URL
                        incrementLoadingCount()
                        Glide.with(binding.root.context)
                            .load(wallpaperResponse!!.previewThumbnail)
                            .placeholder(getDefaultIcon(item))
                            .error(getDefaultIcon(item))
                            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                            .skipMemoryCache(false)
                            .listener(object : RequestListener<Drawable> {
                                override fun onLoadFailed(
                                    e: GlideException?,
                                    model: Any?,
                                    target: Target<Drawable?>,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }

                                override fun onResourceReady(
                                    resource: Drawable,
                                    model: Any,
                                    target: Target<Drawable?>?,
                                    dataSource: DataSource,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }
                            })
                            .into(binding.ivIcon)
                    } else {
                        binding.ivIcon.setImageResource(getDefaultIcon(item))
                    }
                }
                CustomizationOptionType.BACKGROUND -> {
                    if (backgroundResponse?.previewThumbnail != null) {
                        // Load background preview thumbnail from URL
                        incrementLoadingCount()
                        Glide.with(binding.root.context)
                            .load(backgroundResponse!!.previewThumbnail)
                            .placeholder(getDefaultIcon(item))
                            .error(getDefaultIcon(item))
                            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                            .skipMemoryCache(false)
                            .listener(object : RequestListener<Drawable> {
                                override fun onLoadFailed(
                                    e: GlideException?,
                                    model: Any?,
                                    target: Target<Drawable?>,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }

                                override fun onResourceReady(
                                    resource: Drawable,
                                    model: Any,
                                    target: Target<Drawable?>?,
                                    dataSource: DataSource,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }
                            })
                            .into(binding.ivIcon)
                    } else {
                        binding.ivIcon.setImageResource(getDefaultIcon(item))
                    }
                }
                CustomizationOptionType.ROW -> {
                    if (rowResponse?.previewThumbnail != null) {
                        // Load row preview thumbnail from URL
                        incrementLoadingCount()
                        Glide.with(binding.root.context)
                            .load(rowResponse!!.previewThumbnail)
                            .placeholder(getDefaultIcon(item))
                            .error(getDefaultIcon(item))
                            .listener(object : RequestListener<Drawable> {
                                override fun onLoadFailed(
                                    e: GlideException?,
                                    model: Any?,
                                    target: Target<Drawable?>,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }

                                override fun onResourceReady(
                                    resource: Drawable,
                                    model: Any,
                                    target: Target<Drawable?>?,
                                    dataSource: DataSource,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }
                            })
                            .into(binding.ivIcon)
                    } else {
                        binding.ivIcon.setImageResource(getDefaultIcon(item))
                    }
                }
                CustomizationOptionType.SOUND -> {
                    if (!soundImageUrl.isNullOrEmpty()) {
                        // Load sound preview image from URL
                        incrementLoadingCount()
                        Glide.with(binding.root.context)
                            .load(soundImageUrl)
                            .placeholder(getDefaultIcon(item))
                            .error(getDefaultIcon(item))
                            .listener(object : RequestListener<Drawable> {
                                override fun onLoadFailed(
                                    e: GlideException?,
                                    model: Any?,
                                    target: Target<Drawable?>,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }

                                override fun onResourceReady(
                                    resource: Drawable,
                                    model: Any,
                                    target: Target<Drawable?>?,
                                    dataSource: DataSource,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    decrementLoadingCount()
                                    return false
                                }
                            })
                            .into(binding.ivIcon)
                    } else {
                        // Check if "No Sound" is selected (when soundImageUrl is null/empty)
                        binding.ivIcon.setImageResource(getNoSoundIcon())
                    }
                }
                else -> {
                    // Use default icon for other items
                    binding.ivIcon.setImageResource(getDefaultIcon(item))
                }
            }
            
            binding.rlItem.setPreventDoubleClick {
                if (isClickEnabled) {
                    onOptionClick(item)
                }
            }
        }
        
        private fun getDefaultIcon(item: CustomizationOptionType): Int {
            return when (item) {
                CustomizationOptionType.ZIPPER -> R.drawable.ic_zipper
                CustomizationOptionType.ROW -> R.drawable.ic_row
                CustomizationOptionType.SOUND -> R.drawable.ic_music_note
                CustomizationOptionType.BACKGROUND -> R.drawable.ic_wallpaper
                CustomizationOptionType.WALLPAPER -> R.drawable.ic_wallpaper
                CustomizationOptionType.VIBRATION -> R.drawable.ic_wallpaper
            }
        }
        
        private fun getNoSoundIcon(): Int {
            return R.drawable.ic_no_sound
        }
    }

    inner class VibrationViewHolder(
        private val binding: ItemCustomizationOptionVibrationBinding
    ) : BaseViewHolder(binding) {
        override fun bind(item: CustomizationOptionType) {
            binding.tvLabel.text = binding.root.context.getString(item.titleId)
            
            // Set current vibration state
            val isEnabled = isVibrationEnabled()
            binding.switchVibration.isChecked = isEnabled
            binding.tvStatus.text = if (isEnabled) {
                binding.root.context.getString(R.string.on)
            } else {
                binding.root.context.getString(R.string.off)
            }

            // Handle switch toggle
            binding.switchVibration.setOnCheckedChangeListener { _, isChecked ->
                binding.tvStatus.text = if (isChecked) {
                    binding.root.context.getString(R.string.on)
                } else {
                    binding.root.context.getString(R.string.off)
                }
                onVibrationToggle(isChecked)
            }
            
            // Handle item click
            binding.rlItem.setPreventDoubleClick {
                if (isClickEnabled) {
                    // Toggle switch when item is clicked
                    binding.switchVibration.isChecked = !binding.switchVibration.isChecked
                }
            }
        }
    }
}