package co.ziplock.framework.presentation.generalsetting

import android.view.View
import co.ziplock.databinding.FragmentGeneralSettingScreenBinding
import co.ziplock.framework.presentation.common.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class GeneralSettingFragment :
    BaseFragment<FragmentGeneralSettingScreenBinding, GeneralSettingViewModel>(
        FragmentGeneralSettingScreenBinding::inflate,
        GeneralSettingViewModel::class.java,
    ) {
    override fun init(view: View) {
        handleBackEvent()
        bindView()
        policyEvent()
        languageEvent()
        developerEvent()
        resetIapEvent()
        inspectorEvent()
    }

    override fun subscribeObserver(view: View) {
        // TODO("Not yet implemented")
    }

    companion object {
        const val TAG = "GeneralSettingFragment"
    }
}
