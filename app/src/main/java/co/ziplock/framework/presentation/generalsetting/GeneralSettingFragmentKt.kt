package co.ziplock.framework.presentation.generalsetting

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import co.ziplock.BuildConfig
import co.ziplock.R
import co.ziplock.databinding.DialogDeveloperBinding
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import co.ziplock.util.setPreventDoubleClickScaleView
import com.example.libiap.IAPConnector
import pion.datlt.libads.AdsController
import timber.log.Timber

fun GeneralSettingFragment.backEvent() {
    Timber.tag(GeneralSettingFragment.TAG).d("backEvent: ")
    activity?.onBackPressedDispatcher?.onBackPressed()
}

fun GeneralSettingFragment.handleBackEvent() {
    binding.ivBack.setPreventDoubleClickScaleView {
        backEvent()
    }
}

@SuppressLint("SetTextI18n")
fun GeneralSettingFragment.bindView() {
    val remoteResult =
        if (Constant.isRemoteConfigSuccess) {
            "R"
        } else {
            "D"
        }

    binding.txvVersion.text =
        buildString {
            append("Application version: v")
            append(" ")
            append(remoteResult)
            append(" ")
            append(BuildConfig.VERSION_CODE)
            append(" ")
            append(BuildConfig.VERSION_NAME)
        }
}

fun GeneralSettingFragment.developerEvent() {
    binding.btnDeveloper.setPreventDoubleClickScaleView {
        requireContext().showDeveloperDialog(lifecycle = lifecycle, onClose = {})
    }
}

fun Context.showDeveloperDialog(
    lifecycle: Lifecycle,
    onClose: () -> Unit,
) {
    val dialog = Dialog(this)
    val view: View = LayoutInflater.from(this).inflate(R.layout.dialog_developer, null)
    dialog.setContentView(view)
    dialog.setCancelable(false)
    dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    dialog.window?.setLayout(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT,
    )
    val binding = DialogDeveloperBinding.bind(view)

    lifecycle.addObserver(
        LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    dialog.dismiss()
                }

                else -> {
                }
            }
        },
    )

    binding.apply {
        ivClose.setPreventDoubleClick {
            dialog.dismiss()
            onClose.invoke()
        }
    }

    if (!dialog.isShowing) {
        dialog.show()
    }
}

fun GeneralSettingFragment.languageEvent() {
    binding.btnLanguage.setPreventDoubleClickScaleView {
        safeNav(
            R.id.generalSettingFragment,
            R.id.action_generalSettingFragment_to_languageFragment,
        )
    }
}

fun GeneralSettingFragment.policyEvent() {
    binding.btnPolicy.setPreventDoubleClickScaleView {
        try {
            val browserIntent =
                Intent(
                    Intent.ACTION_VIEW,
                    "https://sites.google.com/piontech.co/ziplock".toUri(),
                )
            startActivity(browserIntent)
        } catch (e: Exception) {
        }
    }
}

fun GeneralSettingFragment.resetIapEvent() {
    if (BuildConfig.DEBUG) {
        binding.btnResetIap.isVisible = true
        binding.btnResetIap.setPreventDoubleClick {
            activity?.let { IAPConnector.resetIap(it) }
        }
    } else {
        binding.btnResetIap.isVisible = false
    }
}

fun GeneralSettingFragment.inspectorEvent() {
    if (BuildConfig.DEBUG) {
        binding.btnInspector.isVisible = true
        binding.btnInspector.setPreventDoubleClick {
            AdsController.getInstance().openInspector { }
        }
    } else {
        binding.btnInspector.isVisible = false
    }
}
