package co.ziplock.framework.presentation.home

import android.content.Context
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.common.launchMain
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.framework.presentation.manager.SettingsChangeEvent
import co.ziplock.framework.presentation.manager.SettingsManager
import co.ziplock.framework.presentation.model.home.HomeTabUiState
import co.ziplock.framework.presentation.model.home.SettingsTabUiState
import co.ziplock.framework.service.ZipperOverlayService
import co.ziplock.util.PrefUtil
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val prefUtil: PrefUtil,
    private val securityManager: SecurityManager,
    private val settingsManager: SettingsManager
) : BaseViewModel() {

    // HomeTab UI State
    private val _homeTabUiState = MutableStateFlow(HomeTabUiState())
    val homeTabUiState: StateFlow<HomeTabUiState> = _homeTabUiState.asStateFlow()

    // SettingsTab UI State
    private val _settingsTabUiState = MutableStateFlow(SettingsTabUiState())
    val settingsTabUiState: StateFlow<SettingsTabUiState> = _settingsTabUiState.asStateFlow()

    init {
        // Load saved preferences
        val lockScreenEnabled = prefUtil.lockScreenEnabled
        val passwordEnabled = prefUtil.passwordEnabled
        val soundEnabled = prefUtil.soundEnabled
        val vibrationEnabled = prefUtil.vibrationEnabled
        val dateTimeEnabled = prefUtil.dateTimeEnabled
        val batteryWidgetEnabled = prefUtil.batteryWidgetEnabled

        // Update UI States
        _settingsTabUiState.update { currentState ->
            currentState.copy(
                lockScreenEnabled = lockScreenEnabled,
                passwordEnabled = passwordEnabled,
                soundEnabled = soundEnabled,
                vibrationEnabled = vibrationEnabled,
                dateTimeEnabled = dateTimeEnabled,
                batteryWidgetEnabled = batteryWidgetEnabled,
            )
        }
    }

    fun setLockScreenEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { it.copy(lockScreenEnabled = enabled) }

        // Save to preferences
        prefUtil.lockScreenEnabled = enabled
        
        // Start or stop ZipperOverlayService based on the enabled state
        if (enabled) {
            ZipperOverlayService.startService(context, ZipperOverlayService.ACTION_START_FOREGROUND)
        } else {
            ZipperOverlayService.stopService(context)
        }
    }

    fun setPasswordEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(
                passwordEnabled = enabled,
            )
        }

        // Save to preferences
        prefUtil.passwordEnabled = enabled
    }

    fun hasExistingPassword(): Boolean {
        return securityManager.hasSecurity()
    }

    fun setSoundEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(soundEnabled = enabled)
        }
        // Save to preferences
        prefUtil.soundEnabled = enabled
        // Notify other ViewModels
        launchMain {
            settingsManager.notifySettingsChanged(SettingsChangeEvent.SoundChanged)
        }
    }

    fun setVibrationEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(vibrationEnabled = enabled)
        }
        // Save to preferences
        prefUtil.vibrationEnabled = enabled
        // Notify other ViewModels
        launchMain {
            settingsManager.notifySettingsChanged(SettingsChangeEvent.VibrationChanged)
        }
    }

    fun setDateTimeEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(dateTimeEnabled = enabled)
        }
        // Save to preferences
        prefUtil.dateTimeEnabled = enabled
        // Notify other ViewModels
        launchMain {
            settingsManager.notifySettingsChanged(SettingsChangeEvent.DateTimeChanged)
        }
    }

    fun setBatteryWidgetEnabled(enabled: Boolean) {
        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(batteryWidgetEnabled = enabled)
        }
        // Save to preferences
        prefUtil.batteryWidgetEnabled = enabled
        // Notify other ViewModels
        launchMain {
            settingsManager.notifySettingsChanged(SettingsChangeEvent.BatteryWidgetChanged)
        }
    }

    // Theme navigation methods using PrefUtil
    fun getSelectedThemeFromSaveSuccess(): Int {
        return prefUtil.selectedThemeFromSaveSuccess
    }

    fun clearSelectedThemeFromSaveSuccess() {
        prefUtil.clearSelectedThemeFromSaveSuccess()
    }

}