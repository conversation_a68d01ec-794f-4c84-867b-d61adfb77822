package co.ziplock.framework.presentation.home

import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResultListener
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentHomeBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.home.adapter.HomePagerAdapter
import co.ziplock.framework.presentation.manager.PermissionManager
import co.ziplock.util.BundleKey
import kotlin.system.exitProcess

@AndroidEntryPoint
class HomeFragment : BaseFragment<FragmentHomeBinding, HomeViewModel>(
    FragmentHomeBinding::inflate,
    HomeViewModel::class.java
) {
    lateinit var pagerAdapter: HomePagerAdapter
    
    internal val permissionManager by lazy {
        PermissionManager(requireContext())
    }

    override fun init(view: View) {
        //TODO: Favorite in v1.0.0 is not implemented yet
        binding.iconFavorite.isVisible = false

        setFragmentResultListener(
            BundleKey.KEY_REQUEST_OPEN_CHOOSE_PASSWORD_FROM_FORGOT_PASSWORD_FLOW
        ) { _, bundle ->
            val isOpenChangePasswordDialog = bundle.getBoolean(BundleKey.KEY_REQUEST_OPEN_CHOOSE_PASSWORD_FROM_FORGOT_PASSWORD_FLOW)

            if (isOpenChangePasswordDialog) {
                showPasswordTypeSelectionDialogFragment()
            }
        }
        onBackEvent()
        setupViewPager()
        setupTabLayout()
        setUpPreviewButton()
        setUpSettingsButton()
        checkForThemeNavigation()
        requestNotificationPermission()
        initAppResumeAds()
        showAds()
        iapEvent()
    }

    override fun subscribeObserver(view: View) {
        observeCurrentFragmentTab()
    }
}
