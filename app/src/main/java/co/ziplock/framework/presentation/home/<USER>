package co.ziplock.framework.presentation.home

import android.widget.TextView
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.dialog.PasswordType
import co.ziplock.framework.presentation.dialog.PasswordTypeSelectionDialogFragment
import co.ziplock.framework.presentation.dialog.ZipperFullScreenDialogFragment
import co.ziplock.framework.presentation.home.adapter.HomePagerAdapter
import co.ziplock.framework.presentation.home.dialog.ExitAppDialog
import co.ziplock.framework.presentation.home.tabs.settings.SettingsTabFragment
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.disableTooltipsForTabs
import co.ziplock.util.setPreventDoubleClick
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.AdsController
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.show3NativeUsePriority
import kotlin.system.exitProcess

fun HomeFragment.setupViewPager() {
    pagerAdapter = HomePagerAdapter(fragment = this)
    binding.viewPager.apply {
        adapter = pagerAdapter
        isUserInputEnabled = false
        offscreenPageLimit = 2 // Keep both fragments in memory
    }

    // Set initial fragment is HomeTabFragment
}

fun HomeFragment.iapEvent() {
    binding.iconIap.setPreventDoubleClick {
        safeNav(
            R.id.homeFragment,
            R.id.action_homeFragment_to_iapFragment
        )
    }
}

fun HomeFragment.setupTabLayout() {
    // Connect TabLayout with ViewPager2
    TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
        tab.text =
            when (position) {
                0 -> requireContext().getString(R.string.settings)
                1 -> requireContext().getString(R.string.tab_home)
                else -> ""
            }
    }.attach()

    binding.tabLayout.disableTooltipsForTabs()

    binding.tabLayout.addOnTabSelectedListener(
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                // Tab được chọn
                val position = tab.position

                when (position) {
                    0 -> commonViewModel.requestOpenSettingsTabScreen()
                    else -> commonViewModel.requestOpenHomeTabScreen()
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                // Tab bị bỏ chọn
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
                // Tab được chọn lại (khi đã chọn rồi)
            }
        },
    )
}

fun HomeFragment.setUpSettingsButton() {
    binding.iconSettings.setPreventDoubleClick {
        safeNav(
            R.id.homeFragment,
            R.id.action_homeFragment_to_generalSettingFragment,
        )
    }
}

fun HomeFragment.initAppResumeAds() {
    // init app resume
    if (activity == null || AdsConstant.listConfigAds["appresume"]?.isOn == false) return
    AdsController.getInstance().initResumeAds(
        lifecycle = activity!!.lifecycle,
        listSpaceName = listOf("appresume_openad1", "appresume_openad2", "appresume_openad3"),
        onShowOpenApp = {
            activity?.findViewById<TextView>(R.id.viewShowOpenApp)?.isVisible = true
        },
        onStartToShowOpenAds = {
            activity?.findViewById<TextView>(R.id.viewShowOpenApp)?.isVisible = true
        },
        onCloseOpenApp = {
            activity?.findViewById<TextView>(R.id.viewShowOpenApp)?.isVisible = false
        },
        onPaidEvent = {
            // do nothing
        },
    )
}

fun HomeFragment.showAds() {
    show3NativeUsePriority(
        spaceNameConfig = "Home",
        spaceName1 = "Home_native1",
        spaceName2 = "Home_native2",
        spaceName3 = "",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
    safePreloadAds(
        listSpaceNameConfig = listOf("Home-lock-on/off", "Home-pass-on/off", "Home-sett-on/off"),
        spaceNameAds = "Home-1ID_interstitial",
    )

    safePreloadAds(
        spaceNameConfig = "custom-bot",
        spaceNameAds = "custom-bot_native",
    )

    safePreloadAds(
        spaceNameConfig = "custom-top",
        spaceNameAds = "custom-top_native",
    )

    safePreloadAds(
        spaceNameConfig = "exitapp",
        spaceNameAds = "exitapp_native1",
        includeHasBeenOpened = true,
    )

    safePreloadAds(
        spaceNameConfig = "exitapp",
        spaceNameAds = "exitapp_native2",
        includeHasBeenOpened = true,
    )

    safePreloadAds(
        spaceNameConfig = "exitapp",
        spaceNameAds = "exitapp_native3",
        includeHasBeenOpened = true,
    )
}

fun HomeFragment.setUpPreviewButton() {
    binding.iconPreview.setPreventDoubleClick {
        showZipperFullScreenPreview()
    }
}

fun HomeFragment.showPasswordTypeSelectionDialogFragment(initialPasswordType: PasswordType = PasswordType.PIN) {
    val dialogFragment = PasswordTypeSelectionDialogFragment.newInstance(initialPasswordType)

    dialogFragment.onPasswordTypeSelected = { passwordType ->
        when (passwordType) {
            PasswordType.PIN -> {
                // Navigate to PIN setup fragment
                safeNav(
                    R.id.homeFragment,
                    R.id.action_homeFragment_to_pinSetupFragment,
                    bundleOf(BundleKey.KEY_FIRST_SETUP_PASSWORD to false),
                )
            }

            PasswordType.PATTERN -> {
                // Navigate to Pattern setup fragment
                safeNav(
                    R.id.homeFragment,
                    R.id.action_homeFragment_to_patternSetupFragment,
                    bundleOf(BundleKey.KEY_FIRST_SETUP_PASSWORD to false),
                )
            }
        }
    }

    dialogFragment.onCancel = {
        // Cancel
    }

    dialogFragment.show(parentFragmentManager, SettingsTabFragment.TAG)
}

fun HomeFragment.checkForThemeNavigation() {
    // Check if we need to navigate to theme fragment from save success
    val selectedThemeId = viewModel.getSelectedThemeFromSaveSuccess()

    if (selectedThemeId != -1) {
        // Clear the flag
        viewModel.clearSelectedThemeFromSaveSuccess()

        // Navigate to theme fragment
        safeNav(
            R.id.homeFragment,
            R.id.action_homeFragment_to_themeFragment,
        )
    }
}

fun HomeFragment.onBackEvent() {
    onSystemBackEvent {
        val dialog = ExitAppDialog()
        dialog.setListener(
            listener =
                object : ExitAppDialog.Listener {
                    override fun onSubmitExit() {
                        exitProcess(0)
                    }
                },
        )
        dialog.show(childFragmentManager)
    }
}

fun HomeFragment.observeCurrentFragmentTab() {
    commonViewModel.homeScreenUiState
        .map { it.currentPageIndex }
        .flowWithLifecycle(lifecycle, Lifecycle.State.STARTED)
        .distinctUntilChanged()
        .onEach { index ->
            if (index == Constant.NO_INDEX) {
                commonViewModel.requestOpenHomeTabScreen()
            } else {
                binding.viewPager.post {
                    binding.viewPager.currentItem = index
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun HomeFragment.requestNotificationPermission() {
    // Check if notification permission is already granted
    if (permissionManager.isNotificationPermissionGranted()) {
        return
    }

    // Request notification permission for Android 13+ (API 33+)
    val notificationPermissions = permissionManager.getNotificationPermission()
    if (notificationPermissions.isNotEmpty()) {
        permissionManager.requestPermission(
            fragment = this,
            permission = notificationPermissions,
            onAllPermissionGranted = {
                // Permission granted - notifications can now be sent
            },
            onPermissionDenied = {
                // Permission denied - handle gracefully
                // App can still function without notification permission
            },
        )
    }
}

fun HomeFragment.showZipperFullScreenPreview() {
    val dialogFragment = ZipperFullScreenDialogFragment.newInstance()
    dialogFragment.show(parentFragmentManager, ZipperFullScreenDialogFragment.TAG)
}
