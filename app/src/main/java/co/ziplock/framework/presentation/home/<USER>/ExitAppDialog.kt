package co.ziplock.framework.presentation.home.dialog

import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.DialogExitAppBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.setPreventDoubleClick
import pion.datlt.libads.utils.adsuntils.show3NativeUsePriority

class ExitAppDialog : BaseDialogFragment<DialogExitAppBinding>(R.layout.dialog_exit_app) {
    fun interface Listener {
        fun onSubmitExit()
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo()

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        show3NativeUsePriority(
            spaceNameConfig = "exitapp",
            spaceName1 = "exitapp_native1",
            spaceName2 = "exitapp_native2",
            spaceName3 = "exitapp_native3",
            includeHasBeenOpened = true,
            layoutToAttachAds = binding.viewGroupAds,
            layoutContainAds = binding.layoutAds,
        )
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        binding.btnCancel.setPreventDoubleClick {
            dismiss()
        }
        binding.btnExit.setPreventDoubleClick {
            listener?.onSubmitExit()
            dismiss()
        }
    }
}
