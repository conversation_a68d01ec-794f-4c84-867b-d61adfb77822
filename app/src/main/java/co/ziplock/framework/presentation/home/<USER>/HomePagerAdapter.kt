package co.ziplock.framework.presentation.home.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import co.ziplock.framework.presentation.home.tabs.home.HomeTabFragment
import co.ziplock.framework.presentation.home.tabs.settings.SettingsTabFragment

class HomePagerAdapter(fragment: Fragment) :
    FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> SettingsTabFragment.newInstance()
            1 -> HomeTabFragment.newInstance()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}