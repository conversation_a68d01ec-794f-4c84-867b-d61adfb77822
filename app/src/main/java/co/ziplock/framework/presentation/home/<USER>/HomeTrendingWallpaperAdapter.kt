package co.ziplock.framework.presentation.home.adapter

import androidx.core.text.buildSpannedString
import androidx.core.text.underline
import co.ziplock.R
import co.ziplock.databinding.ItemTrendingWallpaperBinding
import co.ziplock.databinding.ItemTrendingWallpaperHomeBinding
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.framework.presentation.model.home.TrendingWallpaper
import co.ziplock.util.loadImage
import co.ziplock.util.setPreventDoubleClick
import timber.log.Timber

class HomeHotTrendingThemeModelAdapter(
    private val onTryNowClick: (HotTrendingThemeModel) -> Unit
) : BaseListAdapter<HotTrendingThemeModel, ItemTrendingWallpaperHomeBinding>(DIFF_CALLBACK) {

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_trending_wallpaper_home

    override fun bindView(
        binding: ItemTrendingWallpaperHomeBinding,
        item: HotTrendingThemeModel,
        position: Int
    ) {
        val item = getItem(position)
        binding.apply {
            btnTryNow.text = buildSpannedString {
                underline {
                    append(root.context.getString(R.string.button_try_now))
                }
            }

            binding.ivWallpaper.loadImage(item.previewImageUrl)
            
            binding.root.setPreventDoubleClick {
                onTryNowClick(item)
            }
        }
    }

    companion object {
        private val DIFF_CALLBACK = createDiffCallback<HotTrendingThemeModel>(
            areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
        )
    }
}