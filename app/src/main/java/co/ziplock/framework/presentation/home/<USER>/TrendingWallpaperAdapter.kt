package co.ziplock.framework.presentation.home.adapter

import android.view.LayoutInflater
import androidx.core.text.buildSpannedString
import androidx.core.text.underline
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import co.ziplock.R
import co.ziplock.databinding.ItemTrendingWallpaperBinding
import co.ziplock.databinding.ViewItemNativeHotTrendingBinding
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.util.loadImage
import co.ziplock.util.setPreventDoubleClick
import pion.datlt.libads.utils.adsuntils.showLoadedNative

class HotTrendingThemeModelAdapter : BaseListAdapter<HotTrendingThemeModel, ViewDataBinding>(DIFF_CALLBACK) {
    override fun getLayoutRes(viewType: Int): Int =
        if (viewType == ITEM_TYPE_ADS) R.layout.view_item_native_hot_trending else R.layout.item_trending_wallpaper

    override fun getItemViewType(position: Int): Int = if (getItem(position).adsItem != null) ITEM_TYPE_ADS else ITEM_TYPE_NORMAL

    private var fragment: Fragment? = null

    fun setFragment(fragment: Fragment) {
        this.fragment = fragment
    }

    interface Listener {
        fun onTryNowClick(item: HotTrendingThemeModel)
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    override fun bindView(
        binding: ViewDataBinding,
        item: HotTrendingThemeModel,
        position: Int,
    ) {
        if (binding is ItemTrendingWallpaperBinding) {
            val item = getItem(position)
            binding.apply {
                btnTryNow.text =
                    buildSpannedString {
                        underline {
                            append(root.context.getString(R.string.button_try_now))
                        }
                    }

                binding.ivWallpaper.loadImage(item.previewImageUrl)

                binding.root.setPreventDoubleClick {
                    listener?.onTryNowClick(item)
                }
            }
        }
        if (binding is ViewItemNativeHotTrendingBinding) {
            binding.layoutAds.post {
                val view =
                    LayoutInflater
                        .from(binding.root.context)
                        .inflate(R.layout.layout_native_trending_list, null)
                fragment?.showLoadedNative(
                    spaceNameConfig = item.adsItem?.configName ?: "",
                    spaceName = item.adsItem?.admobIdName ?: "",
                    includeHasBeenOpened = true,
                    viewAdsInflateFromXml = view,
                    ratioView = "328:136",
                    layoutToAttachAds = binding.adViewGroup,
                    layoutContainAds = binding.layoutAds,
                    onAdsClick = {},
                )
            }
        }
    }

    companion object {
        private val DIFF_CALLBACK =
            createDiffCallback<HotTrendingThemeModel>(
                areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
                areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
            )

        const val ITEM_TYPE_ADS = 1
        const val ITEM_TYPE_NORMAL = 0
    }
}
