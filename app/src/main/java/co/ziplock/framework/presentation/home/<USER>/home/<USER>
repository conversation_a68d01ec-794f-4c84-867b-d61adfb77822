package co.ziplock.framework.presentation.home.tabs.home

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.widget.Toast
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import co.ziplock.R
import co.ziplock.framework.presentation.common.doActionWhenStop
import co.ziplock.framework.presentation.dialog.HomeDisableLockScreenDialogFragment
import co.ziplock.framework.presentation.dialog.HomeRequiresOverlayPermissionDialogFragment
import co.ziplock.framework.presentation.home.adapter.HomeHotTrendingThemeModelAdapter
import co.ziplock.framework.presentation.model.OnboardingFavoriteStyle
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.framework.service.SettingsNotificationService
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.AdsController
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.show3LoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun HomeTabFragment.setupTrendingWallpapers() {
    trendingAdapter =
        HomeHotTrendingThemeModelAdapter { theme ->
            // Navigate directly to EditLayoutFragment with selected theme
            navigateToEditLayoutWithTheme(theme)
        }

    binding.rvTrendingWallpapers.apply {
        layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        adapter = trendingAdapter
    }
}

fun HomeTabFragment.observeLockScreenStatusChanged() {
    sharedViewModel.settingsTabUiState
        .map { it.lockScreenEnabled }
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .onEach { isEnabled ->
            // Handle tooltip for lock screen
            handleLockScreenTooltip(isEnabled)
        }.distinctUntilChanged()
        .onEach { isEnabled ->
            // Update switch without triggering listener
            binding.switchLockScreen.setOnCheckedChangeListener(null)
            binding.switchLockScreen.isChecked = isEnabled
            setUpLockScreenStatusCheckChanged()
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun HomeTabFragment.handleLockScreenTooltip(isEnabled: Boolean) {
    if (!isEnabled) {
        // Show tooltip when lock screen is OFF
        showLockScreenTooltip()
    } else {
        // Hide tooltip when lock screen is ON
        hideLockScreenTooltip()
    }
}

fun HomeTabFragment.showLockScreenTooltip() {
    binding.tooltipLockScreen.visibility = View.VISIBLE
    binding.tooltipLockScreen.playAnimation()

    // Show toast message
    Toast
        .makeText(
            requireContext(),
            getString(R.string.tooltip_lock_screen_message),
            Toast.LENGTH_LONG,
        ).show()
}

fun HomeTabFragment.hideLockScreenTooltip() {
    binding.tooltipLockScreen.visibility = View.GONE
    binding.tooltipLockScreen.pauseAnimation()
}

fun HomeTabFragment.setUpLockScreenStatusCheckChanged() {
    // Lock screen status toggle
    fun showInterLockOnOff(action: () -> Unit) {
        showLoadedInter(
            spaceNameConfig = "Home-lock-on/off",
            spaceName = "Home-1ID_interstitial",
            destinationToShowAds = R.id.homeFragment,
            isShowLoadingView = true,
            isScreenType = false,
            navOrBack = {
                action.invoke()
            },
            onCloseAds = {},
        )
    }
    binding.switchLockScreen.setPreventDoubleClick {
        val isChecked = binding.switchLockScreen.isChecked
        if (isChecked) {
            if (!Settings.canDrawOverlays(requireContext())) {
                HomeRequiresOverlayPermissionDialogFragment
                    .Builder()
                    .setOnClickDoIt {
                        val intent =
                            Intent(
                                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                "package:${requireContext().packageName}".toUri(),
                            )
                        // Show custom notification when navigating to settings
                        SettingsNotificationService.showNotification(requireContext())
                        doActionWhenStop {
                            AdsController.isBlockOpenAds = true
                        }
                        overlayPermissionLauncher.launch(intent)
                    }.setOnClickCancel {
                        sharedViewModel.setLockScreenEnabled(false)
                    }.build()
                    .show(childFragmentManager, HomeTabFragment.TAG)
            } else {
                showInterLockOnOff {}
            }
        } else {
            if (Settings.canDrawOverlays(requireContext())) {
                HomeDisableLockScreenDialogFragment
                    .Builder()
                    .setOnClickDisableLockScreenButton {
                        showInterLockOnOff {
                            sharedViewModel.setLockScreenEnabled(false)
                        }
                    }.setOnClickCancelButton {
                        sharedViewModel.setLockScreenEnabled(true)
                    }.build()
                    .show(childFragmentManager, HomeTabFragment.TAG)
            }
        }
        sharedViewModel.setLockScreenEnabled(isChecked)
    }
}

fun HomeTabFragment.setupSwipeRefreshLayout() {
    binding.swipeRefreshLayout.apply {
        // Set colors for the refresh indicator using app colors
        setColorSchemeResources(
            R.color.blue_2fafff,
            R.color.blue_4285f4,
            R.color.green_4caf50,
            R.color.blue_1da1f2,
        )

        // Set refresh listener
        setOnRefreshListener {
            // Call refresh data from CommonViewModel
            commonViewModel.loadAllTemplateDataWithCategories()
        }
    }
}

fun HomeTabFragment.setupClickListeners() {
    setUpLockScreenStatusCheckChanged()
    // Setup SwipeRefreshLayout
    setupSwipeRefreshLayout()
    // Setup Shimmer Effect
    setupShimmerEffect()

    binding.apply {
        zipperOption.setPreventDoubleClick {
            // Navigate to Zipper fragment
            showInterFeatureAndDoFunction {
                safeNavInter(
                    R.id.homeFragment,
                    R.id.action_homeFragment_to_zipperFragment,
                )
            }
        }

        rowOption.setPreventDoubleClick {
            // Navigate to Zipper fragment
            showInterFeatureAndDoFunction {
                safeNavInter(
                    R.id.homeFragment,
                    R.id.action_homeFragment_to_rowFragment,
                )
            }
        }

        wallpaperOption.setPreventDoubleClick {
            // Navigate to Wallpaper fragment
            showInterFeatureAndDoFunction {
                safeNavInter(
                    R.id.homeFragment,
                    R.id.action_homeFragment_to_wallpaperFragment,
                )
            }
        }

        btnCustomize.setPreventDoubleClick {
            // Navigate to Customize fragment
            safeNav(
                R.id.homeFragment,
                R.id.action_homeFragment_to_customizeFragment,
            )
        }

        btnMoreHotTrending.setPreventDoubleClick {
            // Navigate to Theme fragment
            safeNav(
                R.id.homeFragment,
                R.id.action_homeFragment_to_themeFragment,
            )
        }
    }
}

fun HomeTabFragment.observeHotTrendingThemes() {
    combine(
        commonViewModel.hotTrendingUiState.map { it.hotTrendingThemes },
        commonViewModel.onboardingUiState.map { it.selectedFavoriteStyles },
    ) { hotTrendingThemes, _ ->
        updateThemesList(hotTrendingThemes)
    }.launchIn(viewLifecycleOwner.lifecycleScope)
}

private fun HomeTabFragment.updateThemesList(hotTrendingThemes: List<HotTrendingThemeModel>) {
    // Get selected favorite styles from onboarding
    val selectedStyles = commonViewModel.onboardingUiState.value.selectedFavoriteStyles

    // Sort themes based on priority from selected styles (if Onboarding 4 is enabled)
    // Otherwise, use default server list order
    val sortedThemes = sortThemesByPriority(hotTrendingThemes, selectedStyles)

    // Limit to maximum 6 themes for Hot Trending display at Home
    val limitedThemes = sortedThemes.take(6)

    // Update trending wallpapers list with limited themes
    trendingAdapter.submitList(limitedThemes)

    // Check if all preferences are null and apply first theme as default
    if (limitedThemes.isNotEmpty()) {
        checkAndApplyDefaultTheme(limitedThemes)
    }
}

fun HomeTabFragment.observeLoadingStateChanged() {
    // Observe loading state from CommonViewModel's hotTrendingUiState
    commonViewModel.hotTrendingUiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            // Update loading state for both ProgressBar and SwipeRefreshLayout
            binding.progressBar.isVisible = isLoading
            binding.swipeRefreshLayout.isRefreshing = isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun HomeTabFragment.navigateToEditLayoutWithTheme(theme: HotTrendingThemeModel) {
    // Apply theme to CommonViewModel first
    theme.zipper?.let {
        commonViewModel.setSelectedZipperResponse(it)
    }

    theme.wallpaper?.let {
        commonViewModel.setSelectedWallpaperImagePath(it)
    }

    theme.background?.let {
        commonViewModel.setSelectedBackgroundImagePath(it)
    }

    theme.sound?.let {
        commonViewModel.setSelectedSound(it)
    }

    theme.row?.let {
        commonViewModel.setSelectedRowResponse(it)
    }

    // Create bundle with theme data
    val bundle =
        Bundle().apply {
            theme.zipper?.let { putParcelable(BundleKey.ZIPPER_DATA, it) }
            theme.wallpaper?.let { putParcelable(BundleKey.WALLPAPER_DATA, it) }
            theme.background?.let { putParcelable(BundleKey.BACKGROUND_DATA, it) }
            theme.sound?.let { putParcelable(BundleKey.SOUND_DATA, it) }
            theme.row?.let { putParcelable(BundleKey.ROW_DATA, it) }
        }

    // Navigate to EditLayoutFragment with theme data
    safeNav(
        R.id.homeFragment,
        R.id.action_homeFragment_to_editLayoutFragment,
        bundle,
    )
}

fun HomeTabFragment.checkAndApplyDefaultTheme(hotTrendingThemes: List<HotTrendingThemeModel>) {
    // Check if all preference values are null
    val allPreferencesNull =
        prefUtil.selectedZipperResponse == null &&
            prefUtil.selectedWallpaperResponse == null &&
            prefUtil.selectedBackgroundResponse == null &&
            prefUtil.selectedSoundUrl == null &&
            prefUtil.selectedRowResponse == null

    if (allPreferencesNull && hotTrendingThemes.isNotEmpty()) {
        // Apply first theme as default
        val firstTheme = hotTrendingThemes.first()

        firstTheme.zipper?.let {
            prefUtil.selectedZipperResponse = it
            commonViewModel.setSelectedZipperResponse(it)
        }

        firstTheme.wallpaper?.let {
            prefUtil.selectedWallpaperResponse = it
            commonViewModel.setSelectedWallpaperImagePath(it)
        }

        firstTheme.background?.let {
            prefUtil.selectedBackgroundResponse = it
            commonViewModel.setSelectedBackgroundImagePath(it)
        }

        firstTheme.sound?.let {
            prefUtil.selectedSoundUrl = it.fileUrl
            prefUtil.selectedSoundImagePreviewPath = it.thumbnailUrl
            commonViewModel.setSelectedSound(it)
        }

        firstTheme.row?.let {
            prefUtil.selectedRowResponse = it
            commonViewModel.setSelectedRowResponse(it)
        }
    }
}

fun HomeTabFragment.setupShimmerEffect() {
    binding.shimmerCustomize.apply {
        // Start shimmer animation
        startShimmer()
    }
}

fun HomeTabFragment.startCustomizeShimmer() {
    binding.shimmerCustomize.startShimmer()
}

fun HomeTabFragment.stopCustomizeShimmer() {
    binding.shimmerCustomize.stopShimmer()
}

private fun HomeTabFragment.sortThemesByPriority(
    themes: List<HotTrendingThemeModel>,
    selectedStyles: List<OnboardingFavoriteStyle>,
): List<HotTrendingThemeModel> {
    // If Onboarding 4 is disabled, return default server list (no custom sorting)
    if (!Constant.isShowOnboard4) {
        return themes
    }

    // If no styles selected from onboarding, return default server list
    if (selectedStyles.isEmpty()) {
        return themes
    }

    // Collect all priority theme IDs from selected styles with their order
    val priorityThemes =
        selectedStyles
            .filter { it.isShow } // Only consider styles that should be shown
            .map { item -> item.idThemes?.split(",") ?: emptyList() }
            .flatten()
            .distinct() // Remove duplicates while preserving order
            .mapNotNull { selectedIds -> themes.find { it.id.toString() == selectedIds } }

    val nonPriorityThemes = themes.filterNot { priorityThemes.contains(it) }

    if (priorityThemes.isEmpty()) {
        return themes
    }

    return priorityThemes + nonPriorityThemes
}

fun HomeTabFragment.showInterFeatureAndDoFunction(action: () -> Unit) {
    show3LoadedInter(
        spaceNameConfig = "Home-choosefunction",
        spaceName1 = "Home-choosefunction_interstitial1",
        spaceName2 = "Home-choosefunction_interstitial2",
        spaceName3 = "",
        destinationToShowAds = R.id.homeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun HomeTabFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "Home-tabhome",
        spaceName = "Home-tabhome_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {},
    )
    safePreloadAds(
        spaceNameConfig = "Home-choosefunction",
        spaceNameAds = "Home-choosefunction_interstitial1",
    )

    safePreloadAds(
        spaceNameConfig = "Home-choosefunction",
        spaceNameAds = "Home-choosefunction_interstitial2",
    )
}
