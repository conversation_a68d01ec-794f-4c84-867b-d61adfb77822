package co.ziplock.framework.presentation.home.tabs.home

import android.content.Intent
import android.provider.Settings
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.activityViewModels
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentHomeTabBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.home.HomeViewModel
import co.ziplock.framework.presentation.home.adapter.HomeHotTrendingThemeModelAdapter

@AndroidEntryPoint
class HomeTabFragment : BaseFragment<FragmentHomeTabBinding, HomeViewModel>(
    FragmentHomeTabBinding::inflate,
    HomeViewModel::class.java
) {
    lateinit var trendingAdapter: HomeHotTrendingThemeModelAdapter
    
    // Share ViewModel with parent HomeFragment
    val sharedViewModel: HomeViewModel by activityViewModels()

    lateinit var overlayPermissionLauncher: ActivityResultLauncher<Intent>

    override fun init(view: View) {
        setupTrendingWallpapers()
        setupClickListeners()
        showAds()

        overlayPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            // Xử lý sau khi người dùng trở về từ màn hình cấp quyền
            if (Settings.canDrawOverlays(requireContext())) {
                sharedViewModel.setLockScreenEnabled(true)
            } else {
                sharedViewModel.setLockScreenEnabled(false)
            }
        }
    }

    override fun subscribeObserver(view: View) {
        observeLoadingStateChanged()
        observeHotTrendingThemes()
        observeLockScreenStatusChanged()
    }

    override fun onResume() {
        super.onResume()
        if (prefUtil.lockScreenEnabled) {
            sharedViewModel.setLockScreenEnabled(Settings.canDrawOverlays(requireContext()))
        } else {
            sharedViewModel.setLockScreenEnabled(false)
        }
        
        // Load default data from PrefUtil to EditLayoutUiState of CommonViewModel
        commonViewModel.loadDefaultDataFromPrefUtil()
        
        startCustomizeShimmer()
    }

    override fun onPause() {
        super.onPause()
        stopCustomizeShimmer()
    }

    companion object {
        const val TAG = "HomeTabFragment"
        fun newInstance() = HomeTabFragment()
    }
}

