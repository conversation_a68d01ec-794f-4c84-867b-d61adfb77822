package co.ziplock.framework.presentation.home.tabs.settings

import android.view.View
import androidx.fragment.app.activityViewModels
import co.ziplock.databinding.FragmentSettingsTabBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.home.HomeViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SettingsTabFragment :
    BaseFragment<FragmentSettingsTabBinding, HomeViewModel>(
        FragmentSettingsTabBinding::inflate,
        HomeViewModel::class.java,
    ) {
    // Share ViewModel with parent HomeFragment
    val sharedViewModel: HomeViewModel by activityViewModels()

    override fun init(view: View) {
        setupClickListeners()
        initTooltips()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observePasswordStatusChanged()
        observeShowPasswordTooltip()
        observeSettingsStatusChanged()
    }

    override fun onStart() {
        super.onStart()
        if (!sharedViewModel.hasExistingPassword()) {
            sharedViewModel.setPasswordEnabled(false)
        }

        sharedViewModel.setVibrationEnabled(prefUtil.vibrationEnabled)
    }

    companion object {
        const val TAG = "SettingsTabFragment"

        fun newInstance() = SettingsTabFragment()
    }
}
