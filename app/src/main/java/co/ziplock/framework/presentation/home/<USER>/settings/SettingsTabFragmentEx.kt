package co.ziplock.framework.presentation.home.tabs.settings

import android.provider.Settings
import android.view.View
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import co.ziplock.R
import co.ziplock.framework.presentation.dialog.ForgotPasswordDialogFragment
import co.ziplock.framework.presentation.dialog.PasswordType
import co.ziplock.framework.presentation.dialog.PasswordTypeSelectionDialogFragment
import co.ziplock.framework.presentation.dialog.PatternVerificationDialogFragment
import co.ziplock.framework.presentation.dialog.PinVerificationDialogFragment
import co.ziplock.framework.presentation.manager.SecurityManager.SecurityType
import co.ziplock.util.BundleKey
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun SettingsTabFragment.observePasswordStatusChanged() {
    sharedViewModel.settingsTabUiState
        .map { it.passwordEnabled }
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { isEnabled ->
            // Update switch without triggering listener
            binding.switchPassword.isChecked = isEnabled
        }.onEach { isEnabled ->
            // Check if password already exists
            if (isEnabled && !sharedViewModel.hasExistingPassword()) {
                // First time setup - navigate to password type selection
                showPasswordTypeSelectionDialogFragment(isFirstSetupPassword = true)
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SettingsTabFragment.observeSettingsStatusChanged() {
    // Observe Sound setting
    sharedViewModel.settingsTabUiState
        .map { it.soundEnabled }
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { isEnabled ->
            binding.switchSound.setOnCheckedChangeListener(null)
            binding.switchSound.isChecked = isEnabled
            setUpSwitchSoundCheckChanged()
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    // Observe Vibration setting
    sharedViewModel.settingsTabUiState
        .map { it.vibrationEnabled }
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { isEnabled ->
            binding.switchVibration.setOnCheckedChangeListener(null)
            binding.switchVibration.isChecked = isEnabled
            setUpSwitchVibrationCheckChanged()
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    // Observe Date & Time setting
    sharedViewModel.settingsTabUiState
        .map { it.dateTimeEnabled }
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { isEnabled ->
            binding.switchDateTime.setOnCheckedChangeListener(null)
            binding.switchDateTime.isChecked = isEnabled
            setUpSwitchDateTimeCheckChanged()
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    // Observe Battery Widget setting
    sharedViewModel.settingsTabUiState
        .map { it.batteryWidgetEnabled }
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { isEnabled ->
            binding.switchBatteryWidget.setOnCheckedChangeListener(null)
            binding.switchBatteryWidget.isChecked = isEnabled
            setUpSwitchBatteryWidgetCheckChanged()
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SettingsTabFragment.observeShowPasswordTooltip() {
    combine(
        sharedViewModel.settingsTabUiState.map { it.lockScreenEnabled },
        sharedViewModel.settingsTabUiState.map { it.passwordEnabled },
    ) { lockScreenEnabled, passwordEnabled ->
        lockScreenEnabled &&
            !passwordEnabled &&
            !sharedViewModel.hasExistingPassword() &&
            Settings.canDrawOverlays(
                requireContext(),
            )
    }.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .distinctUntilChanged()
        .onEach { isEnabled ->
            // Handle tooltip for password
            handlePasswordTooltip(isEnabled)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SettingsTabFragment.setupClickListeners() {
    binding.apply {
        setUpSwitchPasswordCheckChanged()

        // Change password
        settingChangePassword.setPreventDoubleClick {
            if (sharedViewModel.hasExistingPassword()) {
                // Show verification dialog based on current security type
                when (prefUtil.securityType) {
                    SecurityType.PIN.name -> {
                        showPinVerificationDialog()
                    }

                    SecurityType.PATTERN.name -> {
                        showPatternVerificationDialog()
                    }

                    else -> {
                        // No security or unknown type, show password type selection
                        showPasswordTypeSelectionDialogFragment(isFirstSetupPassword = true)
                    }
                }
            } else {
                showPasswordTypeSelectionDialogFragment(isFirstSetupPassword = true)
            }
        }

        // Password recovery
        settingPasswordRecovery.setPreventDoubleClick {
            safeNav(
                R.id.homeFragment,
                R.id.action_homeFragment_to_passwordRecoveryMethodFragment,
            )
        }
    }
}

fun SettingsTabFragment.setUpSwitchPasswordCheckChanged() {
    // Password toggle
    binding.switchPassword.setPreventDoubleClick {
        val isChecked = binding.switchPassword.isChecked
        if (isChecked) {
            // Hide password tooltip when password is enabled
            hidePasswordTooltip()
        }
        sharedViewModel.setPasswordEnabled(isChecked)
        if (!isChecked) {
            showInterChoosePasswordTypeAndDoAction { }
        } else {
            if (sharedViewModel.hasExistingPassword()) {
                showInterChoosePasswordTypeAndDoAction { }
            }
        }
    }
}

fun SettingsTabFragment.showInterToggleSwitchAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "Home-sett-on/off",
        spaceName = "Home-1ID_interstitial",
        destinationToShowAds = R.id.homeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun SettingsTabFragment.setUpSwitchSoundCheckChanged() {
    binding.switchSound.setPreventDoubleClick {
        val isChecked = binding.switchSound.isChecked
        showInterToggleSwitchAndDoAction {
            sharedViewModel.setSoundEnabled(isChecked)
        }
    }
}

fun SettingsTabFragment.setUpSwitchVibrationCheckChanged() {
    binding.switchVibration.setPreventDoubleClick {
        val isChecked = binding.switchSound.isChecked
        showInterToggleSwitchAndDoAction {
            sharedViewModel.setVibrationEnabled(isChecked)
        }
    }
}

fun SettingsTabFragment.setUpSwitchDateTimeCheckChanged() {
    binding.switchDateTime.setPreventDoubleClick {
        val isChecked = binding.switchSound.isChecked
        showInterChoosePasswordTypeAndDoAction {
            sharedViewModel.setDateTimeEnabled(isChecked)
        }
    }
}

fun SettingsTabFragment.setUpSwitchBatteryWidgetCheckChanged() {
    binding.switchBatteryWidget.setPreventDoubleClick {
        val isChecked = binding.switchSound.isChecked
        showInterChoosePasswordTypeAndDoAction {
            sharedViewModel.setBatteryWidgetEnabled(isChecked)
        }
    }
}

fun SettingsTabFragment.showPasswordTypeSelectionDialogFragment(
    isFirstSetupPassword: Boolean,
    initialPasswordType: PasswordType = PasswordType.PIN,
) {
    val dialogFragment = PasswordTypeSelectionDialogFragment.newInstance(initialPasswordType)

    dialogFragment.onPasswordTypeSelected = { passwordType ->
        when (passwordType) {
            PasswordType.PIN -> {
                dialogFragment.dismissAllowingStateLoss()
                // Navigate to PIN setup fragment
                showInterChoosePasswordTypeAndDoAction {
                    safeNavInter(
                        R.id.homeFragment,
                        R.id.action_homeFragment_to_pinSetupFragment,
                        bundleOf(BundleKey.KEY_FIRST_SETUP_PASSWORD to isFirstSetupPassword),
                    )
                }
            }

            PasswordType.PATTERN -> {
                dialogFragment.dismissAllowingStateLoss()
                // Navigate to Pattern setup fragment
                showInterChoosePasswordTypeAndDoAction {
                    safeNavInter(
                        R.id.homeFragment,
                        R.id.action_homeFragment_to_patternSetupFragment,
                        bundleOf(BundleKey.KEY_FIRST_SETUP_PASSWORD to isFirstSetupPassword),
                    )
                }
            }
        }
    }

    dialogFragment.onCancel = {
        if (isFirstSetupPassword) {
            // Cancel button clicked, do nothing or revert changes as needed
            sharedViewModel.setPasswordEnabled(false)
        }
    }

    dialogFragment.show(parentFragmentManager, SettingsTabFragment.TAG)
}

fun SettingsTabFragment.showInterChoosePasswordTypeAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "Home-pass-on/off",
        spaceName = "Home-1ID_interstitial",
        destinationToShowAds = R.id.homeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

// Tooltip management functions
fun SettingsTabFragment.initTooltips() {
    binding.tooltipPassword.visibility = View.GONE
}

fun SettingsTabFragment.handlePasswordTooltip(isEnabled: Boolean) {
    if (isEnabled) {
        // Show tooltip when lock screen is ON but password is OFF and no existing password
        showPasswordTooltip()
    } else {
        // Hide tooltip when password is ON or conditions not met
        hidePasswordTooltip()
    }
}

fun SettingsTabFragment.showPasswordTooltip() {
    binding.tooltipPassword.visibility = View.VISIBLE
    binding.tooltipPassword.playAnimation()

    // Show toast message
    Toast
        .makeText(
            requireContext(),
            getString(R.string.tooltip_password_message),
            Toast.LENGTH_LONG,
        ).show()
}

fun SettingsTabFragment.hidePasswordTooltip() {
    binding.tooltipPassword.visibility = View.GONE
    binding.tooltipPassword.pauseAnimation()
}

fun SettingsTabFragment.showPinVerificationDialog() {
    val dialogFragment = PinVerificationDialogFragment.newInstance()

    dialogFragment.onPinVerified = {
        // PIN verified successfully, show password type selection for changing password
        showPasswordTypeSelectionDialogFragment(isFirstSetupPassword = false)
    }

    dialogFragment.onCancel = {
        // User cancelled, do nothing
    }

    dialogFragment.onForgotPassword = {
        // Show password recovery method dialog
        showPasswordRecoveryMethodDialog()
    }

    dialogFragment.show(parentFragmentManager, PinVerificationDialogFragment.TAG)
}

fun SettingsTabFragment.showPatternVerificationDialog() {
    val dialogFragment = PatternVerificationDialogFragment.newInstance()

    dialogFragment.onPatternVerified = {
        // Pattern verified successfully, show password type selection for changing password
        showPasswordTypeSelectionDialogFragment(
            isFirstSetupPassword = false,
            initialPasswordType = PasswordType.PATTERN,
        )
    }

    dialogFragment.onCancel = {
        // User cancelled, do nothing
    }

    dialogFragment.onForgotPassword = {
        // Show password recovery method dialog
        showPasswordRecoveryMethodDialog()
    }

    dialogFragment.show(parentFragmentManager, PatternVerificationDialogFragment.TAG)
}

fun SettingsTabFragment.showPasswordRecoveryMethodDialog() {
    ForgotPasswordDialogFragment.show(
        fragmentManager = parentFragmentManager,
        onSecurityQuestionSelected = {
            // Navigate to security question answer fragment
            safeNav(
                R.id.homeFragment,
                R.id.action_homeFragment_to_securityQuestionForgotPasswordFragment,
            )
        },
        onEmailRecoverySelected = {
            // Navigate to email password recovery fragment
            safeNav(
                R.id.homeFragment,
                R.id.action_homeFragment_to_emailPasswordRecoveryWhenForgotPasswordFragment,
            )
        },
        onCancel = {
            // User cancelled, do nothing
        },
    )
}

fun SettingsTabFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "Home-tabset",
        spaceName = "Home-tabset_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {},
    )
}
