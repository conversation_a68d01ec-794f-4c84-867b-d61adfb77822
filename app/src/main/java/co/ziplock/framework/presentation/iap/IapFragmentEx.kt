package co.ziplock.framework.presentation.iap

import android.content.ActivityNotFoundException
import android.content.Intent
import android.graphics.Paint
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.navigation.fragment.findNavController
import co.ziplock.BuildConfig
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import com.example.libiap.IAPConnector
import pion.datlt.libads.AdsController
import pion.datlt.libads.utils.adsuntils.show3LoadedInter

fun IapFragment.initView() {
    isOnboard = arguments?.getBoolean(BundleKey.KEY_ONBOARD_IAP) == true

    binding.layoutNoAds.tvContent.text = getString(R.string.no_ads)
    binding.layoutPremiumContent.tvContent.text = getString(R.string.premium_contents)
    binding.layoutAllFunction.tvContent.text = getString(R.string.all_functions)
    binding.layoutQuickOpenApp.tvContent.text = getString(R.string.quick_open_app)

    binding.btnSkip.isVisible = isOnboard

    binding.tvPrice.text = IAPConnector.getProductById(Constant.iapId)?.formattedPrice ?: ""
    binding.tvPriceOld.apply {
        text = IAPConnector.getProductById(Constant.iapGiaGach)?.formattedPrice ?: ""
        paintFlags = paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
    }
}

fun IapFragment.skipEvent() {
    binding.btnSkip.setPreventDoubleClick {
        if (isOnboard) {
            show3LoadedInter(
                spaceNameConfig = "onboardiap",
                spaceName1 = "onboardiap_interstitial1",
                spaceName2 = "onboardiap_interstitial2",
                spaceName3 = "onboardiap_interstitial3",
                destinationToShowAds = R.id.onboardingFragment,
                isShowLoadingView = true,
                isScreenType = false,
                navOrBack = {
                    safeNavInter(
                        R.id.onboardingFragment,
                        R.id.action_onboardingFragment_to_homeFragment,
                    )
                },
                onCloseAds = {},
            )
        }
    }
}

fun IapFragment.onBackEvent() {
    onSystemBackEvent {
        backEvent()
    }
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun IapFragment.backEvent() {
    if (isOnboard) {
        commonViewModel.sendGoToPreviousOnboardingScreenEvent()
    } else {
        findNavController().navigateUp()
    }
}

fun IapFragment.termOfUseEvent() {
    binding.tvTermOfUse.setPreventDoubleClick {
        try {
            val browserIntent =
                Intent(
                    Intent.ACTION_VIEW,
                    "https://sites.google.com/view/in-app-purchase-terms-of-use/home".toUri(),
                )
            AdsController.isBlockOpenAds = true
            requireContext().startActivity(browserIntent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

fun IapFragment.restoreEvent() {
    binding.tvRestore.setPreventDoubleClick {
        try {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    (
                        "https://play.google.com/store/account/subscriptions?" +
                            "&package=${BuildConfig.APPLICATION_ID}"
                    ).toUri(),
                ),
            )
        } catch (e: ActivityNotFoundException) {
            e.printStackTrace()
        }
    }
}

fun IapFragment.useAdsVersionEvent() {
    binding.tvUseAdsVersion.setPreventDoubleClick {
        if (isOnboard) {
            commonViewModel.sendGoToNextOnboardingScreenEvent()
        } else {
            findNavController().navigateUp()
        }
    }
}

fun IapFragment.subscribeEvent() {
    binding.btnSubscribe.setPreventDoubleClick {
        IAPConnector.buyIap(requireActivity(), Constant.iapId)
    }
}
