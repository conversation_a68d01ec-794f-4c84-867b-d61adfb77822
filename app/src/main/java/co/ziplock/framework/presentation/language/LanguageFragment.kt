package co.ziplock.framework.presentation.language

import android.view.View
import co.ziplock.framework.presentation.language.adapter.LanguageAdapter
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentLanguageBinding
import co.ziplock.framework.presentation.common.BaseFragment

@AndroidEntryPoint
class LanguageFragment :
    BaseFragment<FragmentLanguageBinding, LanguageViewModel>(
        FragmentLanguageBinding::inflate,
        LanguageViewModel::class.java,
    ) {
    val languageAdapter by lazy { LanguageAdapter() }
    var isNeedShowReloadAds = false
    var isShowReloadAds = false

    override fun init(view: View) {
        initView()
        initLanguages(requireContext())
        setupRecyclerView()
        setupClickListeners()
        onBackEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeLanguageListState()
        observeSelectedLanguageState()
    }

    override fun onResume() {
        super.onResume()
        showReloadAds()
    }

    companion object {
        const val TAG = "LanguageFragment"
        const val KEY_LANGUAGE_SCREEN_TYPE = "KEY_LANGUAGE_SCREEN_TYPE"
    }
}
