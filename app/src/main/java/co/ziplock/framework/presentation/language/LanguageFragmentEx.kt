package co.ziplock.framework.presentation.language

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.language.LanguageFragment.Companion.TAG
import co.ziplock.framework.presentation.model.language.Language
import co.ziplock.framework.service.ZipperOverlayService
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.show3NativeUsePriority
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber
import java.util.Locale

fun LanguageFragment.initView() {
    binding.tvTitle.isVisible = false
    binding.btnBack.isVisible = false
    if (isCameFromSetting()) {
        binding.btnBack.isVisible = true
        binding.tvTitle.isVisible = true
    } else {
        binding.tvTitle.isVisible = true
    }
}

fun LanguageFragment.onBackEvent() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }

    onSystemBackEvent {
        backEvent()
    }
}

fun LanguageFragment.backEvent() {
    if (isCameFromSetting()) {
        findNavController().navigateUp()
    } else {
        // Nothing
    }
}

fun LanguageFragment.setupRecyclerView() {
    binding.rvLanguages.adapter = languageAdapter

    languageAdapter.onItemClick = { position ->
        isNeedShowReloadAds = true
        showReloadAds()
        viewModel.selectLanguage(position)
        binding.ivNext.isVisible = true
    }
}

fun LanguageFragment.initLanguages(context: Context) {
    val languages =
        listOf(
            Language(
                0,
                context.getString(R.string.language_english),
                "en",
                R.drawable.us,
            ),
            Language(
                1,
                context.getString(R.string.language_spanish),
                "es",
                R.drawable.es,
            ),
            Language(
                2,
                context.getString(R.string.language_arabic),
                "ar",
                R.drawable.sa,
            ),
            Language(
                3,
                context.getString(R.string.language_portuguese),
                "pt",
                R.drawable.pt,
            ),
            Language(
                4,
                context.getString(R.string.language_french),
                "fr",
                R.drawable.fr,
            ),
            Language(
                5,
                context.getString(R.string.language_german),
                "de",
                R.drawable.de,
            ),
            Language(
                6,
                context.getString(R.string.language_chinese),
                "zh",
                R.drawable.cn,
            ),
            Language(
                7,
                context.getString(R.string.language_korean),
                "ko",
                R.drawable.kr,
            ),
            Language(
                8,
                context.getString(R.string.language_japanese),
                "ja",
                R.drawable.jp,
            ),
            Language(
                9,
                context.getString(R.string.language_russian),
                "ru",
                R.drawable.ru,
            ),
            Language(
                10,
                context.getString(R.string.language_vietnamese),
                "vi",
                R.drawable.vn,
            ),
            Language(
                11,
                context.getString(R.string.language_thai),
                "th",
                R.drawable.th,
            ),
            Language(
                12,
                context.getString(R.string.language_turkish),
                "tr",
                R.drawable.tr,
            ),
            Language(
                13,
                context.getString(R.string.language_hindi),
                "hi",
                R.drawable.`in`,
            ),
            Language(
                14,
                context.getString(R.string.language_uzbek),
                "uz",
                R.drawable.uz,
            ),
            Language(
                15,
                context.getString(R.string.language_italian),
                "it",
                R.drawable.it,
            ),
            Language(
                16,
                context.getString(R.string.language_polish),
                "pl",
                R.drawable.pl,
            ),
            Language(
                17,
                context.getString(R.string.language_persian),
                "fa",
                R.drawable.ir,
            ),
            Language(
                18,
                context.getString(R.string.language_ukrainian),
                "uk",
                R.drawable.ua,
            ),
            Language(
                19,
                context.getString(R.string.language_romanian),
                "ro",
                R.drawable.ro,
            ),
            Language(
                20,
                context.getString(R.string.language_dutch),
                "nl",
                R.drawable.nl,
            ),
            Language(
                21,
                context.getString(R.string.language_hungarian),
                "hu",
                R.drawable.hu,
            ),
            Language(
                22,
                context.getString(R.string.language_bulgarian),
                "bg",
                R.drawable.bg,
            ),
            Language(
                23,
                context.getString(R.string.language_greek),
                "el",
                R.drawable.gr,
            ),
        )
    viewModel.setLanguages(languages)
}

fun LanguageFragment.applySelectedLanguage() {
    val selectedLanguage = viewModel.uiState.value.selectedLanguage ?: return

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        // Sử dụng Per-app language preferences cho Android 13+
        val localeList = LocaleListCompat.forLanguageTags(selectedLanguage.locale)
        AppCompatDelegate.setApplicationLocales(localeList)
    } else {
        // Sử dụng cách cũ cho Android < 13
        val locale = Locale(selectedLanguage.locale)
        Locale.setDefault(locale)

        val config = requireContext().resources.configuration
        config.setLocale(locale)

        @Suppress("DEPRECATION")
        requireContext().resources.updateConfiguration(
            config,
            requireContext().resources.displayMetrics,
        )
    }

    // Lưu ngôn ngữ đã chọn
    prefUtil.selectedLanguage = selectedLanguage.locale

    // Refresh Service context if it's running
    refreshServiceContext()
}

/**
 * Refresh Service context when language is changed
 */
fun LanguageFragment.refreshServiceContext() {
    try {
        // Send broadcast to refresh context for Service if it's running
        val intent = Intent(ZipperOverlayService.ACTION_REFRESH_CONTEXT)
        requireContext().sendBroadcast(intent)

        // Also send direct action to Service as backup
        ZipperOverlayService.refreshContext(requireContext())

        Timber
            .tag(TAG)
            .d("refreshServiceContext: Sent refresh context broadcast and action to Service")
    } catch (e: Exception) {
        Timber.tag(TAG).e(e, "refreshServiceContext: Error refreshing service context")
    }
}

fun LanguageFragment.setupClickListeners() {
    binding.ivNext.setPreventDoubleClick {
        applySelectedLanguage()
        navigateToNextScreen()
    }
}

fun LanguageFragment.navigateToNextScreen() {
    if (isCameFromSetting()) {
        Constant.isLanguageToSplash = true
        safeNav(R.id.languageFragment, R.id.action_languageFragment_to_splashFragment)
    } else {
        safeNav(R.id.languageFragment, R.id.action_languageFragment_to_onboardingFragment)
    }
}

fun LanguageFragment.observeSelectedLanguageState() {
    viewModel.uiState
        .map { it.selectedLanguage }
        .distinctUntilChanged()
        .onEach { selectedLanguage ->
            binding.ivNext.isEnabled = selectedLanguage != null
        }.filterNotNull()
        .onEach { selectedLanguage ->
            // selected language
            Timber.tag(TAG).d("observeSelectedLanguageState: selectedLanguage=$selectedLanguage")
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun LanguageFragment.observeLanguageListState() {
    viewModel.uiState
        .map { it.languages }
        .distinctUntilChanged()
        .onEach { languages ->
            languageAdapter.submitList(languages)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun LanguageFragment.isCameFromSetting(): Boolean = navController.previousBackStackEntry?.destination?.id == R.id.generalSettingFragment

fun LanguageFragment.showAds() {
    show3NativeUsePriority(
        spaceNameConfig = "Language1.1",
        spaceName1 = "language1_native1",
        spaceName2 = "language1_native2",
        spaceName3 = "language1_native3",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {
            isNeedShowReloadAds = true
        },
    )

    showLoadedNative(
        spaceNameConfig = "Language2.1",
        spaceName = "Language2.1_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {
            isNeedShowReloadAds = true
        },
    )
    preloadAdsOnboard()
}

fun LanguageFragment.preloadAdsOnboard() {
    if (isCameFromSetting()) return

    // Onboard 1
    val onboard11ConfigName = "Onboard1.1"
    safePreloadAds(
        spaceNameConfig = onboard11ConfigName,
        spaceNameAds = "Onboard1_native1",
        includeHasBeenOpened = true,
    )
    safePreloadAds(
        spaceNameConfig = onboard11ConfigName,
        spaceNameAds = "Onboard1_native2",
        includeHasBeenOpened = true,
    )
    safePreloadAds(
        spaceNameConfig = onboard11ConfigName,
        spaceNameAds = "Onboard1_native3",
        includeHasBeenOpened = true,
    )

    // Onboard 2
    safePreloadAds(
        spaceNameConfig = "Onboard2",
        spaceNameAds = "Onboard2_native",
        includeHasBeenOpened = true,
    )

    // Onboard Full 1.1
    val onboardFull11ConfigName = "onboardfull1.1"
    safePreloadAds(
        spaceNameConfig = onboardFull11ConfigName,
        spaceNameAds = "onboardfull_native1",
        includeHasBeenOpened = true,
    )
    safePreloadAds(
        spaceNameConfig = onboardFull11ConfigName,
        spaceNameAds = "onboardfull_native2",
        includeHasBeenOpened = true,
    )
    safePreloadAds(
        spaceNameConfig = onboardFull11ConfigName,
        spaceNameAds = "onboardfull_native3",
        includeHasBeenOpened = true,
    )

    // Onboard 3
    safePreloadAds(
        spaceNameConfig = "onboard3",
        spaceNameAds = "onboard3_native",
        includeHasBeenOpened = true,
    )

    // Onboard 4
    if (Constant.isShowOnboard4) {
        safePreloadAds(
            spaceNameConfig = "onboard4.1",
            spaceNameAds = "onboard4.1_native",
            includeHasBeenOpened = true,
        )
        safePreloadAds(
            spaceNameConfig = "onboard4.2",
            spaceNameAds = "onboard4.2_native",
            includeHasBeenOpened = true,
        )
    }

    // Onboard Full 2.1
    val onboardFull21ConfigName = "onboardfull2.1"
    safePreloadAds(
        spaceNameConfig = onboardFull21ConfigName,
        spaceNameAds = "onboardfull2_native1",
        includeHasBeenOpened = true,
    )
    safePreloadAds(
        spaceNameConfig = onboardFull21ConfigName,
        spaceNameAds = "onboardfull2_native2",
        includeHasBeenOpened = true,
    )
    safePreloadAds(
        spaceNameConfig = onboardFull21ConfigName,
        spaceNameAds = "onboardfull2_native3",
        includeHasBeenOpened = true,
    )

    // Onboard IAP
    val onboardIAPConfigName = "onboardiap"
    safePreloadAds(
        spaceNameConfig = onboardIAPConfigName,
        spaceNameAds = "onboardiap_interstitial1",
    )
    safePreloadAds(
        spaceNameConfig = onboardIAPConfigName,
        spaceNameAds = "onboardiap_interstitial2",
    )
    safePreloadAds(
        spaceNameConfig = onboardIAPConfigName,
        spaceNameAds = "onboardiap_interstitial3",
    )
}

fun LanguageFragment.showReloadAds() {
    if (!isShowReloadAds && isNeedShowReloadAds) {
        isShowReloadAds = true
        isNeedShowReloadAds = false
        show3NativeUsePriority(
            spaceNameConfig = "Language1.2",
            spaceName1 = "language1_native4",
            spaceName2 = "language1_native5",
            spaceName3 = "language1_native6",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )

        showLoadedNative(
            spaceNameConfig = "Language2.2",
            spaceName = "Language2.2_native",
            layoutToAttachAds = binding.adViewGroupTop,
            layoutContainAds = binding.layoutAdsTop,
        )
    }
}
