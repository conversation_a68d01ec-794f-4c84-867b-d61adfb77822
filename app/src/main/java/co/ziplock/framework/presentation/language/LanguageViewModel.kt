package co.ziplock.framework.presentation.language

import co.ziplock.framework.presentation.model.language.Language
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import co.ziplock.framework.presentation.common.BaseViewModel
import javax.inject.Inject

@HiltViewModel
class LanguageViewModel @Inject constructor() : BaseViewModel() {

    private val _uiState = MutableStateFlow(LanguageUiState())
    val uiState: StateFlow<LanguageUiState> = _uiState.asStateFlow()


    fun setLanguages(listLanguage: List<Language>) {
        _uiState.update {
            it.copy(languages = listLanguage)
        }
    }

    fun selectLanguage(position: Int) {
        val currentLanguages = _uiState.value.languages.toMutableList()

        // Deselect all languages
        currentLanguages.forEachIndexed { index, language ->
            if (language.isSelected && index != position) {
                currentLanguages[index] = language.copy(isSelected = false)
            }
        }

        // Select the chosen language
        val selectedLanguage = currentLanguages[position]
        currentLanguages[position] = selectedLanguage.copy(isSelected = true)

        _uiState.update {
            it.copy(
                languages = currentLanguages, selectedLanguage = currentLanguages[position]
            )
        }
    }

    data class LanguageUiState(
        val languages: List<Language> = emptyList(),
        val selectedLanguage: Language? = null
    )
}