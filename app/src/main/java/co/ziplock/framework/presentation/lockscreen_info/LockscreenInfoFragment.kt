package co.ziplock.framework.presentation.lockscreen_info

import android.content.Intent
import android.provider.Settings
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import co.ziplock.databinding.FragmentLockscreenInfoBinding
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.lockscreen_info.adapter.FontColorAdapter
import co.ziplock.framework.presentation.lockscreen_info.adapter.FontFamilyAdapter
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.util.BundleKey
import co.ziplock.util.parcelable
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

@AndroidEntryPoint
class LockscreenInfoFragment :
    BaseFragment<FragmentLockscreenInfoBinding, LockscreenInfoViewModel>(
        FragmentLockscreenInfoBinding::inflate,
        LockscreenInfoViewModel::class.java,
    ) {
    lateinit var fontFamilyAdapter: FontFamilyAdapter
    lateinit var fontColorAdapter: FontColorAdapter
    var selectedZipper: ZipperResponse? = null
    var selectedWallpaper: WallpaperResponse? = null
    var selectedLocalWallpaper: LocalImageData? = null
    var selectedBackground: BackgroundResponse? = null
    var selectedLocalBackground: LocalImageData? = null
    var selectedSound: SoundResponse? = null
    var selectedRow: RowResponse? = null

    lateinit var overlayPermissionLauncher: ActivityResultLauncher<Intent>

    var isShowedReloadAds = false
    var isNeedShowReloadAds = false

    override fun init(view: View) {
        // Get data from arguments
        selectedZipper = arguments?.parcelable(BundleKey.ZIPPER_DATA)
        selectedWallpaper = arguments?.parcelable(BundleKey.WALLPAPER_DATA)
        selectedLocalWallpaper = arguments?.parcelable(BundleKey.LOCAL_WALLPAPER_DATA)
        selectedBackground = arguments?.parcelable(BundleKey.BACKGROUND_DATA)
        selectedLocalBackground = arguments?.parcelable(BundleKey.LOCAL_BACKGROUND_DATA)
        selectedSound = arguments?.parcelable(BundleKey.SOUND_DATA)
        selectedRow = arguments?.parcelable(BundleKey.ROW_DATA)

        // Update EditLayoutUiState with data from arguments (if not already set)
        updateEditLayoutUiStateFromArguments()

        setupUI()
        setupRecyclerViews()
        setupClickListeners()
        setupSystemBackEvent()
        loadPreview()
        loadPreviewData()
        loadAllPreviewSettings()

        // Load saved font and color settings
        loadSavedFontAndColorSettings()
        showAds()

        overlayPermissionLauncher =
            registerForActivityResult(
                ActivityResultContracts.StartActivityForResult(),
            ) { result ->
                // Xử lý sau khi người dùng trở về từ màn hình cấp quyền
                if (Settings.canDrawOverlays(requireContext())) {
                    prefUtil.lockScreenEnabled = true
                    // navigateToSaveSuccess()
                } else {
                    prefUtil.lockScreenEnabled = false
                }
            }
    }

    override fun subscribeObserver(view: View) {
        observeSelectedFontFamily()
        observeSelectedFontColor()
        observeSettingsChanges()
    }

    override fun onResume() {
        super.onResume()
        showReloadAds()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isShowedReloadAds = false
        isNeedShowReloadAds = false
    }

    private fun observeSettingsChanges() {
        // Observe settings changes from LockscreenInfoViewModel
        viewModel.settingsTabUiState
            .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
            .distinctUntilChanged()
            .onEach { settingsState ->
                // Refresh preview when settings change
                refreshPreview()
            }.launchIn(viewLifecycleOwner.lifecycleScope)
    }

    companion object {
        const val TAG = "LockscreenInfoFragment"
    }
}
