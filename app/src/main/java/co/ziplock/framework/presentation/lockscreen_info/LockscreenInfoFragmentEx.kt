package co.ziplock.framework.presentation.lockscreen_info

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.provider.Settings
import androidx.core.net.toUri
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import co.ziplock.R
import co.ziplock.framework.presentation.common.doActionWhenStop
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.dialog.EnableLockScreenDialogFragment
import co.ziplock.framework.presentation.dialog.ZipperFullScreenDialogFragment
import co.ziplock.framework.presentation.lockscreen_info.adapter.FontColorAdapter
import co.ziplock.framework.presentation.lockscreen_info.adapter.FontFamilyAdapter
import co.ziplock.framework.presentation.model.FontData
import co.ziplock.framework.service.SettingsNotificationService
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import pion.datlt.libads.AdsController
import pion.datlt.libads.utils.DialogNative
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun LockscreenInfoFragment.setupSystemBackEvent() {
    onSystemBackEvent {
        findNavController().navigateUp()
    }
}

fun LockscreenInfoFragment.updateEditLayoutUiStateFromArguments() {
    // Update EditLayoutUiState with data from arguments if EditLayoutUiState is empty
    val editLayoutState = commonViewModel.editLayoutUiState.value

    // Only update if EditLayoutUiState doesn't already have the data
    selectedZipper?.let { zipper ->
        if (editLayoutState.zipperResponse == null) {
            commonViewModel.setEditLayoutZipper(zipper)
        }
    }

    selectedWallpaper?.let { wallpaper ->
        if (editLayoutState.wallpaperResponse == null && editLayoutState.localWallpaper == null) {
            commonViewModel.setEditLayoutWallpaper(wallpaper)
        }
    }

    selectedLocalWallpaper?.let { localWallpaper ->
        if (editLayoutState.wallpaperResponse == null && editLayoutState.localWallpaper == null) {
            commonViewModel.setEditLayoutLocalWallpaper(localWallpaper)
        }
    }

    selectedBackground?.let { background ->
        if (editLayoutState.backgroundResponse == null && editLayoutState.localBackground == null) {
            commonViewModel.setEditLayoutBackground(background)
        }
    }

    selectedLocalBackground?.let { localBackground ->
        if (editLayoutState.backgroundResponse == null && editLayoutState.localBackground == null) {
            commonViewModel.setEditLayoutLocalBackground(localBackground)
        }
    }

    selectedSound?.let { sound ->
        if (editLayoutState.soundResponse == null) {
            commonViewModel.setEditLayoutSound(sound)
        }
    }

    selectedRow?.let { row ->
        if (editLayoutState.rowResponse == null) {
            commonViewModel.setEditLayoutRow(row)
        }
    }
}

fun LockscreenInfoFragment.setupUI() {
    // Setup initial UI state
    updatePreviewText()
}

fun LockscreenInfoFragment.setupRecyclerViews() {
    // Font Family RecyclerView
    fontFamilyAdapter =
        FontFamilyAdapter { fontFamily ->
            isNeedShowReloadAds = true
            showReloadAds()
            viewModel.selectFontFamily(fontFamily)
        }

    binding.rvFontFamilies.apply {
        layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        adapter = fontFamilyAdapter
    }

    fontFamilyAdapter.submitList(FontData.fontFamilies)

    // Font Color RecyclerView
    fontColorAdapter =
        FontColorAdapter { fontColor ->
            isNeedShowReloadAds = true
            showReloadAds()
            viewModel.selectFontColor(fontColor)
        }

    binding.rvFontColors.apply {
        layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        adapter = fontColorAdapter
    }

    fontColorAdapter.submitList(FontData.fontColors)
}

fun LockscreenInfoFragment.setupClickListeners() {
    binding.ivBack.setPreventDoubleClick {
        findNavController().navigateUp()
    }

    binding.tvApply.setPreventDoubleClick {
        applyChanges()
    }

    binding.ivPreview.setPreventDoubleClick {
        startPreviewService()
    }
}

fun LockscreenInfoFragment.loadPreview() {
    // Setup ZipperView in preview mode with 0% progress (closed state)
    binding.zipperPreview.setPreviewMode(true, previewProgress = 0f)
    // Set style if available from selected zipper
    // binding.zipperPreview.setStyle(co.ziplock.customview.StyleRow.STYLE_1)
}

fun LockscreenInfoFragment.loadPreviewData() {
    // Get data from EditLayoutUiState first, then fallback to arguments and PrefUtil
    val editLayoutState = commonViewModel.editLayoutUiState.value

    // Load zipper image
    val zipperResponse =
        editLayoutState.zipperResponse
            ?: selectedZipper
            ?: prefUtil.selectedZipperResponse

    if (zipperResponse != null) {
        // Set ZipperResponse for draw_height and image loading
        binding.zipperPreview.setZipperResponse(zipperResponse)
    }

    // Load wallpaper
    val wallpaperPath =
        when {
            editLayoutState.wallpaperResponse != null -> editLayoutState.wallpaperResponse.fileUrl
            editLayoutState.localWallpaper != null -> editLayoutState.localWallpaper.filePath
            selectedWallpaper != null -> selectedWallpaper!!.fileUrl
            selectedLocalWallpaper != null -> selectedLocalWallpaper!!.filePath
            else -> prefUtil.selectedWallpaperResponse?.fileUrl
        }

    if (!wallpaperPath.isNullOrEmpty()) {
        if (wallpaperPath.startsWith("http")) {
            loadWallpaperImageFromUrl(wallpaperPath)
        } else {
            loadWallpaperImageFromPath(wallpaperPath)
        }
    }

    // Load background
    val backgroundPath =
        when {
            editLayoutState.backgroundResponse != null -> editLayoutState.backgroundResponse.fileUrl
            editLayoutState.localBackground != null -> editLayoutState.localBackground.filePath
            selectedBackground != null -> selectedBackground!!.fileUrl
            selectedLocalBackground != null -> selectedLocalBackground!!.filePath
            else -> prefUtil.selectedBackgroundResponse?.fileUrl
        }

    if (!backgroundPath.isNullOrEmpty()) {
        if (backgroundPath.startsWith("http")) {
            loadBackgroundImageFromUrl(backgroundPath)
        } else {
            loadBackgroundImageFromPath(backgroundPath)
        }
    }

    // Load row data
    val rowResponse =
        editLayoutState.rowResponse
            ?: selectedRow
            ?: prefUtil.selectedRowResponse

    rowResponse?.let { row ->
        binding.zipperPreview.setRowResponse(row)
    }

    // Load sound
    val soundUrl =
        editLayoutState.soundResponse?.fileUrl
            ?: selectedSound?.fileUrl
            ?: prefUtil.selectedSoundUrl

    binding.zipperPreview.setSoundUrl(soundUrl)
}

fun LockscreenInfoFragment.loadWallpaperImageFromUrl(imageUrl: String) {
    Glide
        .with(this)
        .asBitmap()
        .load(imageUrl)
        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
        .skipMemoryCache(false)
        .into(
            object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?,
                ) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setWallpaperBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            },
        )
}

fun LockscreenInfoFragment.loadWallpaperImageFromPath(imagePath: String) {
    Glide
        .with(this)
        .asBitmap()
        .load(imagePath)
        .diskCacheStrategy(DiskCacheStrategy.DATA)
        .skipMemoryCache(false)
        .into(
            object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?,
                ) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setWallpaperBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            },
        )
}

fun LockscreenInfoFragment.loadBackgroundImageFromUrl(imageUrl: String) {
    Glide
        .with(this)
        .asBitmap()
        .load(imageUrl)
        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
        .skipMemoryCache(false)
        .into(
            object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?,
                ) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setBackgroundBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            },
        )
}

fun LockscreenInfoFragment.loadBackgroundImageFromPath(imagePath: String) {
    Glide
        .with(this)
        .asBitmap()
        .load(imagePath)
        .diskCacheStrategy(DiskCacheStrategy.DATA)
        .skipMemoryCache(false)
        .into(
            object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?,
                ) {
                    val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                    binding.zipperPreview.setBackgroundBitmap(copyBitmap)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle cleanup if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    // Handle error case
                }
            },
        )
}

fun LockscreenInfoFragment.loadAllPreviewSettings() {
    // Apply all settings to preview
    binding.zipperPreview.setVibrationEnabled(prefUtil.vibrationEnabled)
    binding.zipperPreview.setSoundEnabled(prefUtil.soundEnabled)
    binding.zipperPreview.setPrefUtil(prefUtil)

    // This method ensures all settings are properly applied to the preview
}

fun LockscreenInfoFragment.refreshPreview() {
    // Refresh all settings in preview
    binding.zipperPreview.refreshSettings()
}

fun LockscreenInfoFragment.observeSelectedFontFamily() {
    viewModel.selectedFontFamily.observe(viewLifecycleOwner) { fontFamily ->
        fontFamily?.let {
            fontFamilyAdapter.setSelectedFontFamily(it)
            updatePreviewText()
            prefUtil.selectedFontFamilyId = fontFamily.id
        }
    }
}

fun LockscreenInfoFragment.observeSelectedFontColor() {
    viewModel.selectedFontColor.observe(viewLifecycleOwner) { fontColor ->
        fontColor?.let {
            fontColorAdapter.setSelectedFontColor(it)
            updatePreviewText()
            prefUtil.selectedFontColorId = fontColor.id
        }
    }
}

fun LockscreenInfoFragment.updatePreviewText() {
    val selectedFontFamily = viewModel.selectedFontFamily.value
    val selectedFontColor = viewModel.selectedFontColor.value

    // Apply font and color to preview ZipperView
    binding.zipperPreview.applyFontAndColorSettings(
        selectedFontFamily?.fontResource,
        selectedFontColor?.colorRes,
    )
}

fun LockscreenInfoFragment.applyChanges() {
    // Check if lock screen is enabled
    if (!checkLockScreenEnabled()) {
        showEnableLockScreenDialog()
    } else {
        prefUtil.lockScreenEnabled = true
        // Save the customization settings and navigate to success screen
        navigateToSaveSuccess()
    }
}

fun LockscreenInfoFragment.checkLockScreenEnabled(): Boolean = prefUtil.lockScreenEnabled

fun LockscreenInfoFragment.showEnableLockScreenDialog() {
    val dialog = EnableLockScreenDialogFragment()

    dialog.onClickEnableButton = {
        enableLockScreen()
    }

    dialog.show(parentFragmentManager, "EnableLockScreenDialog")
}

fun LockscreenInfoFragment.enableLockScreen() {
    try {
        // Check if overlay permission is needed
        if (!Settings.canDrawOverlays(requireContext())) {
            // Navigate to system settings to enable overlay permission
            val intent =
                Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    "package:${requireContext().packageName}".toUri(),
                )
            // Show custom notification when navigating to settings
            SettingsNotificationService.showNotification(requireContext())
            doActionWhenStop {
                AdsController.isBlockOpenAds = true
            }
            overlayPermissionLauncher.launch(intent)
        } else {
            prefUtil.lockScreenEnabled = true
            navigateToSaveSuccess()
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

fun LockscreenInfoFragment.navigateToSaveSuccess() {
    // Save font and color settings to preferences before navigating
    saveFontAndColorSettings()

    // Save all customization data to PrefUtil before navigating to SaveSuccess
    commonViewModel.saveEditLayoutDataToPref()
    showLoadedInter(
        spaceNameConfig = "lockinfo-apply",
        spaceName = "edit-1ID_interstitial",
        destinationToShowAds = R.id.lockscreenInfoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safeNavInter(
                R.id.lockscreenInfoFragment,
                R.id.action_lockscreenInfoFragment_to_zipperLockSaveSuccessFragment,
            )
        },
        onCloseAds = {},
    )
}

fun LockscreenInfoFragment.loadSavedFontAndColorSettings() {
    // Load saved font family and color from preferences
    val savedFontFamilyId = prefUtil.selectedFontFamilyId
    val savedFontColorId = prefUtil.selectedFontColorId

    // Set saved font family if exists
    savedFontFamilyId?.let { fontId ->
        val savedFontFamily = FontData.fontFamilies.find { it.id == fontId }
        savedFontFamily?.let { viewModel.selectFontFamily(it) }
    }

    // Set saved font color if exists
    savedFontColorId?.let { colorId ->
        val savedFontColor = FontData.fontColors.find { it.id == colorId }
        savedFontColor?.let { viewModel.selectFontColor(it) }
    }
}

fun LockscreenInfoFragment.saveFontAndColorSettings() {
    // Save selected font family
    viewModel.selectedFontFamily.value?.let { fontFamily ->
        prefUtil.selectedFontFamilyId = fontFamily.id
    }

    // Save selected font color
    viewModel.selectedFontColor.value?.let { fontColor ->
        prefUtil.selectedFontColorId = fontColor.id
    }
}

fun LockscreenInfoFragment.startPreviewService() {
    showZipperFullScreenPreview()
}

fun LockscreenInfoFragment.showZipperFullScreenPreview() {
    showLoadedInter(
        spaceNameConfig = "lockinfo-preview",
        spaceName = "edit-1ID_interstitial",
        destinationToShowAds = R.id.lockscreenInfoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            DialogNative.runWhenNativeDismiss {
                val dialog =
                    ZipperFullScreenDialogFragment.newInstance(
                        zipperResponse = selectedZipper,
                        wallpaperResponse = selectedWallpaper,
                        localWallpaper = selectedLocalWallpaper,
                        backgroundResponse = selectedBackground,
                        localBackground = selectedLocalBackground,
                        soundResponse = selectedSound,
                        rowResponse = selectedRow,
                        fontFamilyId = viewModel.selectedFontFamily.value?.id,
                        fontColorId = viewModel.selectedFontColor.value?.id,
                    )

                dialog.show(parentFragmentManager, ZipperFullScreenDialogFragment.TAG)
            }
        },
        onCloseAds = {},
    )
}

fun LockscreenInfoFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("lockinfo-apply", "lockinfo-preview"),
        spaceNameAds = "edit-1ID_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "lockinfo-bot",
        spaceName = "lockinfo-bot_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )

    showLoadedNative(
        spaceNameConfig = "lockinfo-midd1",
        spaceName = "lockinfo-midd1_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {
            isNeedShowReloadAds = true
        },
    )
}

fun LockscreenInfoFragment.showReloadAds() {
    if (!isShowedReloadAds && isNeedShowReloadAds) {
        isShowedReloadAds = true
        isNeedShowReloadAds = false
        showLoadedNative(
            spaceNameConfig = "lockinfo-midd2",
            spaceName = "lockinfo-midd2_native",
            layoutToAttachAds = binding.adViewGroupTop,
            layoutContainAds = binding.layoutAdsTop,
            onAdsClick = {},
        )
    }
}
