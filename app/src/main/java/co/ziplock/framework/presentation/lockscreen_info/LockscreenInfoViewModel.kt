package co.ziplock.framework.presentation.lockscreen_info

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SettingsManager
import co.ziplock.framework.presentation.model.FontColor
import co.ziplock.framework.presentation.model.FontData
import co.ziplock.framework.presentation.model.FontFamily
import co.ziplock.framework.presentation.model.home.SettingsTabUiState
import co.ziplock.util.PrefUtil
import javax.inject.Inject

@HiltViewModel
class LockscreenInfoViewModel @Inject constructor(
    private val prefUtil: PrefUtil,
    private val settingsManager: SettingsManager
) : BaseViewModel() {
    
    private val _selectedZipper = MutableLiveData<ZipperResponse?>()
    val selectedZipper: LiveData<ZipperResponse?> = _selectedZipper
    
    private val _selectedFontFamily = MutableLiveData<FontFamily>()
    val selectedFontFamily: LiveData<FontFamily> = _selectedFontFamily
    
    private val _selectedFontColor = MutableLiveData<FontColor>()
    val selectedFontColor: LiveData<FontColor> = _selectedFontColor
    
    private val _fontFamilies = MutableLiveData<List<FontFamily>>()
    val fontFamilies: LiveData<List<FontFamily>> = _fontFamilies
    
    private val _fontColors = MutableLiveData<List<FontColor>>()
    val fontColors: LiveData<List<FontColor>> = _fontColors
    
    // Settings UI State
    private val _settingsTabUiState = MutableStateFlow(SettingsTabUiState())
    val settingsTabUiState: StateFlow<SettingsTabUiState> = _settingsTabUiState.asStateFlow()
    
    init {
        loadFontData()
        setDefaultSelections()
        loadSettings()
        observeSettingsChanges()
    }
    
    private fun observeSettingsChanges() {
        settingsManager.settingsChanged
            .onEach { event ->
                // Refresh settings when changes are notified from other ViewModels
                refreshSettings()
            }
            .launchIn(viewModelScope)
    }
    
    private fun loadSettings() {
        // Load saved preferences
        val soundEnabled = prefUtil.soundEnabled
        val vibrationEnabled = prefUtil.vibrationEnabled
        val dateTimeEnabled = prefUtil.dateTimeEnabled
        val batteryWidgetEnabled = prefUtil.batteryWidgetEnabled

        // Update UI State
        _settingsTabUiState.update { currentState ->
            currentState.copy(
                soundEnabled = soundEnabled,
                vibrationEnabled = vibrationEnabled,
                dateTimeEnabled = dateTimeEnabled,
                batteryWidgetEnabled = batteryWidgetEnabled,
            )
        }
    }
    
    fun setSelectedZipper(zipper: ZipperResponse?) {
        _selectedZipper.value = zipper
    }
    
    fun selectFontFamily(fontFamily: FontFamily) {
        _selectedFontFamily.value = fontFamily
    }
    
    fun selectFontColor(fontColor: FontColor) {
        _selectedFontColor.value = fontColor
    }
    
    private fun loadFontData() {
        _fontFamilies.value = FontData.fontFamilies
        _fontColors.value = FontData.fontColors
    }
    
    private fun setDefaultSelections() {
        // Load saved font family from preferences or use default
        val savedFontFamilyId = prefUtil.selectedFontFamilyId ?: "itim"
        _selectedFontFamily.value = FontData.fontFamilies.find { it.id == savedFontFamilyId }
            ?: FontData.fontFamilies.find { it.isSelected }
            ?: FontData.fontFamilies.firstOrNull()
        
        // Load saved font color from preferences or use default
        val savedFontColorId = prefUtil.selectedFontColorId ?: "white"
        _selectedFontColor.value = FontData.fontColors.find { it.id == savedFontColorId }
            ?: FontData.fontColors.find { it.id == "white" } 
            ?: FontData.fontColors.firstOrNull()
    }

    fun refreshSettings() {
        loadSettings()
    }
}