package co.ziplock.framework.presentation.lockscreen_info.adapter

import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import co.ziplock.R
import co.ziplock.databinding.ItemFontColorBinding
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.FontColor
import co.ziplock.util.setPreventDoubleClick

class FontColorAdapter(
    private val onFontColorClick: (FontColor) -> Unit
) : BaseListAdapter<FontColor, ItemFontColorBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
    )
) {
    
    private var selectedFontColor: FontColor? = null

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_font_color

    override fun bindView(binding: ItemFontColorBinding, item: FontColor, position: Int) {
        // Set color background
        val color = ContextCompat.getColor(binding.root.context, item.colorRes)
        binding.vColorBackground.backgroundTintList = 
            binding.root.context.getColorStateList(item.colorRes)
        
        // Show/hide selection indicator
        val isSelected = item == selectedFontColor
        binding.ivSelected.isVisible = isSelected
        
        // Set click listener
        binding.root.setPreventDoubleClick {
            onFontColorClick(item)
        }
    }
    
    fun setSelectedFontColor(fontColor: FontColor) {
        val oldSelected = selectedFontColor
        selectedFontColor = fontColor
        
        // Notify changes for old and new selected items
        oldSelected?.let { old ->
            currentList.indexOf(old).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
        currentList.indexOf(fontColor).takeIf { it >= 0 }?.let { index ->
            notifyItemChanged(index)
        }
    }
}