package co.ziplock.framework.presentation.lockscreen_info.adapter

import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import co.ziplock.R
import co.ziplock.databinding.ItemFontFamilyBinding
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.FontFamily
import co.ziplock.util.setPreventDoubleClick

class FontFamilyAdapter(
    private val onFontFamilyClick: (FontFamily) -> Unit
) : BaseListAdapter<FontFamily, ItemFontFamilyBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
    )
) {
    
    private var selectedFontFamily: FontFamily? = null

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_font_family

    override fun bindView(binding: ItemFontFamilyBinding, item: FontFamily, position: Int) {
        // Set font sample text with appropriate font family
        binding.tvFontSample.apply {
            text = "Aa"
            typeface = ResourcesCompat.getFont(context, item.fontResource)
        }
        
        // Show/hide selection indicator
        val isSelected = item == selectedFontFamily
        binding.ivSelected.isVisible = isSelected
        
        // Update background for selected state
        if (isSelected) {
            binding.flBackground.backgroundTintList = binding.root.context.getColorStateList(R.color.blue)
            binding.tvFontSample.setTextColor(binding.root.context.getColor(R.color.white))
        } else {
            binding.flBackground.backgroundTintList = binding.root.context.getColorStateList(R.color.light_gray)
            binding.tvFontSample.setTextColor(binding.root.context.getColor(R.color.black_35496d))
        }
        
        // Set click listener
        binding.root.setPreventDoubleClick {
            onFontFamilyClick(item)
        }
    }
    
    fun setSelectedFontFamily(fontFamily: FontFamily) {
        val oldSelected = selectedFontFamily
        selectedFontFamily = fontFamily
        
        // Notify changes for old and new selected items
        oldSelected?.let { old ->
            currentList.indexOf(old).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
        currentList.indexOf(fontFamily).takeIf { it >= 0 }?.let { index ->
            notifyItemChanged(index)
        }
    }
}