package co.ziplock.framework.presentation.manager

import android.Manifest
import android.content.Context
import android.os.Build
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.permissionx.guolindev.PermissionX
import co.ziplock.R
import co.ziplock.framework.presentation.common.doActionWhenStop
import kotlin.collections.all

class PermissionManager(
    private val context: Context
) {
    fun getDefaultListPermission(): List<String> = buildList {
        // Camera permission is required for all Android versions
        add(Manifest.permission.ACCESS_FINE_LOCATION)
        add(Manifest.permission.ACCESS_COARSE_LOCATION)

        add(Manifest.permission.CAMERA)
        val mediaPermission = getMediaPermission()
        addAll(mediaPermission)
    }

    fun getNotificationPermission(): List<String> {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                listOf(Manifest.permission.POST_NOTIFICATIONS)
            }
            else -> emptyList()
        }
    }

    fun isNotificationPermissionGranted(): Boolean {
        return getNotificationPermission().all { permission ->
            PermissionX.isGranted(context, permission)
        }
    }

    fun getMediaPermission(): List<String> {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                listOf(
                    Manifest.permission.READ_MEDIA_IMAGES
                )
            }

            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                listOf(Manifest.permission.READ_EXTERNAL_STORAGE)
            }

            else -> {
                listOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        }
    }

    fun isMediaPermissionGranted(): Boolean {
        return getMediaPermission().all { permission ->
            PermissionX.isGranted(context, permission)
        }
    }

    fun isAllPermissionGranted(): Boolean {
        return getDefaultListPermission().all { permission ->
            PermissionX.isGranted(context, permission)
        }
    }

    fun requestPermission(
        fragment: Fragment? = null,
        permission: List<String> = getDefaultListPermission(),
        activity: AppCompatActivity? = null,
        onAllPermissionGranted: () -> Unit = {},
        onPermissionDenied: () -> Unit = {}
    ) {
        val permissionMediator = when {
            fragment != null -> PermissionX.init(fragment)
            activity != null -> PermissionX.init(activity)
            else -> return@requestPermission
        }
        
        permissionMediator.permissions(permission)
            .onExplainRequestReason { scope, deniedList ->
                scope.showRequestReasonDialog(
                    deniedList,
                    context.getString(R.string.permission_explanation),
                    context.getString(R.string.ok),
                    context.getString(R.string.cancel),
                    onForwardToSetting = {
                        fragment?.doActionWhenStop {
                            //AdsController.isBlockOpenAds = true
                        }
                    }
                )
            }
            .onForwardToSettings { scope, deniedList ->
                scope.showForwardToSettingsDialog(
                    deniedList,
                    context.getString(R.string.permission_settings_message),
                    context.getString(R.string.ok),
                    context.getString(R.string.cancel),
                    onForwardToSetting = {
                        fragment?.doActionWhenStop {
                            //AdsController.isBlockOpenAds = true
                        }
                    }
                )
            }
            .request { allGranted, _, deniedList ->
                if (allGranted) {
                    onAllPermissionGranted()
                } else {
                    onPermissionDenied()
                    Toast.makeText(
                        context,
                        context.getString(R.string.permission_denied, deniedList),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
    }
}