package co.ziplock.framework.presentation.manager

import android.content.Context
import android.os.Handler
import android.os.Looper
import dagger.hilt.android.qualifiers.ApplicationContext
import co.ziplock.R
import co.ziplock.util.PrefUtil
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SecurityManager @Inject constructor(
    private val prefUtil: PrefUtil,
) {
    companion object {
        const val MAX_ATTEMPTS = 5
        const val BASE_LOCKOUT_DURATION = 30 * 1000L // 30 seconds in milliseconds
        const val LOCKOUT_INCREMENT = 30 * 1000L // Additional 30 seconds for each 5 failed attempts
    }
    
    enum class SecurityType {
        NONE, PIN, PATTERN
    }

    private val handler = Handler(Looper.getMainLooper())
    private var lockoutRunnable: Runnable? = null
    
    fun getSecurityType(): SecurityType {
        val typeString = prefUtil.securityType
        return try {
            SecurityType.valueOf(typeString)
        } catch (e: IllegalArgumentException) {
            SecurityType.NONE
        }
    }
    
    fun setSecurityType(type: SecurityType) {
        prefUtil.securityType = type.name
        prefUtil.hasSecurity = type != SecurityType.NONE
    }
    
    fun hasSecurity(): Boolean {
        return prefUtil.hasSecurity
    }
    
    fun setPinCode(pinCode: String) {
        prefUtil.pinCode = pinCode
    }
    
    fun getPinCode(): String? {
        return prefUtil.pinCode
    }
    
    fun setPatternCode(pattern: List<Int>) {
        val patternString = pattern.joinToString(",")
        prefUtil.patternCode = patternString
    }
    
    fun getPatternCode(): List<Int>? {
        val patternString = prefUtil.patternCode
        return if (patternString.isNullOrEmpty()) {
            null
        } else {
            try {
                patternString.split(",").map { it.toInt() }
            } catch (e: NumberFormatException) {
                null
            }
        }
    }
    
    fun verifyPin(inputPin: String): Boolean {
        val savedPin = getPinCode()
        val isCorrect = savedPin != null && savedPin == inputPin
        
        if (isCorrect) {
            resetFailedAttempts()
            return true
        } else {
            incrementFailedAttempts()
            return false
        }
    }
    
    fun verifyPattern(inputPattern: List<Int>): Boolean {
        val savedPattern = getPatternCode()
        val isCorrect = savedPattern != null && savedPattern == inputPattern
        
        if (isCorrect) {
            resetFailedAttempts()
            return true
        } else {
            incrementFailedAttempts()
            return false
        }
    }
    
    private fun incrementFailedAttempts() {
        val currentAttempts = getFailedAttempts() + 1
        prefUtil.failedAttempts = currentAttempts
        
        if (currentAttempts >= MAX_ATTEMPTS) {
            // Increment lockout count and calculate new lockout duration
            val lockoutCount = (prefUtil.failedAttempts / MAX_ATTEMPTS)

            // Calculate lockout duration based on lockout count
            // First lockout (count=1): 30s, Second lockout (count=2): 60s, etc.
            val lockoutDuration = BASE_LOCKOUT_DURATION + (lockoutCount - 1) * LOCKOUT_INCREMENT
            
            // Set lockout time to current time + calculated duration
            setLockoutTime(System.currentTimeMillis() + lockoutDuration)
        }
    }
    
    fun getFailedAttempts(): Int {
        return prefUtil.failedAttempts
    }
    
    fun resetFailedAttempts() {
        prefUtil.failedAttempts = 0
        setLockoutTime(0)
        lockoutRunnable?.let { handler.removeCallbacks(it) }
        lockoutRunnable = null
    }

    fun resetFailedAttemptsWhenOpenApp() {
        prefUtil.failedAttempts = 0
    }

    fun setLockoutTime(timeInMillis: Long) {
        prefUtil.lockoutTime = timeInMillis
    }
    
    fun getLockoutTime(): Long {
        return prefUtil.lockoutTime
    }
    
    fun isLockedOut(): Boolean {
        val lockoutTime = getLockoutTime()
        return lockoutTime > System.currentTimeMillis()
    }
    
    fun getRemainingLockoutTime(): Long {
        val lockoutTime = getLockoutTime()
        val currentTime = System.currentTimeMillis()
        return if (lockoutTime > currentTime) {
            lockoutTime - currentTime
        } else {
            0
        }
    }
    
    fun getRemainingLockoutTimeSeconds(): Int {
        val remainingMs = getRemainingLockoutTime()
        return (remainingMs / 1000).toInt()
    }
    
    fun setSecurityQuestion(question: String) {
        prefUtil.securityQuestion = question
    }
    
    fun getSecurityQuestion(): String? {
        return prefUtil.securityQuestion
    }
    
    fun setSecurityQuestionKey(questionKey: String) {
        prefUtil.securityQuestionKey = questionKey
    }
    
    fun getSecurityQuestionKey(): String? {
        return prefUtil.securityQuestionKey
    }
    
    fun setSecurityAnswer(answer: String) {
        prefUtil.securityAnswer = answer.lowercase()
    }
    
    fun getSecurityAnswer(): String? {
        return prefUtil.securityAnswer
    }
    
    fun verifySecurityAnswer(answer: String): Boolean {
        val savedAnswer = getSecurityAnswer()
        return savedAnswer != null && savedAnswer == answer.lowercase()
    }
    
    fun clearSecurity() {
        prefUtil.pinCode = null
        prefUtil.patternCode = null
        prefUtil.securityType = SecurityType.NONE.name
        prefUtil.hasSecurity = false
        prefUtil.failedAttempts = 0
        prefUtil.lockoutTime = 0
        prefUtil.passwordEnabled = false
    }
    
    /**
     * Setup new PIN code - clears all existing security and sets new PIN
     */
    fun setupPinCode(pinCode: String) {
        // Clear all existing security first
        clearSecurity()
        
        // Set new PIN security
        setPinCode(pinCode)
        setSecurityType(SecurityType.PIN)
        prefUtil.passwordEnabled = true
        prefUtil.hasSecurity = true
        resetFailedAttempts()
    }
    
    /**
     * Setup new Pattern code - clears all existing security and sets new Pattern
     */
    fun setupPatternCode(pattern: List<Int>) {
        // Clear all existing security first
        clearSecurity()
        
        // Set new Pattern security
        setPatternCode(pattern)
        setSecurityType(SecurityType.PATTERN)
        prefUtil.passwordEnabled = true
        prefUtil.hasSecurity = true
        resetFailedAttempts()
    }
    
    fun hasSecurityQuestion(): Boolean {
        return !getSecurityQuestionKey().isNullOrEmpty() && !getSecurityAnswer().isNullOrEmpty()
    }
    
    /**
     * Get security question text from stored key
     */
    fun getSecurityQuestionText(context: Context): String? {
        val questionKey = getSecurityQuestionKey() ?: return null
        
        val questionKeys = context.resources.getStringArray(R.array.security_question_keys)
        val questionTexts = context.resources.getStringArray(R.array.security_questions)
        
        val index = questionKeys.indexOf(questionKey)
        return if (index >= 0 && index < questionTexts.size) {
            questionTexts[index]
        } else {
            null
        }
    }
    
    // Recovery Email Methods
    fun setRecoveryEmail(email: String) {
        prefUtil.recoveryEmail = email
    }
    
    fun getRecoveryEmail(): String? {
        return prefUtil.recoveryEmail
    }
    
    fun hasRecoveryEmail(): Boolean {
        return !getRecoveryEmail().isNullOrEmpty()
    }
}