package co.ziplock.framework.presentation.manager

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsManager @Inject constructor() {
    
    private val _settingsChanged = MutableSharedFlow<SettingsChangeEvent>()
    val settingsChanged: SharedFlow<SettingsChangeEvent> = _settingsChanged.asSharedFlow()
    
    suspend fun notifySettingsChanged(event: SettingsChangeEvent) {
        _settingsChanged.emit(event)
    }
}

sealed class SettingsChangeEvent {
    object SoundChanged : SettingsChangeEvent()
    object VibrationChanged : SettingsChangeEvent()
    object DateTimeChanged : SettingsChangeEvent()
    object BatteryWidgetChanged : SettingsChangeEvent()
    object AllSettingsChanged : SettingsChangeEvent()
}