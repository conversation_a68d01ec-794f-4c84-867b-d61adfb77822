package co.ziplock.framework.presentation.model

import android.graphics.Typeface
import co.ziplock.R

data class FontFamily(
    val id: String,
    val name: String,
    val fontResource: Int,
    val isSelected: Boolean = false
)

data class FontColor(
    val id: String,
    val name: String,
    val colorRes: Int
)

object FontData {
    val fontFamilies = listOf(
        FontFamily("itim", "itim", R.font.font_itim, true),
        FontFamily("allison", "allison", R.font.allison),
        FontFamily("atma", "atma", R.font.atma),
        FontFamily("charmonman", "charmonman", R.font.charmonman),
        FontFamily("cookie", "cookie", R.font.cookie),
        <PERSON>ontFamily("caveat_brush", "caveat brush", R.font.caveat_brush),
        <PERSON>ontFamily("damion", "damion", R.font.damion),
        <PERSON>ontFamily("dyna_puff", "dyna puff", R.font.dyna_puff),
        <PERSON>ont<PERSON>amily("englebert", "englebert", <PERSON>.font.englebert),
        <PERSON><PERSON><PERSON><PERSON><PERSON>("frijole", "frijole", R.font.frijole),
        FontFamily("give_you_glory", "give you glory", R.font.give_you_glory),
        FontFamily("henny_penny", "henny penny", R.font.henny_penny),
        FontFamily("indie_flower", "indie flower", R.font.indie_flower),
        FontFamily("kablammo", "kablammo", R.font.kablammo),
        FontFamily("kurale", "kurale", R.font.kurale),
        FontFamily("lacquer", "lacquer", R.font.lacquer),
        FontFamily("love_ya_like_a_sister", "love ya like a sister", R.font.love_ya_like_a_sister),
        FontFamily("meow_script", "meow script", R.font.meow_script),
        FontFamily("moon_dance", "moon dance", R.font.moon_dance),
        FontFamily("mr_dafoe", "mr dafoe", R.font.mr_dafoe),
        FontFamily("oi", "oi", R.font.oi),
        FontFamily("passions_conflict", "passions conflict", R.font.passions_conflict),
        FontFamily("playball", "playball", R.font.playball),
        FontFamily("poiret_one", "poiret one", R.font.poiret_one),
        FontFamily("rubik_bubbles", "rubik bubbles", R.font.rubik_bubbles),
        FontFamily("rubik_dirt", "rubik dirt", R.font.rubik_dirt),
        FontFamily("rubik_glitch", "rubik glitch", R.font.rubik_glitch),
        FontFamily("rubik_puddles", "rubik puddles", R.font.rubik_puddles),
        FontFamily("seaweed_script", "seaweed script", R.font.seaweed_script),
        FontFamily("special_elite", "special elite", R.font.special_elite)
    )
    
    val fontColors = listOf(
        FontColor("white", "White", R.color.white),
        FontColor("black", "Black", R.color.black),
        FontColor("yellow", "Yellow", android.R.color.holo_orange_light),
        FontColor("orange", "Orange", R.color.orange),
        FontColor("green", "Green", R.color.green_4caf50),
        FontColor("soft_peach", "soft peach", R.color.soft_peach_ffddd2),
        FontColor("bright_red", "bright red", R.color.bright_red_ef233c),
        FontColor("sky_blue", "sky blue", R.color.sky_blue_00bbf9),
        FontColor("deep_teal", "deep teal", R.color.deep_teal_1282a2),
        FontColor("light_rose", "light rose", R.color.light_rose_ff9595),
    )
}