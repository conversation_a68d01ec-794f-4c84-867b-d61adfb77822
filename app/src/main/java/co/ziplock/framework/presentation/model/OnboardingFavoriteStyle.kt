package co.ziplock.framework.presentation.model

import android.os.Parcelable
import androidx.annotation.DrawableRes
import kotlinx.parcelize.Parcelize
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel

@Parcelize
data class OnboardingFavoriteStyle(
    @SerializedName("name")
    val name: String?,
    @SerializedName("thumbnail")
    val thumbnailUrl: String?,
    @SerializedName("is_show")
    val isShow: Boolean = true,
    @SerializedName("id_themes")
    val idThemes: String? = null,

    @DrawableRes
    val defaultDrawable: Int? = null
) : Parcelable
