package co.ziplock.framework.presentation.model

data class ThemeData(
    val id: Int,
    val name: String,
    val previewImageRes: Int,
    val isHotTrending: Boolean = false
)

object ThemeProvider {
    
    private val allThemes = listOf(
        ThemeData(
            id = 1,
            name = "Neon Glow",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 2,
            name = "Galaxy",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 3,
            name = "Cyberpunk",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 4,
            name = "Ocean Wave",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 5,
            name = "Fire Storm",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 6,
            name = "Northern Lights",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 7,
            name = "Crystal Ice",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 8,
            name = "Golden Sunset",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = true
        ),
        ThemeData(
            id = 9,
            name = "Classic",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = false
        ),
        ThemeData(
            id = 10,
            name = "Modern",
            previewImageRes = co.ziplock.R.drawable.img_zipper_preview,
            isHotTrending = false
        )
    )
    
    fun getTop3Themes(): List<ThemeData> {
        return getRandomHotTrendingThemes(3)
    }
    
    fun getRandomHotTrendingThemes(count: Int = 3): List<ThemeData> {
        val hotTrendingThemes = allThemes.filter { it.isHotTrending }
        return hotTrendingThemes.shuffled().take(count)
    }
    
    fun getAllThemes(): List<ThemeData> {
        return allThemes
    }
    
    fun getHotTrendingThemes(): List<ThemeData> {
        return allThemes.filter { it.isHotTrending }
    }
}