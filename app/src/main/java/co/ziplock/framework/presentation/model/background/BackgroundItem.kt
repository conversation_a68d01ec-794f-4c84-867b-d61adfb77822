package co.ziplock.framework.presentation.model.background

import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData

sealed class BackgroundItem {
    data class RemoteBackground(
        val background: BackgroundResponse,
    ) : BackgroundItem()

    data class LocalBackgroundItem(
        val background: LocalImageData,
    ) : BackgroundItem()

    data class AdsBackgroundItem(
        val adsItem: AdsItem,
    ) : BackgroundItem()
}
