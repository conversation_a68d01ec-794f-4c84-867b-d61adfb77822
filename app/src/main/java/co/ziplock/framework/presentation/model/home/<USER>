package co.ziplock.framework.presentation.model.home

import android.os.Parcelable
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.model.AdsItem
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotTrendingThemeModel(
    val id: String? = null,
    val previewImageUrl: String? = null,
    // Các model đầy đủ thay vì chỉ là ID
    val row: RowResponse? = null,
    val sound: SoundResponse? = null,
    val background: BackgroundResponse? = null,
    val wallpaper: WallpaperResponse? = null,
    val zipper: ZipperResponse? = null,
    val adsItem: AdsItem? = null,
    val isPro: Boolean = false,
    val category: String? = null,
) : Parcelable
