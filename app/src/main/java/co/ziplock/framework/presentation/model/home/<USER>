package co.ziplock.framework.presentation.model.home

import android.os.Parcelable
import co.ziplock.framework.presentation.model.theme.ThemeCategoryModel
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.IgnoredOnParcel

@Parcelize
data class HotTrendingUiState(
    val isLoading: Boolean = false,
    val hotTrendingThemes: List<HotTrendingThemeModel> = emptyList(),
    val categories: List<ThemeCategoryModel> = emptyList(),
    val selectedCategory: ThemeCategoryModel? = null,
    val filteredThemes: List<HotTrendingThemeModel> = emptyList(),
    val currentPage: Int = 0,
    val itemsPerPage: Int = 12,
    val hasMoreItems: Boolean = false,
    val isLoadingMore: Boolean = false,
    @IgnoredOnParcel
    val error: Throwable? = null
) : Parcelable