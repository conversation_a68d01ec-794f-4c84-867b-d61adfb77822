package co.ziplock.framework.presentation.model.pickphoto

import android.net.Uri
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class PickPhotoUiState(
    val devicePhotos: List<DevicePhoto> = emptyList(),
    val selectedPhotos: List<DevicePhoto> = emptyList(),
    val requiredPhotoCount: Int = 0,
    val isLoading: Boolean = false,
    val isEnableGoToNextScreen: Boolean = false,
    val photoSourceMode: PhotoSourceMode = PhotoSourceMode.ALL_PHOTOS,
    val availableAlbums: List<PhotoAlbum> = emptyList(),
    val selectedAlbum: PhotoAlbum? = null,
    val albumPhotos: List<DevicePhoto> = emptyList()
)

enum class PhotoSourceMode {
    ALL_PHOTOS,
    ALBUM
}

@Parcelize
data class DevicePhoto(
    val id: Long,
    val uri: Uri,
    val path: String,
) : Parcelable

@Parcelize
data class PhotoAlbum(
    val id: Long,
    val name: String,
    val coverPhotoUri: Uri? = null,
    val photoCount: Int = 0
) : Parcelable
