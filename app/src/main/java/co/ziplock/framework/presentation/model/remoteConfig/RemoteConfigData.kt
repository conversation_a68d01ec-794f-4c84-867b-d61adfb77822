package co.ziplock.framework.presentation.model.remoteConfig

import android.os.Parcelable
import co.ziplock.BuildConfig
import kotlinx.parcelize.Parcelize

@Parcelize
data class RemoteConfigData(
    val configShowAds: String,
    val admobId: String,
    val isRealData: Boolean,
    val baseUrlOtp: String = BuildConfig.BASE_URL_OTP,
    val showOnboarding1Fragment: Boolean = false,
    val showOnboarding2Fragment: Boolean = false,
    val showOnboarding3Fragment: Boolean = false,
    val showOnboarding4Fragment: Boolean = false,
    val numberOfContentBetweenThemeList: Int = 2,
    val numberOfContentBetweenZipList: Int = 2,
    val numberOfContentBetweenRowList: Int = 2,
    val numberOfContentBetweenBackgroundList: Int = 2,
    val numberOfContentBetweenWallpaperList: Int = 2,
    val numberOfContentBetweenSoundList: Int = 4,
) : Parcelable
