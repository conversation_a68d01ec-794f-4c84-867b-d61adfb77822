package co.ziplock.framework.presentation.model.row

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import co.ziplock.framework.network.model.RowResponse
import kotlinx.parcelize.IgnoredOnParcel

@Parcelize
data class RowUiState(
    val isLoading: Boolean = false,
    val allRows: List<RowResponse> = emptyList(),
    val selectedRow: RowResponse? = null,
    @IgnoredOnParcel
    val error: Throwable? = null
) : Parcelable