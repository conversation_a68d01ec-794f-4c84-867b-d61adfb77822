package co.ziplock.framework.presentation.model.sound

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import co.ziplock.framework.network.model.SoundResponse
import kotlinx.parcelize.IgnoredOnParcel

@Parcelize
data class SoundUiState(
    val isLoading: Boolean = false,
    val categories: List<String> = emptyList(),
    val selectedCategory: String? = null,
    val soundCategories: Map<String, List<SoundResponse>> = emptyMap(),
    val allSounds: List<SoundResponse> = emptyList(),
    val currentCategorySounds: List<SoundResponse> = emptyList(),
    @IgnoredOnParcel
    val error: Throwable? = null
) : Parcelable