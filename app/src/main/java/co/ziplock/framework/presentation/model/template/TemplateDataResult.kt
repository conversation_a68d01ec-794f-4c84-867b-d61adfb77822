package co.ziplock.framework.presentation.model.template

import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.HotTrendingThemeResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.model.OnboardingFavoriteStyle

data class TemplateDataResult(
    val categoryIdMap: Map<String, String>,
    val baseTemplateData: BaseTemplateData,
    val hotTrendingThemes: List<HotTrendingThemeResponse>,
    val categoryTemplateData: CategoryTemplateData
)

data class BaseTemplateData(
    val rowMap: Map<String, RowResponse>,
    val soundMap: Map<String, SoundResponse>,
    val backgroundMap: Map<String, BackgroundResponse>,
    val wallpaperMap: Map<String, WallpaperResponse>,
    val zipperMap: Map<String, ZipperResponse>,
    val onboardingStyles: List<OnboardingFavoriteStyle>
)

data class CategoryTemplateData(
    val soundCategories: Map<String, List<SoundResponse>>,
    val backgroundCategories: Map<String, List<BackgroundResponse>>,
    val wallpaperCategories: Map<String, List<WallpaperResponse>>,
    val zipperCategories: Map<String, List<ZipperResponse>>,
)