package co.ziplock.framework.presentation.model.template

import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel

/**
 * Extension functions for TemplateDataResult to provide easy access to mapped data
 */

// Chuyển đổi hot trending themes thành HotTrendingThemeModel
fun TemplateDataResult.toHotTrendingThemeModels(): List<HotTrendingThemeModel> {
    return hotTrendingThemes.map { theme ->
        // Map category ID to category name if available
        val categoryName = theme.idCategory?.let { categoryId ->
            // For now, we'll use a simple mapping. In a real app, you'd have a proper category lookup
            when (categoryId) {
                "1" -> "Nature"
                "2" -> "Abstract"
                "3" -> "Minimal"
                "4" -> "Dark"
                "5" -> "Colorful"
                "6" -> "Gradient"
                "7" -> "Pattern"
                else -> "All"
            }
        }

        HotTrendingThemeModel(
            id = theme.id,
            previewImageUrl = theme.previewThumbnail,
            row = theme.idRow?.let { baseTemplateData.rowMap[it] },
            sound = theme.idSound?.let { baseTemplateData.soundMap[it] },
            background = theme.idBackground?.let { baseTemplateData.backgroundMap[it] },
            wallpaper = theme.idWallpaper?.let { baseTemplateData.wallpaperMap[it] },
            zipper = theme.idZipper?.let { baseTemplateData.zipperMap[it] },
            isPro = theme.isPro ?: false,
            category = categoryName
        )
    }
}

// Lấy tất cả rows từ baseTemplateData
fun TemplateDataResult.getAllRows(): List<RowResponse> {
    return baseTemplateData.rowMap.values.toList()
}