package co.ziplock.framework.presentation.model.theme

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class ThemeCategoryModel(
    val id: String,
    val name: String,
    val displayName: String
) : Parcelable {
    companion object {
        fun getAllCategories() = "all"
        
        fun getDefaultCategories() = listOf(
            ThemeCategoryModel("all", "All", "All Categories"),
            ThemeCategoryModel("nature", "Nature", "Nature"),
            ThemeCategoryModel("abstract", "Abstract", "Abstract"),
            ThemeCategoryModel("minimal", "Minimal", "Minimal"),
            ThemeCategoryModel("dark", "Dark", "Dark"),
            ThemeCategoryModel("colorful", "Colorful", "Colorful"),
            ThemeCategoryModel("gradient", "Gradient", "Gradient"),
            ThemeCategoryModel("pattern", "Pattern", "Pattern")
        )
    }
}