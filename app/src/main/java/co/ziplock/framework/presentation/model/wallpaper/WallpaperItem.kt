package co.ziplock.framework.presentation.model.wallpaper

import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.presentation.model.AdsItem

sealed class WallpaperItem {
    data class RemoteWallpaper(
        val wallpaper: WallpaperResponse,
    ) : WallpaperItem()

    data class LocalWallpaperItem(
        val wallpaper: LocalImageData,
    ) : WallpaperItem()

    data class AdsWallpaperItem(
        val adsItem: AdsItem,
    ) : WallpaperItem()
}
