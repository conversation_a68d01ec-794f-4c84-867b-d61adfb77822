package co.ziplock.framework.presentation.onboarding

import android.view.View
import co.ziplock.databinding.FragmentOnboardingBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.onboarding.adapter.OnboardingViewPagerAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnboardingFragment :
    BaseFragment<FragmentOnboardingBinding, OnboardingViewModel>(
        FragmentOnboardingBinding::inflate,
        OnboardingViewModel::class.java,
    ) {
    lateinit var onboardingAdapter: OnboardingViewPagerAdapter

    override fun init(view: View) {
        setupViewPager()
    }

    override fun subscribeObserver(view: View) {
        observeOnboardingUiEvent()
    }

    companion object {
        const val TAG = "OnboardingFragment"
    }
}
