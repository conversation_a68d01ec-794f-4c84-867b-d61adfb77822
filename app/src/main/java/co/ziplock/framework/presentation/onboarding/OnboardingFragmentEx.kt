package co.ziplock.framework.presentation.onboarding

import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import co.ziplock.R
import co.ziplock.framework.presentation.common.OnboardUiEvent
import co.ziplock.framework.presentation.onboarding.adapter.OnboardingViewPagerAdapter
import timber.log.Timber

fun OnboardingFragment.setupViewPager() {
    onboardingAdapter = OnboardingViewPagerAdapter(fragment = this)
    binding.viewPager.apply {
        adapter = onboardingAdapter
        orientation = ViewPager2.ORIENTATION_HORIZONTAL
    }
}

fun OnboardingFragment.backEvent() {
    Timber.tag(OnboardingFragment.TAG).d("backEvent: ")
    val currentItem = binding.viewPager.currentItem
    if (currentItem > 0) {
        // If not on first page, go to previous page
        binding.viewPager.currentItem = currentItem - 1
        Timber.tag(OnboardingFragment.TAG).d("backEvent: go to previous screen")
    } else {
        // If on first page, prevent back press
        Timber.tag(OnboardingFragment.TAG).d("backEvent: cannot go to previous screen")
    }
}

fun OnboardingFragment.onPreviousEvent() {
    backEvent()
}

fun OnboardingFragment.observeOnboardingUiEvent() {
    commonViewModel.onboardingUiEvent
        .onEach { event ->
            when (event) {
                is OnboardUiEvent.GoToPreviousScreen -> onPreviousEvent()
                is OnboardUiEvent.GoToNextScreen -> onNextEvent()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun OnboardingFragment.onNextEvent() {
    val currentItem = binding.viewPager.currentItem
    val lastIndex = onboardingAdapter.itemCount - 1
    if (currentItem == lastIndex) {
        navigateToHome()
    } else {
        binding.viewPager.currentItem = currentItem + 1
    }
}

fun OnboardingFragment.navigateToHome() {
    safeNav(
        R.id.onboardingFragment,
        R.id.action_onboardingFragment_to_homeFragment
    )
}

