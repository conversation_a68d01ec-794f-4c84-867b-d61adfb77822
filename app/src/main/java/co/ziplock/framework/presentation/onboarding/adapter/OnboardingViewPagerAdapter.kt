package co.ziplock.framework.presentation.onboarding.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import co.ziplock.framework.presentation.iap.IapFragment
import co.ziplock.framework.presentation.model.remoteConfig.RemoteConfigData
import co.ziplock.framework.presentation.onboarding.viewpager.onboard1.OnboardingItem1Fragment
import co.ziplock.framework.presentation.onboarding.viewpager.onboard2.OnboardingItem2Fragment
import co.ziplock.framework.presentation.onboarding.viewpager.onboard3.OnboardingItem3Fragment
import co.ziplock.framework.presentation.onboarding.viewpager.onboard4.OnboardingItem4Fragment
import co.ziplock.framework.presentation.onboarding.viewpager.onboardNativeFull1.OnboardNativeFull1Fragment
import co.ziplock.framework.presentation.onboarding.viewpager.onboardNativeFull2.OnboardNativeFull2Fragment
import co.ziplock.util.Constant
import pion.datlt.libads.utils.AdsConstant

class OnboardingViewPagerAdapter(
    fragment: Fragment,
) : FragmentStateAdapter(fragment) {
    private val fragments =
        buildList {
            if (Constant.isShowOnboard1) {
                add(OnboardingItem1Fragment.newInstance(isShowOnboarding4Fragment = Constant.isShowOnboard4))
            }
            if (Constant.isShowOnboard2) {
                add(OnboardingItem2Fragment.newInstance(isShowOnboarding4Fragment = Constant.isShowOnboard4))
            }
            if (AdsConstant.listConfigAds["onboardfull1.1"]?.isOn == true) {
                add(OnboardNativeFull1Fragment())
            }
            if (Constant.isShowOnboard3) {
                add(OnboardingItem3Fragment.newInstance(isShowOnboarding4Fragment = Constant.isShowOnboard4))
            }
            if (AdsConstant.listConfigAds["onboardfull2.1"]?.isOn == true) {
                add(OnboardNativeFull2Fragment())
            }
            if (Constant.isShowOnboard4) {
                add(OnboardingItem4Fragment())
            }
            add(IapFragment.newInstance(true))
        }

    override fun getItemCount(): Int = fragments.size

    override fun createFragment(position: Int): Fragment = fragments[position]
}
