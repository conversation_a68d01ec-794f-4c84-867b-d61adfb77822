package co.ziplock.framework.presentation.onboarding.viewpager.onboard1

import android.os.Bundle
import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentOnboardingItem1NewBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.util.BundleKey

@AndroidEntryPoint
class OnboardingItem1Fragment :
    BaseFragment<FragmentOnboardingItem1NewBinding, OnboardingItem1ViewModel>(
        FragmentOnboardingItem1NewBinding::inflate,
        OnboardingItem1ViewModel::class.java,
    ) {
    var isClickAds = false
    var isShowReloadAds = false

    val isShowOnboarding4Fragment by lazy { arguments?.getBoolean(BundleKey.KEY_IS_SHOW_ONBOARDING4_FRAGMENT) ?: false }

    override fun init(view: View) {
        setupDotsIndicator()
        setupNextButton()
        onBackEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
    }

    override fun onResume() {
        super.onResume()
        showReloadAds()
    }

    companion object {
        const val TAG = "OnboardingItem1Fragment"

        fun newInstance(isShowOnboarding4Fragment: Boolean): OnboardingItem1Fragment {
            return OnboardingItem1Fragment().apply {
                arguments = Bundle().apply {
                    putBoolean(BundleKey.KEY_IS_SHOW_ONBOARDING4_FRAGMENT, isShowOnboarding4Fragment)
                }
            }
        }
    }
}
