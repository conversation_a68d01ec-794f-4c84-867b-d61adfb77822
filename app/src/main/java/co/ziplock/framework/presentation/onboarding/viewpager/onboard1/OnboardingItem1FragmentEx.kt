package co.ziplock.framework.presentation.onboarding.viewpager.onboard1

import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.util.setPreventDoubleClickScaleView
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.show3NativeUsePriority
import kotlin.text.get

fun OnboardingItem1Fragment.setupDotsIndicator() {
    if (isShowOnboarding4Fragment) {
        binding.dotsIndicator.setNumberOfDots(4)
    } else {
        binding.dotsIndicator.setNumberOfDots(3)
    }

    binding.dotsIndicator.setSelectedPosition(0)
}

fun OnboardingItem1Fragment.setupNextButton() {
    binding.btnNext.setPreventDoubleClickScaleView {
        commonViewModel.sendGoToNextOnboardingScreenEvent()
    }
}

fun OnboardingItem1Fragment.onBackEvent() {
    onSystemBackEvent {
        // No action
    }
}

fun OnboardingItem1Fragment.showAds() {
    runCatching {
        show3NativeUsePriority(
            spaceNameConfig = "Onboard1.1",
            spaceName1 = "Onboard1_native1",
            spaceName2 = "Onboard1_native2",
            spaceName3 = "Onboard1_native3",
            includeHasBeenOpened = true,
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {
                isClickAds = true

                safePreloadAds(
                    spaceNameConfig = "Onboard1.2",
                    spaceNameAds = "Onboard1_native4",
                )
                safePreloadAds(
                    spaceNameConfig = "Onboard1.2",
                    spaceNameAds = "Onboard1_native5",
                )
                safePreloadAds(
                    spaceNameConfig = "Onboard1.2",
                    spaceNameAds = "Onboard1_native6",
                )
            },
        )
    }
}

fun OnboardingItem1Fragment.showReloadAds() {
    if (!isShowReloadAds && isClickAds && AdsConstant.listConfigAds["Onboard1.1"]?.isOn == true) {
        isShowReloadAds = true
        runCatching {
            show3NativeUsePriority(
                spaceNameConfig = "Onboard1.2",
                spaceName1 = "Onboard1_native4",
                spaceName2 = "Onboard1_native5",
                spaceName3 = "Onboard1_native6",
                includeHasBeenOpened = true,
                layoutToAttachAds = binding.adViewGroup,
                layoutContainAds = binding.layoutAds,
                onAdsClick = {},
            )
        }
    }
}
