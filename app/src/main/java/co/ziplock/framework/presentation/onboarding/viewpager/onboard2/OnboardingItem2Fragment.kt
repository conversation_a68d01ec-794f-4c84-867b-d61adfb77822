package co.ziplock.framework.presentation.onboarding.viewpager.onboard2

import android.os.Bundle
import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentOnboardingItem2NewBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.util.BundleKey

@AndroidEntryPoint
class OnboardingItem2Fragment :
    BaseFragment<FragmentOnboardingItem2NewBinding, OnboardingItem2ViewModel>(
        FragmentOnboardingItem2NewBinding::inflate,
        OnboardingItem2ViewModel::class.java,
    ) {

    val isShowOnboarding4Fragment by lazy { arguments?.getBoolean(BundleKey.KEY_IS_SHOW_ONBOARDING4_FRAGMENT) ?: false }

    override fun init(view: View) {
        setupDotsIndicator()
        onBackEvent()
        setupNextButton()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        // TODO("Not yet implemented")
    }

    companion object {
        const val TAG = "OnboardingItem2Fragment"

        fun newInstance(isShowOnboarding4Fragment: Boolean): OnboardingItem2Fragment {
            return OnboardingItem2Fragment().apply {
                arguments = Bundle().apply {
                    putBoolean(BundleKey.KEY_IS_SHOW_ONBOARDING4_FRAGMENT, isShowOnboarding4Fragment)
                }
            }
        }
    }
}
