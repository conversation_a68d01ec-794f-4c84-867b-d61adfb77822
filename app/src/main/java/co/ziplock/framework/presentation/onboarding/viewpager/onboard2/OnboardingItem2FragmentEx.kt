package co.ziplock.framework.presentation.onboarding.viewpager.onboard2

import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.util.setPreventDoubleClickScaleView
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun OnboardingItem2Fragment.setupDotsIndicator() {
    if (isShowOnboarding4Fragment) {
        binding.dotsIndicator.setNumberOfDots(4)
    } else {
        binding.dotsIndicator.setNumberOfDots(3)
    }

    binding.dotsIndicator.setSelectedPosition(1)
}

fun OnboardingItem2Fragment.onBackEvent() {
    onSystemBackEvent {
        backEvent()
    }
}

fun OnboardingItem2Fragment.backEvent() {
    commonViewModel.sendGoToPreviousOnboardingScreenEvent()
}

fun OnboardingItem2Fragment.setupNextButton() {
    binding.btnNext.setPreventDoubleClickScaleView {
        commonViewModel.sendGoToNextOnboardingScreenEvent()
    }
}

fun OnboardingItem2Fragment.showAds() {
    runCatching {
        showLoadedNative(
            spaceNameConfig = "Onboard2",
            spaceName = "Onboard2_native",
            includeHasBeenOpened = true,
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
}
