package co.ziplock.framework.presentation.onboarding.viewpager.onboard3

import android.os.Bundle
import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentOnboardingItem3Binding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.util.BundleKey

@AndroidEntryPoint
class OnboardingItem3Fragment :
    BaseFragment<FragmentOnboardingItem3Binding, OnboardingItem3ViewModel>(
        FragmentOnboardingItem3Binding::inflate,
        OnboardingItem3ViewModel::class.java,
    ) {

    val isShowOnboarding4Fragment by lazy { arguments?.getBoolean(BundleKey.KEY_IS_SHOW_ONBOARDING4_FRAGMENT) ?: false }

    override fun init(view: View) {
        setupDotsIndicator()
        setupNextButton()
        onBackEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        // TODO("Not yet implemented")
    }

    companion object {
        const val TAG = "OnboardingItem3Fragment"

        fun newInstance(isShowOnboarding4Fragment: Boolean): OnboardingItem3Fragment {
            return OnboardingItem3Fragment().apply {
                arguments = Bundle().apply {
                    putBoolean(BundleKey.KEY_IS_SHOW_ONBOARDING4_FRAGMENT, isShowOnboarding4Fragment)
                }
            }
        }
    }
}
