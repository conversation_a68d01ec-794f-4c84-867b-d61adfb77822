package co.ziplock.framework.presentation.onboarding.viewpager.onboard3

import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.util.setPreventDoubleClickScaleView
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun OnboardingItem3Fragment.setupDotsIndicator() {
    if (isShowOnboarding4Fragment) {
        binding.dotsIndicator.setNumberOfDots(4)
    } else {
        binding.dotsIndicator.setNumberOfDots(3)
    }

    binding.dotsIndicator.setSelectedPosition(2)
}

fun OnboardingItem3Fragment.setupNextButton() {
    binding.btnNext.setPreventDoubleClickScaleView {
        commonViewModel.sendGoToNextOnboardingScreenEvent()
    }
}

fun OnboardingItem3Fragment.onBackEvent() {
    onSystemBackEvent {
        commonViewModel.sendGoToPreviousOnboardingScreenEvent()
    }
}

fun OnboardingItem3Fragment.showAds() {
    runCatching {
        showLoadedNative(
            spaceNameConfig = "onboard3",
            spaceName = "onboard3_native",
            includeHasBeenOpened = true,
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
}
