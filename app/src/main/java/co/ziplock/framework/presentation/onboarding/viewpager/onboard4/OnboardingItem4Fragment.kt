package co.ziplock.framework.presentation.onboarding.viewpager.onboard4

import android.view.View
import co.ziplock.databinding.FragmentOnboardingItem4Binding
import co.ziplock.framework.presentation.common.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnboardingItem4Fragment :
    BaseFragment<FragmentOnboardingItem4Binding, OnboardingItem4ViewModel>(
        FragmentOnboardingItem4Binding::inflate,
        OnboardingItem4ViewModel::class.java,
    ) {
    var isShowedReloadAds = false

    override fun init(view: View) {
        setupDotsIndicator()
        setupNextButton()
        setupRecyclerView()
        onBackEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeFavoriteStyles()
    }
}
