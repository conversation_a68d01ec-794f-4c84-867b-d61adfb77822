package co.ziplock.framework.presentation.onboarding.viewpager.onboard4

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.onboarding.viewpager.onboard4.adapter.OnboardingFavoriteStyleAdapter
import co.ziplock.util.setPreventDoubleClickScaleView
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun OnboardingItem4Fragment.setupDotsIndicator() {
    binding.dotsIndicator.setSelectedPosition(3)
}

fun OnboardingItem4Fragment.setupNextButton() {
    // Initially hide the button
    binding.btnNext.visibility = android.view.View.GONE

    binding.btnNext.setPreventDoubleClickScaleView {
        commonViewModel.sendGoToNextOnboardingScreenEvent()
    }
}

fun OnboardingItem4Fragment.onBackEvent() {
    onSystemBackEvent {
        commonViewModel.sendGoToPreviousOnboardingScreenEvent()
    }
}

fun OnboardingItem4Fragment.showAds() {
    runCatching {
        showLoadedNative(
            spaceNameConfig = "onboard4.1",
            spaceName = "onboard4.1_native",
            includeHasBeenOpened = true,
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
}

fun OnboardingItem4Fragment.showReloadAds() {
    if (!isShowedReloadAds) {
        isShowedReloadAds = true
        runCatching {
            showLoadedNative(
                spaceNameConfig = "onboard4.2",
                spaceName = "onboard4.2_native",
                includeHasBeenOpened = true,
                layoutToAttachAds = binding.adViewGroup,
                layoutContainAds = binding.layoutAds,
                onAdsClick = {},
            )
        }
    }
}

fun OnboardingItem4Fragment.setupRecyclerView() {
    val adapter =
        OnboardingFavoriteStyleAdapter { style, isSelected ->
            viewModel.onStyleSelected(style, isSelected)
            showReloadAds()
        }

    binding.recyclerViewFavoriteStyle.apply {
        layoutManager = GridLayoutManager(context, 2)
        this.adapter = adapter
    }
}

fun OnboardingItem4Fragment.observeFavoriteStyles() {
    commonViewModel.onboardingUiState
        .map { it.onboardingFavoriteStyles }
        .flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
        .onEach { onboardingFavoriteStyles ->
            val adapter =
                binding.recyclerViewFavoriteStyle.adapter as? OnboardingFavoriteStyleAdapter
            // Filter only styles that should be shown
            val visibleStyles = onboardingFavoriteStyles.filter { it.isShow }
            adapter?.submitList(visibleStyles)
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    viewModel.selectedStyles.observe(viewLifecycleOwner) { selectedStyles ->
        // Update CommonViewModel with selected styles for use in HomeTabFragment
        commonViewModel.updateSelectedFavoriteStyles(selectedStyles)

        // Show/hide Start Now button based on selection
        updateStartNowButtonVisibility(selectedStyles.isNotEmpty())
    }
}

fun OnboardingItem4Fragment.updateStartNowButtonVisibility(hasSelection: Boolean) {
    binding.btnNext.visibility =
        if (hasSelection) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }
}
