package co.ziplock.framework.presentation.onboarding.viewpager.onboard4

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import dagger.hilt.android.lifecycle.HiltViewModel
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.model.OnboardingFavoriteStyle
import javax.inject.Inject

@HiltViewModel
class OnboardingItem4ViewModel @Inject constructor() : BaseViewModel() {

    private val _selectedStyles = MutableLiveData<List<OnboardingFavoriteStyle>>()
    val selectedStyles: LiveData<List<OnboardingFavoriteStyle>> = _selectedStyles

    fun onStyleSelected(style: OnboardingFavoriteStyle, isSelected: Boolean) {
        val currentSelected = _selectedStyles.value?.toMutableList() ?: mutableListOf()
        
        if (isSelected) {
            if (!currentSelected.contains(style)) {
                currentSelected.add(style)
            }
        } else {
            currentSelected.remove(style)
        }
        
        _selectedStyles.value = currentSelected
    }

    fun getSelectedStylesCount(): Int {
        return _selectedStyles.value?.size ?: 0
    }
}
