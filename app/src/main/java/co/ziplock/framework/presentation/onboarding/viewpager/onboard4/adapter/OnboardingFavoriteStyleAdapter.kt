package co.ziplock.framework.presentation.onboarding.viewpager.onboard4.adapter

import co.ziplock.R
import co.ziplock.databinding.ItemOnboardingFavoriteStyleBinding
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.OnboardingFavoriteStyle
import com.bumptech.glide.Glide

class OnboardingFavoriteStyleAdapter(
    private val onItemClick: (OnboardingFavoriteStyle, Boolean) -> Unit
) : BaseListAdapter<OnboardingFavoriteStyle, ItemOnboardingFavoriteStyleBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.name == newItem.name },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
    )
) {
    private val selectedItems = mutableSetOf<String>()

    fun getSelectedItems(): List<OnboardingFavoriteStyle> {
        return currentList.filter { selectedItems.contains(it.name) }
    }

    override fun getLayoutRes(viewType: Int): Int {
        return R.layout.item_onboarding_favorite_style
    }

    override fun bindView(
        binding: ItemOnboardingFavoriteStyleBinding,
        item: OnboardingFavoriteStyle,
        position: Int
    ) {
        binding.apply {
            textTitle.text = item.name

            // Load image using Glide
            when {
                item.thumbnailUrl != null -> {
                    Glide.with(imageView.context)
                        .load(item.thumbnailUrl)
                        .centerCrop()
                        .into(imageView)
                }

                item.defaultDrawable != null -> {
                    Glide.with(imageView.context)
                        .load(item.defaultDrawable)
                        .centerCrop()
                        .into(imageView)
                }
            }

            // Set selection state
            val isSelected = selectedItems.contains(item.name)
            root.isSelected = isSelected
            checkboxSelected.isChecked = isSelected

            // Handle click
            root.setOnClickListener {
                val newSelectionState = !selectedItems.contains(item.name)
                if (newSelectionState) {
                    item.name?.let { element -> selectedItems.add(element) }
                } else {
                    selectedItems.remove(item.name)
                }

                checkboxSelected.isChecked = newSelectionState
                root.isSelected = newSelectionState

                onItemClick(item, newSelectionState)
            }
        }
    }
}