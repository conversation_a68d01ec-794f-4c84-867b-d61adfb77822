package co.ziplock.framework.presentation.onboarding.viewpager.onboardNativeFull1

import android.view.View
import co.ziplock.databinding.FragmentOnboardNativeFullBinding
import co.ziplock.framework.presentation.common.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnboardNativeFull1Fragment :
    BaseFragment<FragmentOnboardNativeFullBinding, OnboardNativeFull1ViewModel>(
        FragmentOnboardNativeFullBinding::inflate,
        OnboardNativeFull1ViewModel::class.java,
    ) {

     var isClickAds = false
     var isShowReloadAds = false

    override fun init(view: View) {
        onBackEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        // TODO("Not yet implemented")
    }

    override fun onResume() {
        super.onResume()
        showReloadAds()
    }
}
