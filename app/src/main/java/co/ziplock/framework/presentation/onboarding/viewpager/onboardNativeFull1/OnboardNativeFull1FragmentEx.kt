package co.ziplock.framework.presentation.onboarding.viewpager.onboardNativeFull1

import android.view.LayoutInflater
import co.ziplock.framework.presentation.common.onSystemBackEvent
import pion.datlt.libads.R
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.show3NativeFullScreenUsePriority

fun OnboardNativeFull1Fragment.onBackEvent() {
    onSystemBackEvent {
        commonViewModel.sendGoToPreviousOnboardingScreenEvent()
    }
}

fun OnboardNativeFull1Fragment.showAds() {
    runCatching {
        show3NativeFullScreenUsePriority(
            spaceNameConfig = "onboardfull1.1",
            spaceName1 = "onboardfull_native1",
            spaceName2 = "onboardfull_native2",
            spaceName3 = "onboardfull_native3",
            includeHasBeenOpened = true,
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            viewAdsInflateFromXml =
                LayoutInflater
                    .from(requireContext())
                    .inflate(R.layout.layout_native_full_screen, null),
            onAdsClick = {
                isClickAds = true

                safePreloadAds(
                    spaceNameConfig = "onboardfull1.2",
                    spaceNameAds = "onboardfull_native4",
                )
                safePreloadAds(
                    spaceNameConfig = "onboardfull1.2",
                    spaceNameAds = "onboardfull_native5",
                )
                safePreloadAds(
                    spaceNameConfig = "onboardfull1.2",
                    spaceNameAds = "onboardfull_native6",
                )
            },
        )
    }
}

fun OnboardNativeFull1Fragment.showReloadAds() {
    if (!isShowReloadAds && isClickAds && AdsConstant.listConfigAds["onboardfull1.1"]?.isOn == true) {
        isShowReloadAds = true
        isClickAds = false
        runCatching {
            show3NativeFullScreenUsePriority(
                spaceNameConfig = "onboardfull1.2",
                spaceName1 = "onboardfull_native4",
                spaceName2 = "onboardfull_native5",
                spaceName3 = "onboardfull_native6",
                includeHasBeenOpened = true,
                layoutToAttachAds = binding.adViewGroup,
                layoutContainAds = binding.layoutAds,
                viewAdsInflateFromXml =
                    LayoutInflater
                        .from(context)
                        .inflate(R.layout.layout_native_full_screen, null),
                onAdsClick = {},
            )
        }
    }
}
