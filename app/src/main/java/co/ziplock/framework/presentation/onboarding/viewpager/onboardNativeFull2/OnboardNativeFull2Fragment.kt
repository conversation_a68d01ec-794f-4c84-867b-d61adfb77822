package co.ziplock.framework.presentation.onboarding.viewpager.onboardNativeFull2

import android.view.View
import co.cameralocation.framework.presentation.onboarding.viewpager.onboardNativeFull2.onBackEvent
import co.cameralocation.framework.presentation.onboarding.viewpager.onboardNativeFull2.showAds
import co.cameralocation.framework.presentation.onboarding.viewpager.onboardNativeFull2.showReloadAds
import co.ziplock.databinding.FragmentOnboardNativeFullBinding
import co.ziplock.framework.presentation.common.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnboardNativeFull2Fragment :
    BaseFragment<FragmentOnboardNativeFullBinding, OnboardNativeFull2ViewModel>(
        FragmentOnboardNativeFullBinding::inflate,
        OnboardNativeFull2ViewModel::class.java,
    ) {

    var isClickAds = false
    var isShowReloadAds = false

    override fun init(view: View) {
        onBackEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        // TODO("Not yet implemented")
    }

    override fun onResume() {
        super.onResume()
        showReloadAds()
    }
}
