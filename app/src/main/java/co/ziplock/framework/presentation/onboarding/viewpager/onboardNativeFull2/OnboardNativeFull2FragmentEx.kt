package co.cameralocation.framework.presentation.onboarding.viewpager.onboardNativeFull2

import android.view.LayoutInflater
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.onboarding.viewpager.onboardNativeFull2.OnboardNativeFull2Fragment
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.show3NativeFullScreenUsePriority

fun OnboardNativeFull2Fragment.onBackEvent() {
    onSystemBackEvent {
        commonViewModel.sendGoToPreviousOnboardingScreenEvent()
    }
}

fun OnboardNativeFull2Fragment.showAds() {
    runCatching {
        show3NativeFullScreenUsePriority(
            spaceNameConfig = "onboardfull2.1",
            spaceName1 = "onboardfull2_native1",
            spaceName2 = "onboardfull2_native2",
            spaceName3 = "onboardfull2_native3",
            includeHasBeenOpened = true,
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            viewAdsInflateFromXml =
                LayoutInflater
                    .from(requireContext())
                    .inflate(pion.datlt.libads.R.layout.layout_native_full_screen, null),
            onAdsClick = {
                isClickAds = true

                safePreloadAds(
                    spaceNameConfig = "onboardfull2.2",
                    spaceNameAds = "onboardfull2_native4",
                )
                safePreloadAds(
                    spaceNameConfig = "onboardfull2.2",
                    spaceNameAds = "onboardfull2_native5",
                )
                safePreloadAds(
                    spaceNameConfig = "onboardfull2.2",
                    spaceNameAds = "onboardfull2_native6",
                )
            },
        )
    }
}

fun OnboardNativeFull2Fragment.showReloadAds() {
    if (!isShowReloadAds && isClickAds && AdsConstant.listConfigAds["onboardfull2.1"]?.isOn == true) {
        isShowReloadAds = true
        isClickAds = false
        runCatching {
            show3NativeFullScreenUsePriority(
                spaceNameConfig = "onboardfull2.2",
                spaceName1 = "onboardfull2_native4",
                spaceName2 = "onboardfull2_native5",
                spaceName3 = "onboardfull2_native6",
                includeHasBeenOpened = true,
                layoutToAttachAds = binding.adViewGroup,
                layoutContainAds = binding.layoutAds,
                viewAdsInflateFromXml =
                    LayoutInflater
                        .from(context)
                        .inflate(pion.datlt.libads.R.layout.layout_native_full_screen, null),
                onAdsClick = {},
            )
        }
    }
}
