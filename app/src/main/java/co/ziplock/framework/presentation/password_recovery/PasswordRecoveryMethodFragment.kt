package co.ziplock.framework.presentation.password_recovery

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentPasswordRecoveryMethodBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@AndroidEntryPoint
class PasswordRecoveryMethodFragment : BaseFragment<FragmentPasswordRecoveryMethodBinding, PasswordRecoveryMethodViewModel>(
    FragmentPasswordRecoveryMethodBinding::inflate,
    PasswordRecoveryMethodViewModel::class.java
) {
    @Inject
    lateinit var securityManager: SecurityManager
    
    override fun init(view: View) {
        setupBackButtonClickListener()
        setupSecurityQuestionCardClickListener()
        setupEmailRecoveryCardClickListener()
    }

    override fun subscribeObserver(view: View) {
        //Do nothing
    }

    companion object {
        const val TAG = "PasswordRecoveryMethodFragment"
    }
}