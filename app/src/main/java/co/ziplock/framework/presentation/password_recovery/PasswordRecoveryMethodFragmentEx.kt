package co.ziplock.framework.presentation.password_recovery

import androidx.core.os.bundleOf
import androidx.navigation.fragment.findNavController
import co.ziplock.R
import co.ziplock.framework.presentation.dialog.ForgotPasswordDialogFragment
import co.ziplock.framework.presentation.dialog.PasswordType
import co.ziplock.framework.presentation.dialog.PasswordTypeSelectionDialogFragment
import co.ziplock.framework.presentation.dialog.PatternVerificationDialogFragment
import co.ziplock.framework.presentation.dialog.PinVerificationDialogFragment
import co.ziplock.framework.presentation.home.tabs.settings.SettingsTabFragment
import co.ziplock.framework.presentation.home.tabs.settings.showPasswordTypeSelectionDialogFragment
import co.ziplock.framework.presentation.manager.SecurityManager.SecurityType
import co.ziplock.framework.presentation.security.change_security_question.showPatternVerificationDialog
import co.ziplock.framework.presentation.security.change_security_question.showPinVerificationDialog
import co.ziplock.util.BundleKey
import co.ziplock.util.setPreventDoubleClick

fun PasswordRecoveryMethodFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        findNavController().popBackStack()
    }
}

fun PasswordRecoveryMethodFragment.setupSecurityQuestionCardClickListener() {
    binding.cardSecurityQuestion.setPreventDoubleClick {
        if (securityManager.hasSecurityQuestion()) {
            safeNav(
                R.id.passwordRecoveryMethodFragment,
                R.id.action_passwordRecoveryMethodFragment_to_changeSecurityQuestionFragment
            )
        } else {
            showFirstSetupPasswordTypeSelectionDialogFragment()
        }
    }
}

fun PasswordRecoveryMethodFragment.setupEmailRecoveryCardClickListener() {
    binding.cardEmailRecovery.setPreventDoubleClick {
        if (securityManager.hasSecurity()) {
            when (prefUtil.securityType) {
                SecurityType.PIN.name -> {
                    showPinVerificationDialog()
                }
                SecurityType.PATTERN.name -> {
                    showPatternVerificationDialog()
                }
                else -> {
                    // No security or unknown type, show password type selection
                }
            }
        } else {
            goToChangeOrCreateNewEmailScreen()
        }
    }
}

fun PasswordRecoveryMethodFragment.showPinVerificationDialog() {
    val dialogFragment = PinVerificationDialogFragment.newInstance()

    dialogFragment.onPinVerified = {
        // PIN verified successfully, show password type selection for changing password
       goToChangeOrCreateNewEmailScreen()
    }

    dialogFragment.onCancel = {
        // User cancelled, do nothing
    }

    dialogFragment.onForgotPassword = {
        // Show password recovery method dialog
        showPasswordRecoveryMethodDialog()
    }

    dialogFragment.show(parentFragmentManager, PinVerificationDialogFragment.TAG)
}

fun PasswordRecoveryMethodFragment.showPatternVerificationDialog() {
    val dialogFragment = PatternVerificationDialogFragment.newInstance()

    dialogFragment.onPatternVerified = {
        // Pattern verified successfully, show password type selection for changing password
        goToChangeOrCreateNewEmailScreen()
    }

    dialogFragment.onCancel = {
        // User cancelled, do nothing
    }

    dialogFragment.onForgotPassword = {
        // Show password recovery method dialog
        showPasswordRecoveryMethodDialog()
    }

    dialogFragment.show(parentFragmentManager, PatternVerificationDialogFragment.TAG)
}

fun PasswordRecoveryMethodFragment.showPasswordRecoveryMethodDialog() {
    ForgotPasswordDialogFragment.show(
        fragmentManager = parentFragmentManager,
        onSecurityQuestionSelected = {
            // Navigate to security question answer fragment
            safeNav(R.id.passwordRecoveryMethodFragment, R.id.action_passwordRecoveryMethodFragment_to_securityQuestionForgotPasswordFragment)
        },
        onEmailRecoverySelected = {
            // Navigate to email password recovery fragment
            safeNav(R.id.passwordRecoveryMethodFragment, R.id.action_passwordRecoveryMethodFragment_to_emailPasswordRecoveryWhenForgotPasswordFragment)
        },
        onCancel = {
            // User cancelled, do nothing
        }
    )
}

fun PasswordRecoveryMethodFragment.goToChangeOrCreateNewEmailScreen() {
    if (securityManager.hasRecoveryEmail()) {
        safeNav(
            R.id.passwordRecoveryMethodFragment,
            R.id.action_passwordRecoveryMethodFragment_to_changeRecoveryEmailFragment
        )
    } else {
        safeNav(
            R.id.passwordRecoveryMethodFragment,
            R.id.action_passwordRecoveryMethodFragment_to_setupRecoveryEmailFromRecoveryEmailFragment,
        )
    }
}

fun PasswordRecoveryMethodFragment.showFirstSetupPasswordTypeSelectionDialogFragment(
    initialPasswordType: PasswordType = PasswordType.PIN
) {
    val dialogFragment = PasswordTypeSelectionDialogFragment.newInstance(initialPasswordType)

    dialogFragment.onPasswordTypeSelected = { passwordType ->
        when (passwordType) {
            PasswordType.PIN -> {
                dialogFragment.dismissAllowingStateLoss()
                // Navigate to PIN setup fragment
                safeNav(
                    R.id.passwordRecoveryMethodFragment,
                    R.id.action_passwordRecoveryMethodFragment_to_pinSetupFragment,
                    bundleOf(BundleKey.KEY_FIRST_SETUP_PASSWORD to true)
                )
            }

            PasswordType.PATTERN -> {
                dialogFragment.dismissAllowingStateLoss()
                // Navigate to Pattern setup fragment
                safeNav(
                    R.id.passwordRecoveryMethodFragment,
                    R.id.action_passwordRecoveryMethodFragment_to_patternSetupFragment,
                    bundleOf(BundleKey.KEY_FIRST_SETUP_PASSWORD to true)
                )
            }
        }
    }

    dialogFragment.onCancel = {
        //Do nothing
    }

    dialogFragment.show(parentFragmentManager, SettingsTabFragment.TAG)
}