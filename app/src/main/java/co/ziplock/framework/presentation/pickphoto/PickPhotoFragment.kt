package co.ziplock.framework.presentation.pickphoto

import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import co.ziplock.framework.presentation.pickphoto.adapter.DevicePhotoAdapter
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentPickPhotoBinding
import co.ziplock.framework.presentation.common.BaseFragment

@AndroidEntryPoint
class PickPhotoFragment :
    BaseFragment<FragmentPickPhotoBinding, PickPhotoViewModel>(
        FragmentPickPhotoBinding::inflate,
        PickPhotoViewModel::class.java,
    ) {
    lateinit var devicePhotoAdapter: DevicePhotoAdapter

    val requestPermissionLauncher =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions(),
        ) { permissions ->
            val allGranted = permissions.all { it.value }
            if (allGranted) {
                loadPhotos()
            }
        }
    var isShowingNative2 = false

    override fun init(view: View) {
        setupBackButton()
        setupPhotoSourceSpinner()
        setupDevicePhotoAdapter()
        setupButtons()
        checkPermissionsAndLoadPhotos()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeDevicePhotoListChanged()
        observeLoadingState()
        observePhotoSourceMode()
        observeAvailableAlbums()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isShowingNative2 = false
    }

    companion object {
        const val TAG = "PickPhotoFragment"
    }
}
