package co.ziplock.framework.presentation.pickphoto

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.appcompat.widget.AppCompatSpinner
import androidx.appcompat.widget.ListPopupWindow
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.pickphoto.DevicePhoto
import co.ziplock.framework.presentation.model.pickphoto.PhotoSourceMode
import co.ziplock.framework.presentation.pickphoto.adapter.DevicePhotoAdapter
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber
import kotlin.text.get

fun PickPhotoFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
    onSystemBackEvent {
        backEvent()
    }
}

fun PickPhotoFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "gallery-back",
        spaceName = "gallery-1ID_interstitial",
        destinationToShowAds = R.id.pickPhotoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safeNavigateUp(R.id.pickPhotoFragment)
        },
        onCloseAds = {},
    )
}

fun PickPhotoFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("gallery-choosept", "gallery-back"),
        spaceNameAds = "gallery-1ID_interstitial",
    )
    showLoadedNative(
        spaceNameConfig = "gallery",
        spaceName = "gallery_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}

fun PickPhotoFragment.setupDevicePhotoAdapter() {
    devicePhotoAdapter =
        DevicePhotoAdapter().apply {
            onItemClick = { photo ->
                // Navigate to edit image/layout when user selects a photo
                navigateToEditImage(photo)
            }
        }

    binding.rvDevicePhotos.apply {
        adapter = devicePhotoAdapter
        layoutManager = GridLayoutManager(requireContext(), 3)
    }
}

fun PickPhotoFragment.setupButtons() {
}

fun PickPhotoFragment.checkPermissionsAndLoadPhotos() {
    val permissions =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        }

    if (permissions.all {
            ContextCompat.checkSelfPermission(
                requireContext(),
                it,
            ) == PackageManager.PERMISSION_GRANTED
        }
    ) {
        loadPhotos()
    } else {
        requestPermissionLauncher.launch(permissions)
    }
}

fun PickPhotoFragment.setupPhotoSourceSpinner() {
    // Initialize with basic options
    updateSpinnerOptions()

    // Set dropdown height limit to 240dp for AppCompatSpinner
    binding.spinnerPhotoSource.post {
        setAppCompatSpinnerDropdownHeight(binding.spinnerPhotoSource, 240)
    }

    binding.spinnerPhotoSource.onItemSelectedListener =
        object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                handleSpinnerSelection(position)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                // Do nothing
            }
        }
}

private fun PickPhotoFragment.setAppCompatSpinnerDropdownHeight(
    spinner: AppCompatSpinner,
    heightDp: Int,
) {
    try {
        val dropDownHeight = (heightDp * resources.displayMetrics.density).toInt()

        // For AppCompatSpinner, try to access the popup window
        val popupField = spinner.javaClass.getDeclaredField("mPopup")
        popupField.isAccessible = true
        val popup = popupField.get(spinner)

        when (popup) {
            is ListPopupWindow -> {
                popup.height = dropDownHeight
            }

            is android.widget.ListPopupWindow -> {
                popup.height = dropDownHeight
            }

            else -> {
                // Try alternative approach for other popup types
                try {
                    val heightField = popup?.javaClass?.getDeclaredField("mDropDownHeight")
                    heightField?.isAccessible = true
                    heightField?.set(popup, dropDownHeight)
                } catch (e: Exception) {
                    // Try another field name
                    try {
                        val heightField = popup?.javaClass?.getDeclaredField("mMaxHeight")
                        heightField?.isAccessible = true
                        heightField?.set(popup, dropDownHeight)
                    } catch (ex: Exception) {
                        Timber.d("Could not set dropdown height: ${ex.message}")
                    }
                }
            }
        }
    } catch (e: Exception) {
        Timber.d("Failed to set AppCompatSpinner dropdown height: ${e.message}")
    }
}

private fun PickPhotoFragment.updateSpinnerOptions() {
    val uiState = viewModel.uiState.value
    val options = mutableListOf<String>()

    // Add basic options
    options.add(getString(R.string.all_photos))

    // Add albums if available
    if (uiState.availableAlbums.isNotEmpty()) {
        uiState.availableAlbums.forEach { album ->
            options.add("${album.name} (${album.photoCount})")
        }
    }

    val adapter = ArrayAdapter(requireContext(), R.layout.item_photo_source_spinner, options)
    adapter.setDropDownViewResource(R.layout.item_photo_source_spinner)
    binding.spinnerPhotoSource.adapter = adapter

    // Set dropdown height limit to 240dp using our helper function
    binding.spinnerPhotoSource.post {
        setAppCompatSpinnerDropdownHeight(binding.spinnerPhotoSource, 240)
    }
}

private fun PickPhotoFragment.handleSpinnerSelection(position: Int) {
    val uiState = viewModel.uiState.value

    when {
        position == 0 -> {
            // All Photos
            viewModel.setPhotoSourceMode(PhotoSourceMode.ALL_PHOTOS)
        }

        position >= 1 && position - 1 < uiState.availableAlbums.size -> {
            // Album selected
            val albumIndex = position - 1
            val selectedAlbum = uiState.availableAlbums[albumIndex]
            viewModel.selectAlbum(selectedAlbum)
        }

        else -> {
            viewModel.setPhotoSourceMode(PhotoSourceMode.ALL_PHOTOS)
        }
    }
}

fun PickPhotoFragment.loadPhotos() {
    viewModel.loadPhotos()
}

fun PickPhotoFragment.observeDevicePhotoListChanged() {
    // Observe photo source mode changes
    viewModel.uiState
        .map {
            Triple(
                it.photoSourceMode,
                when (it.photoSourceMode) {
                    PhotoSourceMode.ALL_PHOTOS -> it.devicePhotos
                    PhotoSourceMode.ALBUM -> it.albumPhotos
                },
                it.selectedAlbum,
            )
        }.onEach {
            Timber.d("Photo source mode changed: ${it.first} - ${it.second.size} items - Album: ${it.third?.name}")
        }.distinctUntilChanged()
        .onEach { (mode, photos, album) ->
            devicePhotoAdapter.submitList(photos)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.observeLoadingState() {
    viewModel.uiState
        .map { it.isLoading }
        .onEach { isLoading ->
            Timber.d("Loading state changed: $isLoading")
        }.distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressLoading.isVisible = isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.observePhotoSourceMode() {
    viewModel.uiState
        .map { Pair(it.photoSourceMode, it.selectedAlbum) }
        .onEach { (mode, album) ->
            Timber.d("Photo source mode changed: $mode, Album: ${album?.name}")
        }.distinctUntilChanged()
        .onEach { (mode, album) ->
            val position =
                when (mode) {
                    PhotoSourceMode.ALL_PHOTOS -> 0
                    PhotoSourceMode.ALBUM -> {
                        // Find album position in spinner
                        val uiState = viewModel.uiState.value
                        if (album != null) {
                            val albumIndex = uiState.availableAlbums.indexOf(album)
                            if (albumIndex >= 0) albumIndex + 1 else 0
                        } else {
                            0
                        }
                    }
                }

            if (position < binding.spinnerPhotoSource.count) {
                binding.spinnerPhotoSource.setSelection(position)
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.observeAvailableAlbums() {
    viewModel.uiState
        .map { it.availableAlbums }
        .distinctUntilChanged()
        .onEach { albums ->
            Timber.d("Available albums changed: ${albums.size} albums")
            updateSpinnerOptions()
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.navigateToEditImage(photo: DevicePhoto) {
    // Get save context from arguments
    showLoadedInter(
        spaceNameConfig = "gallery-choosept",
        spaceName = "gallery-1ID_interstitial",
        destinationToShowAds = R.id.pickPhotoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            val saveContext =
                arguments?.getString(BundleKey.KEY_SAVE_CONTEXT) ?: Constant.SAVE_CONTEXT_WALLPAPER
            val bundle = Bundle()
            bundle.putParcelable(BundleKey.KEY_SELECTED_PHOTO, photo)
            bundle.putString(BundleKey.KEY_SAVE_CONTEXT, saveContext)
            safeNavInter(
                R.id.pickPhotoFragment,
                R.id.action_pickPhotoFragment_to_editImageFragment,
                bundle,
            )
        },
        onCloseAds = {},
    )
}
