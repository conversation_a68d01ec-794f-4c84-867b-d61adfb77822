package co.ziplock.framework.presentation.pickphoto

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.common.launchIO
import co.ziplock.framework.presentation.model.pickphoto.DevicePhoto
import co.ziplock.framework.presentation.model.pickphoto.PhotoAlbum
import co.ziplock.framework.presentation.model.pickphoto.PhotoSourceMode
import co.ziplock.framework.presentation.model.pickphoto.PickPhotoUiState
import co.ziplock.repository.FileRepository
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class PickPhotoViewModel @Inject constructor(
    private val fileRepository: FileRepository,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(PickPhotoUiState())
    val uiState: StateFlow<PickPhotoUiState> = _uiState.asStateFlow()

    fun loadPhotos() {
        Timber.d("loadPhotos called")
        _uiState.update { it.copy(isLoading = true) }

        launchIO(
            exceptionHandler = CoroutineExceptionHandler {
                    context, throwable ->
                Timber.e("Error loading photos: ${throwable}")
                _uiState.update { it.copy(isLoading = false) }
            }
        ) {
            // Load all device photos and albums
            val allPhotosDeferred = async { queryAllDevicePhotos() }
            val albumsDeferred = async { queryAllAlbums() }

            listOf(allPhotosDeferred, albumsDeferred).awaitAll().let { results ->
                val allPhotos = results[0] as List<DevicePhoto>
                val albums = results[1] as List<PhotoAlbum>
                
                Timber.d("Loaded ${allPhotos.size} photos and ${albums.size} albums")
                _uiState.update {
                    it.copy(
                        devicePhotos = allPhotos,
                        availableAlbums = albums,
                        isLoading = false
                    )
                }
            }
        }
    }

    private suspend fun queryAllDevicePhotos(): List<DevicePhoto> = withContext(Dispatchers.IO) {
        val files = fileRepository.readAllImagesDCIM()

        Timber.d("Found ${files.size} images")

        val photos = files.map { photo ->
            DevicePhoto(
                id = photo.imageUri.hashCode().toLong(),
                uri = photo.imageUri,
                path = photo.imagePath,
            )
        }

        Timber.d("Filtered out ${photos.size - files.size} non-image files")

        return@withContext photos
    }

    private suspend fun queryAllAlbums(): List<PhotoAlbum> = withContext(Dispatchers.IO) {
        return@withContext fileRepository.getAllPhotoAlbums()
    }

    private suspend fun queryPhotosFromAlbum(albumId: Long): List<DevicePhoto> = withContext(Dispatchers.IO) {
        val files = fileRepository.getPhotosFromAlbum(albumId)
        
        val photos = files.map { photo ->
            DevicePhoto(
                id = photo.imageUri.hashCode().toLong(),
                uri = photo.imageUri,
                path = photo.imagePath,
            )
        }

        return@withContext photos
    }

    /*private suspend fun queryAppPhotos(): List<DevicePhoto> = withContext(Dispatchers.IO) {
        val files = fileRepository.readImagesFromSpecificDCIMPath(
            FileRepositoryImpl.DEFAULT_CAMERA_GPS_FOLDER_PATH
        )

        val photos = files.map { photo ->
            DevicePhoto(
                id = photo.imageUri.hashCode().toLong(),
                uri = photo.imageUri,
                path = photo.imagePath,
                isSelected = _uiState.value.selectedPhotos.any { it.uri == photo.imageUri }
            )
        }

        return@withContext photos
    }*/

    /*fun togglePhotoSelection(photo: DevicePhoto) {
        val currentSelectedPhotos = _uiState.value.selectedPhotos.toMutableList()
        val isCurrentlySelected = currentSelectedPhotos.any { it.id == photo.id }

        if (isCurrentlySelected) {
            // Remove from selection
            currentSelectedPhotos.removeAll { it.id == photo.id }
        } else {
            // Add to selection if not at max
            if (currentSelectedPhotos.size < _uiState.value.requiredPhotoCount) {
                currentSelectedPhotos.add(photo)
            }
        }

        updateSelectedPhotos(currentSelectedPhotos)
    }

    fun removeSelectedPhoto(photo: DevicePhoto) {
        val currentSelectedPhotos = _uiState.value.selectedPhotos.toMutableList()
        currentSelectedPhotos.removeAll { it.id == photo.id }
        updateSelectedPhotos(currentSelectedPhotos)
    }

    fun clearAllSelectedPhotos() {
        updateSelectedPhotos(emptyList())
    }

    fun reorderSelectedPhotos(fromPosition: Int, toPosition: Int) {
        val currentSelectedPhotos = _uiState.value.selectedPhotos.toMutableList()
        if (fromPosition < toPosition) {
            for (i in fromPosition until toPosition) {
                currentSelectedPhotos[i] = currentSelectedPhotos[i + 1].also {
                    currentSelectedPhotos[i + 1] = currentSelectedPhotos[i]
                }
            }
        } else {
            for (i in fromPosition downTo toPosition + 1) {
                currentSelectedPhotos[i] = currentSelectedPhotos[i - 1].also {
                    currentSelectedPhotos[i - 1] = currentSelectedPhotos[i]
                }
            }
        }

        updateSelectedPhotos(currentSelectedPhotos)
    }

    private fun updateSelectedPhotos(selectedPhotos: List<DevicePhoto>) {
        val isEnableGoToNextScreen = selectedPhotos.size == _uiState.value.requiredPhotoCount

        // Update device photos to reflect selection state
        val updatedDevicePhotos = _uiState.value.devicePhotos.map { devicePhoto ->
            val isSelected = selectedPhotos.any { it.id == devicePhoto.id }
            devicePhoto.copy(isSelected = isSelected)
        }

        // Update app photos to reflect selection state
        val updatedAppPhotos = _uiState.value.appPhotos.map { appPhoto ->
            val isSelected = selectedPhotos.any { it.id == appPhoto.id }
            appPhoto.copy(isSelected = isSelected)
        }

        _uiState.update {
            it.copy(
                selectedPhotos = selectedPhotos,
                devicePhotos = updatedDevicePhotos,
                appPhotos = updatedAppPhotos,
                isEnableGoToNextScreen = isEnableGoToNextScreen
            )
        }
    }*/

    fun setPhotoSourceMode(mode: PhotoSourceMode) {
        // Only update if the mode is different
        if (_uiState.value.photoSourceMode != mode) {
            _uiState.update { it.copy(photoSourceMode = mode) }
          }
    }

    fun selectAlbum(album: PhotoAlbum) {
        _uiState.update { 
            it.copy(
                selectedAlbum = album,
                photoSourceMode = PhotoSourceMode.ALBUM
            ) 
        }
        
        // Load photos from selected album
        launchIO(
            exceptionHandler = CoroutineExceptionHandler { context, throwable ->
                Timber.e("Error loading album photos: ${throwable}")
                _uiState.update { it.copy(isLoading = false) }
            }
        ) {
            _uiState.update { it.copy(isLoading = true) }
            
            val albumPhotos = queryPhotosFromAlbum(album.id)
            
            _uiState.update {
                it.copy(
                    albumPhotos = albumPhotos,
                    isLoading = false
                )
            }
        }
    }

    fun navigateToEditGridPhoto() {

    }
}
