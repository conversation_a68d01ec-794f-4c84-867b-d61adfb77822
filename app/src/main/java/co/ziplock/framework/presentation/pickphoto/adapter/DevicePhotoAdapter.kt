package co.ziplock.framework.presentation.pickphoto.adapter

import com.bumptech.glide.Glide
import co.ziplock.R
import co.ziplock.databinding.ItemDevicePhotoBinding
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.pickphoto.DevicePhoto
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy

class DevicePhotoAdapter : BaseListAdapter<DevicePhoto, ItemDevicePhotoBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
        areContentsTheSame = { oldItem, newItem -> 
            oldItem.uri == newItem.uri
        }
    )
) {
    var onItemClick: ((DevicePhoto) -> Unit)? = null

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_device_photo

    override fun bindView(binding: ItemDevicePhotoBinding, item: DevicePhoto, position: Int) {
        binding.apply {
            // Load photo
            Glide.with(binding.root.context)
                .load(item.uri)
                .override(binding.ivPhoto.width, binding.ivPhoto.height)
                .format(DecodeFormat.PREFER_RGB_565)
                .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                .skipMemoryCache(true)
                .centerCrop()
                .into(ivPhoto)

            // Set click listener
            root.setPreventDoubleClick {
                onItemClick?.invoke(item)
            }
        }
    }

    override fun bindView(
        binding: ItemDevicePhotoBinding,
        item: DevicePhoto,
        position: Int,
        payloads: MutableList<Any>
    ) {
        bindView(binding, item, position)
    }
}
