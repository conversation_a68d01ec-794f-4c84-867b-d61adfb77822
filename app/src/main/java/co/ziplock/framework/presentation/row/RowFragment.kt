package co.ziplock.framework.presentation.row

import android.view.View
import co.ziplock.R
import co.ziplock.databinding.FragmentRowBinding
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.row.adapter.RowAdapter
import co.ziplock.framework.presentation.theme.dialog.RewardContentDialog
import co.ziplock.util.BundleKey
import co.ziplock.util.displayToast
import dagger.hilt.android.AndroidEntryPoint
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.showLoadedRewardVideo

@AndroidEntryPoint
class RowFragment :
    BaseFragment<FragmentRowBinding, RowViewModel>(
        FragmentRowBinding::inflate,
        RowViewModel::class.java,
    ),
    RowAdapter.Listener,
    RewardContentDialog.Listener {
    val rowAdapter = RowAdapter()

    val isReEditFlow by lazy {
        arguments?.getBoolean(BundleKey.KEY_FROM_RE_EDIT_FLOW) ?: false
    }
    var currentRow: RowResponse? = null

    override fun init(view: View) {
        setupRecyclerView()
        setupSystemBackEvent()
        setupBackButtonClickListener()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeAllRows() // This will show all rows
        observeLoadingState()
    }

    override fun onRowClick(row: RowResponse) {
        currentRow = row
        if (checkConditionShowAds(
                context = requireContext(),
                spaceNameConfig = "rowpro",
            ) &&
            row.isPro == true &&
            !commonViewModel.isRowUnlocked(row.id)
        ) {
            val dialog = RewardContentDialog()
            dialog.setListener(this)
            dialog.show(childFragmentManager)
        } else {
            showInterWhenClickRow()
        }
    }

    override fun onWatchVideoEvent() {
        showLoadedRewardVideo(
            spaceNameConfig = "rowpro",
            spaceName = "rowpro_rewarded",
            destinationToShowAds = R.id.rowFragment,
            isShowLoadingView = true,
            isScreenType = false,
            onRewardDone = { isSuccess ->
                if (!isSuccess) {
                    showInterWhenClickRow()
                }
            },
            onGetReward = {
                if (currentRow == null) {
                    displayToast(R.string.something_error)
                    return@showLoadedRewardVideo
                }
                logicClickRow()
            },
        )
    }

    override fun onBuyVipVersion() {
        safeNav(R.id.rowFragment, R.id.action_to_iapFragment)
    }

    companion object {
        const val TAG = "RowFragment"

        fun newInstance() = RowFragment()
    }
}
