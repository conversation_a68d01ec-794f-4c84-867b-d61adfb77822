package co.ziplock.framework.presentation.row

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun RowFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun RowFragment.setupSystemBackEvent() {
    onSystemBackEvent {
        backEvent()
    }
}

fun RowFragment.backEvent() {
    findNavController().navigateUp()
}

fun RowFragment.logicClickRow() {
    if (currentRow == null) {
        displayToast(R.string.something_error)
        return
    }
    if (currentRow!!.isPro == true) {
        commonViewModel.addUnlockedRow(currentRow!!.id ?: "")
    }
    rowAdapter.setSelectedRow(currentRow)
    if (isReEditFlow) {
        // If coming from EditLayoutFragment, set data to CommonViewModel and go back
        commonViewModel.setEditLayoutRow(currentRow!!)
        safeNavigateUp(R.id.rowFragment)
    } else {
        // Normal flow - set data to CommonViewModel and navigate to EditLayout
        commonViewModel.setEditLayoutRow(currentRow!!)
        navigateToEditLayout(currentRow!!)
    }
}

fun RowFragment.showInterWhenClickRow() {
    showLoadedInter(
        spaceNameConfig = "row-nopro",
        spaceName = "content_1ID_interstitial",
        destinationToShowAds = R.id.rowFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            logicClickRow()
        },
        onCloseAds = {},
    )
}

fun RowFragment.navigateToEditLayout(row: RowResponse) {
    // Save selected row response to preferences for preview
    commonViewModel.setSelectedRowResponse(row)

    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.ROW_DATA, row)
        }
    safeNavInter(
        R.id.rowFragment,
        R.id.action_rowFragment_to_editLayoutFragment,
        bundle,
    )
}

fun RowFragment.setupRecyclerView() {
    rowAdapter.setFragment(this)
    rowAdapter.setListener(this)
    val gridLayoutManager = GridLayoutManager(requireContext(), 3)
    gridLayoutManager.spanSizeLookup =
        object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                val item = rowAdapter.currentList.getOrNull(position)
                return if (item?.adsItem != null) 3 else 1
            }
        }

    binding.rvRows.apply {
        layoutManager = gridLayoutManager
        adapter = rowAdapter
    }
}

fun RowFragment.observeAllRows() {
    commonViewModel.rowUiState
        .map { it.allRows }
        .distinctUntilChanged()
        .onEach { rows ->
            rowAdapter.submitList(filterListAds(rows))
            // Show/hide empty state
            binding.emptyState.isVisible =
                rows.isEmpty() &&
                !commonViewModel.rowUiState.value.isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun RowFragment.observeSelectedCategory() {
    // No longer needed since we don't have categories
}

fun RowFragment.observeLoadingState() {
    commonViewModel.rowUiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressBar.isVisible = isLoading
            binding.rvRows.isVisible = !isLoading
            // Hide empty state when loading
            if (isLoading) {
                binding.emptyState.isVisible = false
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun RowFragment.filterListAds(rawList: List<RowResponse>): List<RowResponse> {
    val tag = "filterListAdsZip"
    if (!checkConditionShowAds(
            context = requireContext(),
            spaceNameConfig = "listrow-ct",
        )
    ) {
        Timber.tag(tag).d("rawList $rawList")
        return rawList
    }

    val editedList = mutableListOf<RowResponse>()
    var count = 0
    var isLastTimeAddAds1 = false

    val itemAds1 =
        AdsItem(
            configName = "listrow-ct",
            admobIdName = "listrow-ct_native1",
        )
    val zipItemAds1 =
        RowResponse(
            id = null,
            isPro = null,
            adsItem = itemAds1,
            imageRowLeft = null,
            imageRowRight = null,
            previewThumbnail = null,
        )

    val itemAds2 =
        AdsItem(
            configName = "listrow-ct",
            admobIdName = "listrow-ct_native2",
        )

    val zipItemAds2 =
        RowResponse(
            id = null,
            isPro = null,
            adsItem = itemAds2,
            imageRowLeft = null,
            imageRowRight = null,
            previewThumbnail = null,
        )

    rawList.forEachIndexed { index, item ->
        editedList.add(item)
        count++
        if (index == 2 || count == Constant.numberOfContentBetweenRowList * 3) {
            if (!isLastTimeAddAds1) {
                editedList.add(zipItemAds1)
                isLastTimeAddAds1 = true
            } else {
                editedList.add(zipItemAds2)
                isLastTimeAddAds1 = false
            }
            count = 0
        }
    }
    Timber.tag(tag).d("editedList $editedList")
    return editedList
}

fun RowFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "listrow-ct",
        spaceNameAds = "listrow-ct_native1",
        includeHasBeenOpened = false,
    )
    safePreloadAds(
        spaceNameConfig = "listrow-ct",
        spaceNameAds = "listrow-ct_native2",
        includeHasBeenOpened = false,
    )
    showLoadedNative(
        spaceNameConfig = "listrow",
        spaceName = "listrow_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
    safePreloadAds(
        spaceNameConfig = "rowpro",
        spaceNameAds = "rowpro_rewarded",
    )
    safePreloadAds(
        spaceNameConfig = "row-nopro",
        spaceNameAds = "content_1ID_interstitial",
    )
}
