package co.ziplock.framework.presentation.row.adapter

import android.view.LayoutInflater
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import co.ziplock.R
import co.ziplock.databinding.ItemRowBinding
import co.ziplock.databinding.ViewItemNativeHotTrendingBinding
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import pion.datlt.libads.utils.adsuntils.showLoadedNative

class RowAdapter :
    BaseListAdapter<RowResponse, ViewDataBinding>(
        createDiffCallback(
            areItemsTheSame = { oldItem, newItem -> oldItem.previewThumbnail == newItem.previewThumbnail },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
        ),
    ) {
    private var selectedRow: RowResponse? = null

    interface Listener {
        fun onRowClick(row: RowResponse)
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    override fun getLayoutRes(viewType: Int): Int =
        if (viewType == ITEM_TYPE_ADS) R.layout.view_item_native_hot_trending else R.layout.item_row

    override fun getItemViewType(position: Int): Int = if (getItem(position).adsItem != null) ITEM_TYPE_ADS else ITEM_TYPE_NORMAL

    private var fragment: Fragment? = null

    fun setFragment(fragment: Fragment) {
        this.fragment = fragment
    }

    override fun bindView(
        binding: ViewDataBinding,
        item: RowResponse,
        position: Int,
    ) {
        if (binding is ItemRowBinding) {
            // Load preview thumbnail
            item.previewThumbnail?.let { thumbnail ->
                Glide
                    .with(binding.root.context)
                    .load(thumbnail)
                    .override(binding.ivRowPreview.width, binding.ivRowPreview.height)
                    .fitCenter()
                    .into(binding.ivRowPreview)
            }

            // Set click listener
            binding.root.setPreventDoubleClick {
                listener?.onRowClick(item)
            }
        }
        if (binding is ViewItemNativeHotTrendingBinding) {
            binding.layoutAds.post {
                val view =
                    LayoutInflater
                        .from(binding.root.context)
                        .inflate(R.layout.layout_native_trending_list, null)
                fragment?.showLoadedNative(
                    spaceNameConfig = item.adsItem?.configName ?: "",
                    spaceName = item.adsItem?.admobIdName ?: "",
                    includeHasBeenOpened = true,
                    viewAdsInflateFromXml = view,
                    ratioView = "328:136",
                    layoutToAttachAds = binding.adViewGroup,
                    layoutContainAds = binding.layoutAds,
                    onAdsClick = {},
                )
            }
        }
    }

    fun setSelectedRow(row: RowResponse?) {
        val oldSelected = selectedRow
        selectedRow = row

        // Notify changes for old and new selected items
        oldSelected?.let { old ->
            currentList.indexOf(old).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
        row?.let { new ->
            currentList.indexOf(new).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
    }

    companion object {
        const val ITEM_TYPE_ADS = 1
        const val ITEM_TYPE_NORMAL = 0
    }
}
