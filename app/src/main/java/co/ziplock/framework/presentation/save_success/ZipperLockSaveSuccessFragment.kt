package co.ziplock.framework.presentation.save_success

import android.view.View
import co.ziplock.databinding.FragmentZipperLockSaveSuccessBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.common.CommonViewModel
import co.ziplock.framework.presentation.home.adapter.HotTrendingThemeModelAdapter
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ZipperLockSaveSuccessFragment :
    BaseFragment<FragmentZipperLockSaveSuccessBinding, CommonViewModel>(
        FragmentZipperLockSaveSuccessBinding::inflate,
        CommonViewModel::class.java,
    ),
    HotTrendingThemeModelAdapter.Listener {
    val hotTrendingAdapter = HotTrendingThemeModelAdapter()

    override fun init(view: View) {
        setupRecyclerView()
        setupClickListeners()
        startSuccessAnimation()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeHotTrendingThemes()
    }

    override fun onTryNowClick(item: HotTrendingThemeModel) {
        onHotTrendingThemeClick(item)
    }

    companion object {
        const val TAG = "ZipperLockSaveSuccessFragment"
    }
}
