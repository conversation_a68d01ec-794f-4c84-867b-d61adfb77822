package co.ziplock.framework.presentation.save_success

import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.util.BundleKey
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun ZipperLockSaveSuccessFragment.startSuccessAnimation() {
    // Start the success Lottie animation
    binding.lottieSuccess.apply {
        playAnimation()
    }
}

fun ZipperLockSaveSuccessFragment.setupRecyclerView() {
    hotTrendingAdapter.setListener(this)
    binding.rvTop3Themes.apply {
        layoutManager = GridLayoutManager(context, 3)
        adapter = hotTrendingAdapter
    }
}

fun ZipperLockSaveSuccessFragment.setupClickListeners() {
    binding.ivHome.setPreventDoubleClick {
        navigateToHome()
    }
}

fun ZipperLockSaveSuccessFragment.onHotTrendingThemeClick(hotTrendingTheme: HotTrendingThemeModel) {
    // Navigate directly to EditLayoutFragment with selected theme
    navigateToEditLayoutWithTheme(hotTrendingTheme)
}

fun ZipperLockSaveSuccessFragment.observeHotTrendingThemes() {
    commonViewModel.hotTrendingUiState
        .map { it.hotTrendingThemes }
        .distinctUntilChanged()
        .onEach { hotTrendingThemes ->
            // Update with top 3 hot trending themes
            val top3Themes = hotTrendingThemes.take(3)
            hotTrendingAdapter.submitList(top3Themes)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ZipperLockSaveSuccessFragment.navigateToHome() {
    showLoadedInter(
        spaceNameConfig = "setsucess-home",
        spaceName = "setsucess-home_interstitial",
        destinationToShowAds = R.id.zipperLockSaveSuccessFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safePopBackStack(R.id.zipperLockSaveSuccessFragment, R.id.homeFragment)
        },
        onCloseAds = {},
    )
}

fun ZipperLockSaveSuccessFragment.navigateToEditLayoutWithTheme(theme: HotTrendingThemeModel) {
    // Apply theme to CommonViewModel first
    theme.zipper?.let {
        commonViewModel.setSelectedZipperResponse(it)
    }

    theme.wallpaper?.let {
        commonViewModel.setSelectedWallpaperImagePath(it)
    }

    theme.background?.let {
        commonViewModel.setSelectedBackgroundImagePath(it)
    }

    theme.sound?.let {
        commonViewModel.setSelectedSound(it)
    }

    theme.row?.let {
        commonViewModel.setSelectedRowResponse(it)
    }

    // Create bundle with theme data
    val bundle =
        Bundle().apply {
            theme.zipper?.let { putParcelable(BundleKey.ZIPPER_DATA, it) }
            theme.wallpaper?.let { putParcelable(BundleKey.WALLPAPER_DATA, it) }
            theme.background?.let { putParcelable(BundleKey.BACKGROUND_DATA, it) }
            theme.sound?.let { putParcelable(BundleKey.SOUND_DATA, it) }
            theme.row?.let { putParcelable(BundleKey.ROW_DATA, it) }
        }

    // Navigate to EditLayoutFragment with theme data
    findNavController().navigate(
        R.id.action_zipperLockSaveSuccessFragment_to_editLayoutFragment,
        bundle,
    )
}

fun ZipperLockSaveSuccessFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "setsucess-home",
        spaceNameAds = "setsucess-home_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "setsucess-bot",
        spaceName = "setsucess-bot_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )

    showLoadedNative(
        spaceNameConfig = "setsucess-midd",
        spaceName = "setsucess-midd_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {},
    )
}
