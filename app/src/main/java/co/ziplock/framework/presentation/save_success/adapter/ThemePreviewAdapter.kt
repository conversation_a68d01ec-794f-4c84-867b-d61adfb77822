package co.ziplock.framework.presentation.save_success.adapter

import android.view.View
import com.bumptech.glide.Glide
import co.ziplock.R
import co.ziplock.databinding.ItemThemePreviewBinding
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.ThemeData

class ThemePreviewAdapter(
    private val onThemeClick: (ThemeData) -> Unit
) : BaseListAdapter<ThemeData, ItemThemePreviewBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
    )
) {

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_theme_preview

    override fun bindView(binding: ItemThemePreviewBinding, item: ThemeData, position: Int) {
        binding.tvThemeName.text = item.name
        
        // Show/hide hot trending indicator
        binding.ivHotIndicator.visibility = if (item.isHotTrending) {
            View.VISIBLE
        } else {
            View.GONE
        }
        
        Glide.with(binding.ivThemePreview.context)
            .load(item.previewImageRes)
            .into(binding.ivThemePreview)
            
        // Set click listener
        binding.root.setOnClickListener {
            onThemeClick(item)
        }
    }
}