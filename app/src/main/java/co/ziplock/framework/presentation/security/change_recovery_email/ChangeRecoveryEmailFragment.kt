package co.ziplock.framework.presentation.security.change_recovery_email

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentChangeRecoveryEmailBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@AndroidEntryPoint
class ChangeRecoveryEmailFragment : BaseFragment<FragmentChangeRecoveryEmailBinding, ChangeRecoveryEmailViewModel>(
    FragmentChangeRecoveryEmailBinding::inflate,
    ChangeRecoveryEmailViewModel::class.java
) {
    @Inject
    lateinit var securityManager: SecurityManager

    var currentEmail: String? = null

    override fun init(view: View) {
        setupCurrentEmail()
        setupClickListeners()
        setupTextChangeListeners()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeRequestOtpState()
    }

    companion object {
        const val TAG = "ChangeRecoveryEmailFragment"
    }
}