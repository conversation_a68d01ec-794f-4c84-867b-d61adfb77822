package co.ziplock.framework.presentation.security.change_recovery_email

import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import co.ziplock.R
import co.ziplock.framework.presentation.common.SendOtpStatus
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.util.BundleKey
import co.ziplock.util.EmailUtils
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun ChangeRecoveryEmailFragment.setupCurrentEmail() {
    currentEmail = securityManager.getRecoveryEmail()
    binding.tvCurrentEmail.setText(EmailUtils.maskEmail(currentEmail))
    binding.tvCurrentEmail.isEnabled = false
}

fun ChangeRecoveryEmailFragment.setupClickListeners() {
    setupBackButtonClickListener()
    setupCancelButtonClickListener()
    setupUpdateButtonClickListener()
}

fun ChangeRecoveryEmailFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }

    onSystemBackEvent {
        backEvent()
    }
}

fun ChangeRecoveryEmailFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "email-back",
        spaceName = "pass-1ID_interstitial",
        destinationToShowAds = R.id.changeRecoveryEmailFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safeNavigateUp(R.id.changeRecoveryEmailFragment)
        },
        onCloseAds = {},
    )
}

fun ChangeRecoveryEmailFragment.setupCancelButtonClickListener() {
    binding.btnCancel.setPreventDoubleClick {
        findNavController().popBackStack()
    }
}

fun ChangeRecoveryEmailFragment.setupUpdateButtonClickListener() {
    binding.btnUpdate.setPreventDoubleClick {
        if (validateInputs()) {
            val newEmail =
                binding.etNewEmail.text
                    .toString()
                    .trim()
            commonViewModel.requestOtp(newEmail)
        }
    }
}

fun ChangeRecoveryEmailFragment.setupTextChangeListeners() {
    binding.apply {
        etNewEmail.doAfterTextChanged {
            tvError.isVisible = false
        }

        etConfirmEmail.doAfterTextChanged {
            tvError.isVisible = false
        }
    }
}

fun ChangeRecoveryEmailFragment.validateInputs(): Boolean {
    val newEmail =
        binding.etNewEmail.text
            .toString()
            .trim()
    val confirmEmail =
        binding.etConfirmEmail.text
            .toString()
            .trim()

    // Check if new email is valid
    if (!EmailUtils.validateEmail(newEmail) || !EmailUtils.validateEmail(confirmEmail)) {
        binding.tvError.isVisible = true
        binding.tvError.text = getString(R.string.invalid_email_format)
        return false
    }

    // Check if emails match
    if (newEmail != confirmEmail) {
        binding.tvError.isVisible = true
        binding.tvError.text = getString(R.string.emails_do_not_match)
        return false
    }

    // Check if new email is different from current
    if (newEmail == currentEmail) {
        binding.tvError.isVisible = true
        binding.tvError.text = getString(R.string.new_email_same_as_current)
        return false
    }

    return true
}

fun ChangeRecoveryEmailFragment.observeRequestOtpState() {
    commonViewModel.sendOtpStatus
        .onEach {
            Timber.d("sendOtpStatus $it")
            when (it) {
                SendOtpStatus.Limited -> {
                    displayToast(R.string.des_limit_request_otp)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                }

                SendOtpStatus.Error -> {
                    displayToast(R.string.something_error)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                }

                SendOtpStatus.None -> {
                    updateUiLoadingState(false)
                }

                SendOtpStatus.Standby -> {
                    updateUiLoadingState(true)
                }

                is SendOtpStatus.Success -> {
                    commonViewModel.setOtpCode(it.otp)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                    val newEmail =
                        binding.etNewEmail.text
                            .toString()
                            .trim()
                    showLoadedInter(
                        spaceNameConfig = "email-save",
                        spaceName = "pass-1ID_interstitial",
                        destinationToShowAds = R.id.changeRecoveryEmailFragment,
                        isShowLoadingView = true,
                        isScreenType = false,
                        navOrBack = {
                            safeNavInter(
                                R.id.changeRecoveryEmailFragment,
                                R.id.action_changeRecoveryEmailFragment_to_otpVerifyWhenSetupOrChangeEmailFragment,
                                bundleOf(
                                    BundleKey.KEY_CURRENT_EMAIL to newEmail,
                                ),
                            )
                        },
                        onCloseAds = {},
                    )
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ChangeRecoveryEmailFragment.updateUiLoadingState(isLoading: Boolean) {
    binding.btnUpdate.isEnabled = !isLoading
    binding.btnUpdate.alpha = if (!isLoading) 1f else 0.7f
    binding.progressBar.isVisible = isLoading
}

fun ChangeRecoveryEmailFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "email",
        spaceName = "email_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
    safePreloadAds(
        listSpaceNameConfig = listOf("email-save", "email-back"),
        spaceNameAds = "pass-1ID_interstitial",
    )
}
