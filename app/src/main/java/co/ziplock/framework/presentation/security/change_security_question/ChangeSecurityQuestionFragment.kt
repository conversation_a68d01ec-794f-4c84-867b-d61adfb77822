package co.ziplock.framework.presentation.security.change_security_question

import android.view.View
import co.ziplock.databinding.FragmentChangeSecurityQuestionBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ChangeSecurityQuestionFragment :
    BaseFragment<FragmentChangeSecurityQuestionBinding, ChangeSecurityQuestionViewModel>(
        FragmentChangeSecurityQuestionBinding::inflate,
        ChangeSecurityQuestionViewModel::class.java,
    ) {
    @Inject
    lateinit var securityManager: SecurityManager
    var failedAttempts = 0

    override fun init(view: View) {
        viewModel.loadSecurityQuestions(requireContext())
        setupSpinner()
        setupTextWatcher()
        setupClickListeners()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeAvailableQuestions()
        observeButtonState()
        observeErrorMessages()
        observeSuccessMessages()
    }

    companion object {
        const val TAG = "ChangeSecurityQuestionFragment"
        const val MAX_FAILED_ATTEMPTS = 5
    }
}
