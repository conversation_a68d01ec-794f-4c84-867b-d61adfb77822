package co.ziplock.framework.presentation.security.change_security_question

import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import co.ziplock.R
import co.ziplock.framework.presentation.common.launchMain
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.dialog.ForgotPasswordDialogFragment
import co.ziplock.framework.presentation.dialog.PatternVerificationDialogFragment
import co.ziplock.framework.presentation.dialog.PinVerificationDialogFragment
import co.ziplock.framework.presentation.home.tabs.settings.showPasswordRecoveryMethodDialog
import co.ziplock.framework.presentation.home.tabs.settings.showPatternVerificationDialog
import co.ziplock.framework.presentation.home.tabs.settings.showPinVerificationDialog
import co.ziplock.framework.presentation.manager.SecurityManager.SecurityType
import co.ziplock.util.BundleKey
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun ChangeSecurityQuestionFragment.setupSpinner() {
    // Setup current security question spinner
    binding.spinnerCurrentSecurityQuestion.onItemSelectedListener =
        object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                if (position > 0) { // Skip the "Select question" placeholder
                    val selectedQuestion = parent?.getItemAtPosition(position)?.toString() ?: ""
                    viewModel.setCurrentSecurityQuestion(selectedQuestion)
                } else {
                    viewModel.setCurrentSecurityQuestion("")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

    // Setup new security question spinner
    binding.spinnerNewSecurityQuestion.onItemSelectedListener =
        object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                if (position > 0) { // Skip the "Select question" placeholder
                    val selectedQuestion = parent?.getItemAtPosition(position)?.toString() ?: ""
                    viewModel.setNewSecurityQuestion(selectedQuestion)
                } else {
                    viewModel.setNewSecurityQuestion("")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
}

fun ChangeSecurityQuestionFragment.setupTextWatcher() {
    binding.etCurrentAnswer.addTextChangedListener(
        object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int,
            ) {
            }

            override fun onTextChanged(
                s: CharSequence?,
                start: Int,
                before: Int,
                count: Int,
            ) {
            }

            override fun afterTextChanged(s: Editable?) {
                val answer = s.toString().trim()
                viewModel.setCurrentSecurityAnswer(answer)
            }
        },
    )

    binding.etNewAnswer.addTextChangedListener(
        object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int,
            ) {
            }

            override fun onTextChanged(
                s: CharSequence?,
                start: Int,
                before: Int,
                count: Int,
            ) {
            }

            override fun afterTextChanged(s: Editable?) {
                val answer = s.toString().trim()
                viewModel.setNewSecurityAnswer(answer)
            }
        },
    )
}

fun ChangeSecurityQuestionFragment.observeAvailableQuestions() {
    viewModel.uiState
        .map { it.availableSecurityQuestions }
        .filter { it.isNotEmpty() }
        .distinctUntilChanged()
        .onEach { availableSecurityQuestions ->
            val questions = listOf(getString(R.string.select_question)) + availableSecurityQuestions

            // Create adapter for current security question spinner
            val currentAdapter =
                ArrayAdapter(
                    requireContext(),
                    android.R.layout.simple_spinner_item,
                    questions,
                ).apply {
                    setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
                }
            binding.spinnerCurrentSecurityQuestion.adapter = currentAdapter

            // Create adapter for new security question spinner
            val newAdapter =
                ArrayAdapter(
                    requireContext(),
                    android.R.layout.simple_spinner_item,
                    questions,
                ).apply {
                    setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
                }
            binding.spinnerNewSecurityQuestion.adapter = newAdapter

            // If we have a current security question, select it in the spinner
            val currentQuestion = viewModel.uiState.value.currentSecurityQuestion
            if (currentQuestion.isNotEmpty()) {
                val position = questions.indexOf(currentQuestion)
                if (position >= 0) {
                    binding.spinnerCurrentSecurityQuestion.setSelection(position)
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ChangeSecurityQuestionFragment.observeButtonState() {
    viewModel.uiState
        .map { it.isSaveEnabled to it.isLoading }
        .distinctUntilChanged()
        .onEach { (isSaveEnabled, isLoading) ->
            binding.btnSave.isEnabled = isSaveEnabled && !isLoading
            binding.btnSave.alpha = if (binding.btnSave.isEnabled) 1f else 0.5f
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ChangeSecurityQuestionFragment.observeErrorMessages() {
    // General error message
    viewModel.uiState
        .map { it.errorMessage }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            errorMessage?.let { error ->
                showError(error)
                viewModel.clearError()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    // Current answer error
    viewModel.uiState
        .map { it.currentAnswerError }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            binding.tvCurrentAnswerError.apply {
                text = errorMessage
                isVisible = !errorMessage.isNullOrEmpty()
                setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    // New answer error
    viewModel.uiState
        .map { it.newAnswerError }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            binding.tvNewAnswerError.apply {
                text = errorMessage
                isVisible = !errorMessage.isNullOrEmpty()
                setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ChangeSecurityQuestionFragment.observeSuccessMessages() {
    viewModel.uiState
        .map { it.successMessage }
        .distinctUntilChanged()
        .onEach { successMessage ->
            successMessage?.let { message ->
                // Show success message if needed
                viewModel.clearSuccess()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ChangeSecurityQuestionFragment.showError(message: String) {
    binding.tvError.text = message
    binding.tvError.isVisible = true
    binding.tvError.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
}

fun ChangeSecurityQuestionFragment.hideError() {
    binding.tvError.isVisible = false
}

fun ChangeSecurityQuestionFragment.setupClickListeners() {
    setupBackButtonClickListener()
    setupSaveButtonClickListener()
}

fun ChangeSecurityQuestionFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }

    onSystemBackEvent {
        backEvent()
    }
}

fun ChangeSecurityQuestionFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "question-back",
        spaceName = "pass-1ID_interstitial",
        destinationToShowAds = R.id.changeSecurityQuestionFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safeNavigateUp(R.id.changeSecurityQuestionFragment)
        },
        onCloseAds = {},
    )
}

fun ChangeSecurityQuestionFragment.setupSaveButtonClickListener() {
    binding.btnSave.setPreventDoubleClick {
        validateAndSave()
    }
}

fun ChangeSecurityQuestionFragment.validateAndSave() {
    if (!viewModel.validateAndShowErrors() || !viewModel.verifyCurrentAnswer()) {
        // If failed attempted 5 times, show dialog PIN/Pattern.
        if (failedAttempts >= ChangeSecurityQuestionFragment.MAX_FAILED_ATTEMPTS) {
            when (prefUtil.securityType) {
                SecurityType.PIN.name -> {
                    showPinVerificationDialog()
                }

                SecurityType.PATTERN.name -> {
                    showPatternVerificationDialog()
                }

                else -> {
                    // No security or unknown type, show password type selection
                }
            }
        } else {
            failedAttempts++
        }

        return
    }

    viewModel.saveSecurityQuestion {
        launchMain {
            showLoadedInter(
                spaceNameConfig = "question-save",
                spaceName = "pass-1ID_interstitial",
                destinationToShowAds = R.id.changeSecurityQuestionFragment,
                isShowLoadingView = true,
                isScreenType = false,
                navOrBack = {
                    displayToast(getString(R.string.changes_saved_successfully))
                    safePopBackStack(R.id.changeSecurityQuestionFragment, R.id.homeFragment)
                },
                onCloseAds = {},
            )
        }
    }
}

fun ChangeSecurityQuestionFragment.showPinVerificationDialog() {
    val dialogFragment = PinVerificationDialogFragment.newInstance()

    dialogFragment.onPinVerified = {
        // PIN verified successfully, show password type selection for changing password
        safeNav(
            R.id.changeSecurityQuestionFragment,
            R.id.action_changeSecurityQuestionFragment_to_securityQuestionSetupFragment,
            bundleOf(BundleKey.KEY_FROM_CHANGE_SECURITY_QUESTION to true),
        )
    }

    dialogFragment.onCancel = {
        // User cancelled, do nothing
    }

    dialogFragment.onForgotPassword = {
        // Show password recovery method dialog
        showPasswordRecoveryMethodDialog()
    }

    dialogFragment.show(parentFragmentManager, PinVerificationDialogFragment.TAG)
}

fun ChangeSecurityQuestionFragment.showPatternVerificationDialog() {
    val dialogFragment = PatternVerificationDialogFragment.newInstance()

    dialogFragment.onPatternVerified = {
        // Pattern verified successfully, show password type selection for changing password
        safeNav(
            R.id.changeSecurityQuestionFragment,
            R.id.action_changeSecurityQuestionFragment_to_securityQuestionSetupFragment,
            bundleOf(BundleKey.KEY_FROM_CHANGE_SECURITY_QUESTION to true),
        )
    }

    dialogFragment.onCancel = {
        // User cancelled, do nothing
    }

    dialogFragment.onForgotPassword = {
        // Show password recovery method dialog
        showPasswordRecoveryMethodDialog()
    }

    dialogFragment.show(parentFragmentManager, PatternVerificationDialogFragment.TAG)
}

fun ChangeSecurityQuestionFragment.showPasswordRecoveryMethodDialog() {
    ForgotPasswordDialogFragment.show(
        fragmentManager = parentFragmentManager,
        onSecurityQuestionSelected = {
            // Navigate to security question answer fragment
            safeNav(
                R.id.changeSecurityQuestionFragment,
                R.id.action_changeSecurityQuestionFragment_to_securityQuestionForgotPasswordFragment,
            )
        },
        onEmailRecoverySelected = {
            // Navigate to email password recovery fragment
            safeNav(
                R.id.changeSecurityQuestionFragment,
                R.id.action_changeSecurityQuestionFragment_to_emailPasswordRecoveryWhenForgotPasswordFragment,
            )
        },
        onCancel = {
            // User cancelled, do nothing
        },
    )
}

fun ChangeSecurityQuestionFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("question-save", "question-back"),
        spaceNameAds = "pass-1ID_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "question",
        spaceName = "question_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
