package co.ziplock.framework.presentation.security.change_security_question

import android.app.Application
import android.content.Context
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import co.ziplock.R
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@HiltViewModel
class ChangeSecurityQuestionViewModel @Inject constructor(
    private val application: Application,
    private val securityManager: SecurityManager
) : BaseViewModel() {

    data class ChangeSecurityQuestionUiState(
        val currentSecurityQuestion: String = "",
        val currentSecurityQuestionKey: String = "",
        val currentSecurityAnswer: String = "",
        val newSecurityQuestion: String = "",
        val newSecurityQuestionKey: String = "",
        val newSecurityAnswer: String = "",
        val isLoading: Boolean = false,
        val errorMessage: String? = null,
        val currentAnswerError: String? = null,
        val newAnswerError: String? = null,
        val successMessage: String? = null,
        val isSaveEnabled: Boolean = false,
        val availableSecurityQuestions: List<String> = emptyList(),
        val availableSecurityQuestionKeys: List<String> = emptyList(),
        val isCurrentAnswerCorrect: Boolean = true,
        val isCurrentQuestionCorrect: Boolean = true
    )

    private val _uiState = MutableStateFlow(ChangeSecurityQuestionUiState())
    val uiState: StateFlow<ChangeSecurityQuestionUiState> = _uiState.asStateFlow()

    fun loadSecurityQuestions(context: Context) {
        val questions = context.resources.getStringArray(R.array.security_questions).toList()
        val questionKeys = context.resources.getStringArray(R.array.security_question_keys).toList()
        _uiState.update { 
            it.copy(
                availableSecurityQuestions = questions,
                availableSecurityQuestionKeys = questionKeys
            ) 
        }
    }

    fun setCurrentSecurityQuestion(question: String) {
        val currentState = _uiState.value
        val questionIndex = currentState.availableSecurityQuestions.indexOf(question)
        val questionKey = if (questionIndex >= 0 && questionIndex < currentState.availableSecurityQuestionKeys.size) {
            currentState.availableSecurityQuestionKeys[questionIndex]
        } else {
            ""
        }
        
        _uiState.update { 
            val newState = it.copy(
                currentSecurityQuestion = question,
                currentSecurityQuestionKey = questionKey,
                currentAnswerError = null,
                isCurrentQuestionCorrect = true
            )
            newState.copy(isSaveEnabled = validateInputs(
                newState.currentSecurityQuestion,
                newState.currentSecurityAnswer, 
                newState.newSecurityQuestion, 
                newState.newSecurityAnswer
            ))
        }
    }

    fun setCurrentSecurityAnswer(answer: String) {
        _uiState.update { 
            val newState = it.copy(
                currentSecurityAnswer = answer.trim(),
                currentAnswerError = null,
                isCurrentAnswerCorrect = true
            )
            newState.copy(isSaveEnabled = validateInputs(
                newState.currentSecurityQuestion,
                newState.currentSecurityAnswer, 
                newState.newSecurityQuestion, 
                newState.newSecurityAnswer
            ))
        }
    }

    fun setNewSecurityQuestion(question: String) {
        val currentState = _uiState.value
        val questionIndex = currentState.availableSecurityQuestions.indexOf(question)
        val questionKey = if (questionIndex >= 0 && questionIndex < currentState.availableSecurityQuestionKeys.size) {
            currentState.availableSecurityQuestionKeys[questionIndex]
        } else {
            ""
        }
        
        _uiState.update { 
            val newState = it.copy(
                newSecurityQuestion = question,
                newSecurityQuestionKey = questionKey
            )
            newState.copy(isSaveEnabled = validateInputs(
                newState.currentSecurityQuestion,
                newState.currentSecurityAnswer, 
                newState.newSecurityQuestion, 
                newState.newSecurityAnswer
            ))
        }
    }

    fun setNewSecurityAnswer(answer: String) {
        _uiState.update { 
            val newState = it.copy(
                newSecurityAnswer = answer.trim(),
                newAnswerError = validateSecurityAnswer(answer.trim())
            )
            newState.copy(isSaveEnabled = validateInputs(
                newState.currentSecurityQuestion,
                newState.currentSecurityAnswer, 
                newState.newSecurityQuestion, 
                newState.newSecurityAnswer
            ))
        }
    }

    private fun validateInputs(currentQuestion: String, currentAnswer: String, newQuestion: String, newAnswer: String): Boolean {
        val isCurrentQuestionAnswerValid = currentQuestion.isNotEmpty()
                && currentAnswer.isNotEmpty()

        val isNewQuestionAnswerValid = newQuestion.isNotEmpty()
                && newAnswer.isNotEmpty()
                && newAnswer.length >= 3

        val isCurrentQuestionAnswerSameNewQuestionAnswer =
                currentQuestion == newQuestion
                        && currentAnswer == newAnswer

        return isNewQuestionAnswerValid && isCurrentQuestionAnswerValid && !isCurrentQuestionAnswerSameNewQuestionAnswer
    }

    fun validateSecurityAnswer(answer: String): String? {
        return when {
            answer.isBlank() -> null // Don't show error for empty field
            answer.length !in 3..256 -> application.getString(R.string.answer_must_be_between_3_and_256_characters)
            else -> null
        }
    }

    fun validateAndShowErrors(): Boolean {
        val currentState = _uiState.value
        
        // Validate current question
        if (currentState.currentSecurityQuestion.isEmpty()) {
            _uiState.update { it.copy(errorMessage = application.getString(R.string.please_select_a_security_question)) }
            return false
        }
        
        // Validate current answer
        if (currentState.currentSecurityAnswer.isEmpty()) {
            _uiState.update { it.copy(currentAnswerError = application.getString(R.string.please_answer_your_current_security_question)) }
            return false
        }
        
        // Validate new question
        if (currentState.newSecurityQuestion.isEmpty()) {
            _uiState.update { it.copy(errorMessage = application.getString(R.string.please_select_a_new_security_question)) }
            return false
        }
        
        // Validate new answer
        val newAnswerError = validateSecurityAnswer(currentState.newSecurityAnswer)
        if (newAnswerError != null) {
            _uiState.update { it.copy(newAnswerError = newAnswerError) }
            return false
        }
        
        return true
    }

    fun verifyCurrentAnswer(): Boolean {
        val currentState = _uiState.value
        val currentAnswer = currentState.currentSecurityAnswer
        val storedQuestionKey = securityManager.getSecurityQuestionKey()
        
        // First verify if the selected question is correct
        val isQuestionCorrect = storedQuestionKey == currentState.currentSecurityQuestionKey
        
        if (!isQuestionCorrect) {
            _uiState.update { 
                it.copy(
                    currentAnswerError = application.getString(R.string.incorrect_answer_try_again),
                    isCurrentQuestionCorrect = false
                )
            }
            return false
        }
        
        // Then verify the answer
        val isAnswerCorrect = securityManager.verifySecurityAnswer(currentAnswer)
        
        if (!isAnswerCorrect) {
            _uiState.update { 
                it.copy(
                    currentAnswerError = application.getString(R.string.incorrect_answer_try_again),
                    isCurrentQuestionCorrect = true,
                    isCurrentAnswerCorrect = false
                )
            }
        }
        
        return isAnswerCorrect
    }

    fun saveSecurityQuestion(onSuccess: () -> Unit) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val currentState = _uiState.value
                
                // Verify current question and answer
                if (!verifyCurrentAnswer()) {
                    _uiState.update { it.copy(isLoading = false) }
                    return@launch
                }
                
                // Validate new answer
                val answerError = validateSecurityAnswer(currentState.newSecurityAnswer)
                if (answerError != null) {
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            newAnswerError = answerError
                        )
                    }
                    return@launch
                }

                // Save new security question key and answer
                securityManager.setSecurityQuestionKey(currentState.newSecurityQuestionKey)
                securityManager.setSecurityAnswer(currentState.newSecurityAnswer)

                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        successMessage = application.getString(R.string.changes_saved_successfully)
                    )
                }

                onSuccess()
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        errorMessage = application.getString(R.string.failed_to_save_security_question, e.message)
                    )
                }
            }
        }
    }

    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }

    fun clearSuccess() {
        _uiState.update { it.copy(successMessage = null) }
    }
}