package co.ziplock.framework.presentation.security.email_otp_verify_when_forgot_password_fragment

import android.os.CountDownTimer
import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentEmailOtpVerifyWhenForgotPasswordBinding
import co.ziplock.framework.presentation.common.BaseFragment

@AndroidEntryPoint
class EmailOTPVerifyWhenForgotPasswordFragment :
    BaseFragment<FragmentEmailOtpVerifyWhenForgotPasswordBinding, EmailOTPVerifyWhenForgotPasswordViewModel>(
        FragmentEmailOtpVerifyWhenForgotPasswordBinding::inflate,
        EmailOTPVerifyWhenForgotPasswordViewModel::class.java
    ) {

    val verificationCodeEditTexts by lazy {
        with(binding) {
            listOf(etCode1, etCode2, etCode3, etCode4, etCode5, etCode6)
        }
    }

    var countDownTimer: CountDownTimer? = null
    var resendCountDownTimer: CountDownTimer? = null

    override fun init(view: View) {
        setUpTextInstructions()
        setupVerificationCodeTextWatchers()
        setupClickListeners()
        startOtpExpirationCountdown()
    }

    override fun subscribeObserver(view: View) {
        observeRequestOtpState()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // Cancel the countdown timers to prevent memory leaks
        countDownTimer?.cancel()
        countDownTimer = null
        resendCountDownTimer?.cancel()
        resendCountDownTimer = null
        commonViewModel.setOtpCode(null)
    }

    companion object {
        const val TAG = "EmailOTPVerifyWhenForgotPasswordFragment"
    }
}