package co.ziplock.framework.presentation.security.email_otp_verify_when_forgot_password_fragment

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@HiltViewModel
class EmailOTPVerifyWhenForgotPasswordViewModel @Inject constructor(
    private val securityManager: SecurityManager,
) : BaseViewModel() {

    fun getRecoveryEmail(): String? {
        return securityManager.getRecoveryEmail()
    }

    fun clearAllOldPasswordData() {
        securityManager.clearSecurity()
    }
}