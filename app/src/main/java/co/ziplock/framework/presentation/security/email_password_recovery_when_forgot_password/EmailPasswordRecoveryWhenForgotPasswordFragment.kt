package co.ziplock.framework.presentation.security.email_password_recovery_when_forgot_password

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentEmailPasswordRecoveryBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@AndroidEntryPoint
class EmailPasswordRecoveryWhenForgotPasswordFragment :
    BaseFragment<FragmentEmailPasswordRecoveryBinding, EmailPasswordRecoveryWhenForgotPasswordViewModel>(
        FragmentEmailPasswordRecoveryBinding::inflate,
        EmailPasswordRecoveryWhenForgotPasswordViewModel::class.java
    ) {
    @Inject
    lateinit var securityManager: SecurityManager

    override fun init(view: View) {
        setupTextWatcher()
        setupClickListeners()
    }

    override fun subscribeObserver(view: View) {
        observeRequestOtpState()
        observeErrorMessages()
    }

    companion object {
        const val TAG = "EmailPasswordRecoveryFragment"
    }
}