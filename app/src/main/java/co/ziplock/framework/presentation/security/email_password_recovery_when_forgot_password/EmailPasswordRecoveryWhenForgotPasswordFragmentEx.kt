package co.ziplock.framework.presentation.security.email_password_recovery_when_forgot_password

import android.text.Editable
import android.text.TextWatcher
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import co.ziplock.R
import co.ziplock.framework.presentation.common.SendOtpStatus
import co.ziplock.util.collectFlowOnView
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick

fun EmailPasswordRecoveryWhenForgotPasswordFragment.setupTextWatcher() {
    binding.etEmail.addTextChangedListener(object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        override fun afterTextChanged(s: Editable?) {
            val email = s.toString().trim()
            viewModel.setEmail(email)

            // Hide error when user starts typing
            if (email.isNotEmpty()) {
                hideError()
            }
        }
    })
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.setupClickListeners() {
    setupBackButtonClickListener()
    setupAnswerSecurityQuestionClickListener()
    setupContinueButtonClickListener()
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        findNavController().popBackStack()
    }
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.setupAnswerSecurityQuestionClickListener() {
    binding.tvAnswerSecurityQuestion.setPreventDoubleClick {
        //First navigate back to home fragment then navigate to answer security question fragment
        findNavController().popBackStack(R.id.homeFragment, false)

        //Navigate to AnswerSecurityQuestionFragment
        safeNav(
            R.id.homeFragment,
            R.id.action_homeFragment_to_securityQuestionForgotPasswordFragment
        )
    }
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.setupContinueButtonClickListener() {
    binding.btnContinue.setPreventDoubleClick {
        val email = binding.etEmail.text.toString().trim()
        // Send verification email
        viewModel.validateEmail(
            email,
            onSuccess = {
                commonViewModel.requestOtp(email)
            }
        )
    }
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.observeRequestOtpState() {
    commonViewModel.sendOtpStatus.collectFlowOnView(viewLifecycleOwner) {
        when (it) {
            SendOtpStatus.Limited -> {
                displayToast(R.string.des_limit_request_otp)
                updateUiLoadingState(false)
                commonViewModel.resetOtpRequestStatus()
            }

            SendOtpStatus.Error -> {
                displayToast(R.string.something_error)
                updateUiLoadingState(false)
                commonViewModel.resetOtpRequestStatus()
            }

            SendOtpStatus.None -> {
                updateUiLoadingState(false)
            }

            SendOtpStatus.Standby -> {
                updateUiLoadingState(true)
            }

            is SendOtpStatus.Success -> {
                commonViewModel.setOtpCode(it.otp)
                updateUiLoadingState(false)
                commonViewModel.resetOtpRequestStatus()
                // Navigate to the CheckEmailFragment
                safeNav(
                    R.id.emailPasswordRecoveryWhenForgotPasswordFragment,
                    R.id.action_emailPasswordRecoveryWhenForgotPasswordFragment_to_emailOTPVerifyWhenForgotPasswordFragment
                )
            }
        }
    }
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.updateUiLoadingState(isLoading: Boolean) {
    binding.btnContinue.isEnabled = !isLoading
    binding.btnContinue.alpha = if (!isLoading) 1f else 0.7f
    binding.progressBar.isVisible = isLoading
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.observeErrorMessages() {
    viewModel.uiState
        .map { it.errorMessage }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            errorMessage?.let { error ->
                showError(error)
                viewModel.clearError()
            }
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.showError(message: String) {
    binding.tvError.text = message
    binding.tvError.isVisible = true
}

fun EmailPasswordRecoveryWhenForgotPasswordFragment.hideError() {
    binding.tvError.isVisible = false
}