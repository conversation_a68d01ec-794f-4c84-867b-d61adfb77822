package co.ziplock.framework.presentation.security.email_password_recovery_when_forgot_password

import android.app.Application
import android.util.Patterns
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import co.ziplock.R
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.EmailUtils
import javax.inject.Inject

@HiltViewModel
class EmailPasswordRecoveryWhenForgotPasswordViewModel @Inject constructor(
    private val application: Application,
    private val securityManager: SecurityManager
) : BaseViewModel() {

    data class EmailRecoveryUiState(
        val email: String = "",
        val errorMessage: String? = null,
        val isEmailValid: Boolean = false,
    )

    private val _uiState = MutableStateFlow(EmailRecoveryUiState())
    val uiState: StateFlow<EmailRecoveryUiState> = _uiState.asStateFlow()

    fun setEmail(email: String) {
        val isValid = EmailUtils.validateEmail(email)
        _uiState.update { 
            it.copy(
                email = email,
                isEmailValid = isValid,
                errorMessage = null
            )
        }
    }

    fun validateEmail(email: String, onSuccess: () -> Unit) {
        if (!EmailUtils.validateEmail(email)) {
            _uiState.update { it.copy(errorMessage = application.getString(R.string.that_doesn_t_look_like_a_valid_email_address_please_check_the_format)) }
            return
        }

        // Check if email exists in the system
        if (!securityManager.hasRecoveryEmail() || email != securityManager.getRecoveryEmail()) {
            _uiState.update {
                it.copy(
                    errorMessage = application.getString(R.string.this_email_address_is_not_registered_in_our_system_please_double_check_or_try_a_different_email)
                )
            }
            return
        }

        onSuccess()
    }

    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
}