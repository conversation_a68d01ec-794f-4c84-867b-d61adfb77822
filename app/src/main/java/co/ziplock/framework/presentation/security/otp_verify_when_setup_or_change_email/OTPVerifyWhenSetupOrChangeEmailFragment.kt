package co.ziplock.framework.presentation.security.otp_verify_when_setup_or_change_email

import android.os.CountDownTimer
import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentEmailOtpVerifyWhenSetupOrChangeEmailBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.util.BundleKey

@AndroidEntryPoint
class OTPVerifyWhenSetupOrChangeEmailFragment :
    BaseFragment<FragmentEmailOtpVerifyWhenSetupOrChangeEmailBinding, OTPVerifyWhenSetupOrChangeEmailViewModel>(
        FragmentEmailOtpVerifyWhenSetupOrChangeEmailBinding::inflate,
        OTPVerifyWhenSetupOrChangeEmailViewModel::class.java
    ) {

    var countDownTimer: CountDownTimer? = null
    var resendCountDownTimer: CountDownTimer? = null

    val currentEmail: String? by lazy {
        arguments?.getString(BundleKey.KEY_CURRENT_EMAIL)
    }

    val isFromFirstSetupPasswordFlow: Boolean by lazy {
        arguments?.getBoolean(BundleKey.KEY_FIRST_SETUP_PASSWORD) == true
    }

    val verificationCodeEditTexts by lazy {
        with(binding) {
            listOf(etCode1, etCode2, etCode3, etCode4, etCode5, etCode6)
        }
    }

    override fun init(view: View) {
        setUpTextInstructions()
        setupVerificationCodeTextWatchers()
        setupClickListeners()
        startOtpExpirationCountdown()
    }

    override fun subscribeObserver(view: View) {
        observeRequestOtpState()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // Cancel the countdown timers to prevent memory leaks
        countDownTimer?.cancel()
        countDownTimer = null
        resendCountDownTimer?.cancel()
        resendCountDownTimer = null
        commonViewModel.setOtpCode(null)
    }

    companion object {
        const val TAG = "EmailOTPVerifyWhenForgotPasswordFragment"
    }
}