package co.ziplock.framework.presentation.security.otp_verify_when_setup_or_change_email

import android.content.Intent
import android.os.CountDownTimer
import androidx.core.text.HtmlCompat
import androidx.core.text.bold
import androidx.core.text.buildSpannedString
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import co.ziplock.R
import co.ziplock.framework.presentation.common.SendOtpStatus
import co.ziplock.util.Constant
import co.ziplock.util.EmailUtils
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import timber.log.Timber

fun OTPVerifyWhenSetupOrChangeEmailFragment.setupVerificationCodeTextWatchers() {
    // Set up text watchers for verification code fields
    for (i in verificationCodeEditTexts.indices) {
        val currentEditText = verificationCodeEditTexts[i]

        currentEditText.doAfterTextChanged { s ->
            // If text is entered, move to next field
            if (s?.length == 1 && i < verificationCodeEditTexts.size - 1) {
                verificationCodeEditTexts[i + 1].requestFocus()
            } else if (s?.isEmpty() == true && i > 0) {
                // If backspace is pressed and field is empty, move to previous field
                verificationCodeEditTexts[i - 1].requestFocus()
            }

            // Hide error when user starts typing
            hideError()
        }

        currentEditText.setOnKeyListener { _, keyCode, event ->
            if (keyCode == android.view.KeyEvent.KEYCODE_DEL && currentEditText.text.isEmpty() && i > 0) {
                verificationCodeEditTexts[i - 1].requestFocus()
                true
            } else {
                false
            }
        }
    }

    // Set focus to first field
    verificationCodeEditTexts.firstOrNull()?.requestFocus()
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.observeRequestOtpState() {
    commonViewModel.sendOtpStatus
        .onEach {
            Timber.d("observeRequestOtpState: $it")
            when (it) {
                SendOtpStatus.Limited -> {
                    displayToast(R.string.des_limit_request_otp)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                }

                SendOtpStatus.Error -> {
                    displayToast(R.string.something_error)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                }

                SendOtpStatus.None -> {
                    updateUiLoadingState(false)
                }

                SendOtpStatus.Standby -> {
                    updateUiLoadingState(true)
                }

                is SendOtpStatus.Success -> {
                    commonViewModel.setOtpCode(it.otp)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                    displayToast(getString(R.string.otp_sent_successfully))
                    // Restart the OTP expiration countdown when a new OTP is sent
                    startOtpExpirationCountdown()
                }
            }
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.setupClickListeners() {
    setupBackButtonClickListener()
    setupOpenEmailAppClickListener()
    setupResendEmailClickListener()
    setupVerifyCodeButtonClickListener()
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        findNavController().popBackStack()
    }
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.setupOpenEmailAppClickListener() {
    binding.tvOpenEmailApp.setPreventDoubleClick {
        openEmailApp()
    }
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.setupResendEmailClickListener() {
    binding.tvResendEmail.setPreventDoubleClick {
        if (binding.tvResendEmail.isEnabled) {
            commonViewModel.setOtpCode(null)
            commonViewModel.requestOtp(currentEmail.toString())
            // Start resend countdown after clicking resend
            startResendCountdown()
        }
    }
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.setupVerifyCodeButtonClickListener() {
    binding.btnVerifyCode.setPreventDoubleClick {
        val code = verificationCodeEditTexts.joinToString("") { it.text.toString() }
        commonViewModel.submitOtpCode(
            code = code,
            onSuccess = {
                // Save the updated email address
                viewModel.saveRecoveryEmail(currentEmail.toString())

                // Password recovery successful, navigate back to home
                if (isFromFirstSetupPasswordFlow) {
                    displayToast(getString(R.string.your_password_has_been_set))
                } else {
                    displayToast(getString(R.string.recovery_email_has_been_updated))
                }

                findNavController().popBackStack(R.id.homeFragment, false)
            },
            onFailure = {
                showError(getString(R.string.your_security_code_is_incorrect_try_again))
            },
        )
    }
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.startOtpExpirationCountdown() {
    // Cancel any existing timer
    countDownTimer?.cancel()

    countDownTimer = object : CountDownTimer(Constant.TIMEOUT_OTP, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            val minutes = millisUntilFinished / 1000 / 60
            val seconds = millisUntilFinished / 1000 % 60
            val timeFormatted = String.format("%02d:%02d", minutes, seconds)
            binding.tvCountdown.text = getString(R.string.otp_expires_in, timeFormatted)
        }

        override fun onFinish() {
            binding.tvCountdown.text = getString(R.string.otp_expired)
            // Enable resend button when OTP expires (only if not in resend cooldown)
            if (resendCountDownTimer == null) {
                enableResendEmailButton()
            }
            // Optionally show a toast or notification that OTP has expired
            displayToast(getString(R.string.otp_expired))
            commonViewModel.setOtpCode(null)
        }
    }.start()
    
    // Start resend countdown when OTP is first sent
    startResendCountdown()
}


fun OTPVerifyWhenSetupOrChangeEmailFragment.updateUiLoadingState(isLoading: Boolean) {
    binding.btnVerifyCode.isEnabled = !isLoading
    binding.btnVerifyCode.alpha = if (!isLoading) 1f else 0.7f
    binding.progressBar.isVisible = isLoading
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.disableResendEmailButton() {
    binding.tvResendEmail.isEnabled = false
    binding.tvResendEmail.alpha = 0.7f
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.enableResendEmailButton() {
    binding.tvResendEmail.isEnabled = true
    binding.tvResendEmail.alpha = 1f
    binding.tvResendEmail.text = getString(R.string.resend_email)
}


fun OTPVerifyWhenSetupOrChangeEmailFragment.showError(message: String) {
    binding.tvError.text = message
    binding.tvError.isVisible = true
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.hideError() {
    binding.tvError.isVisible = false
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.setUpTextInstructions() {
    binding.tvInstruction.text = buildSpannedString {
        append(getString(R.string.password_recovery_email_sent_prefix))
        bold {
            append(EmailUtils.maskEmail(currentEmail))
            append(".")
            append("\n")
            append(getString(R.string.password_recovery_email_sent_suffix))
        }
    }
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.startResendCountdown() {
    // Cancel any existing resend timer
    resendCountDownTimer?.cancel()

    // Disable resend button and start countdown
    binding.tvResendEmail.isEnabled = false
    binding.tvResendEmail.alpha = 0.7f
    
    resendCountDownTimer = object : CountDownTimer(Constant.TIME_RESEND_OTP, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            val seconds = millisUntilFinished / 1000
            binding.tvResendEmail.text = getString(R.string.resend_in_seconds, seconds)
        }
        
        override fun onFinish() {
            // Enable resend button after countdown
            enableResendEmailButton()
            resendCountDownTimer = null
        }
    }.start()
}

fun OTPVerifyWhenSetupOrChangeEmailFragment.openEmailApp() {
    try {
        val intent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_APP_EMAIL)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        startActivity(intent)
    } catch (e: Exception) {
        // If no email app found, try to open the default email chooser
        try {
            val emailIntent = Intent(Intent.ACTION_SEND).apply {
                type = "message/rfc822"
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            val chooser = Intent.createChooser(emailIntent, getString(R.string.open_email_app))
            chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(chooser)
        } catch (ex: Exception) {
            displayToast(getString(R.string.no_email_app_found))
        }
    }
}