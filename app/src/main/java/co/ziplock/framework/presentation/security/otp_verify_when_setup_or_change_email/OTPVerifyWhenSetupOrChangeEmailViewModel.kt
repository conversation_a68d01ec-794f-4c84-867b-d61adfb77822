package co.ziplock.framework.presentation.security.otp_verify_when_setup_or_change_email

import dagger.hilt.android.lifecycle.HiltViewModel
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@HiltViewModel
class OTPVerifyWhenSetupOrChangeEmailViewModel @Inject constructor(
    private val securityManager: SecurityManager,
) : BaseViewModel() {
    fun saveRecoveryEmail(email: String) {
        securityManager.setRecoveryEmail(email)
    }
}