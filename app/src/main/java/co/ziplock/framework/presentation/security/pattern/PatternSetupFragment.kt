package co.ziplock.framework.presentation.security.pattern

import android.view.View
import co.ziplock.databinding.FragmentPatternSetupBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.util.BundleKey
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job

@AndroidEntryPoint
class PatternSetupFragment :
    BaseFragment<FragmentPatternSetupBinding, PatternSetupViewModel>(
        FragmentPatternSetupBinding::inflate,
        PatternSetupViewModel::class.java,
    ) {
    val isFirstPasswordSetupFlow: Boolean by lazy {
        arguments?.getBoolean(
            BundleKey.KEY_FIRST_SETUP_PASSWORD,
            false,
        ) == true
    }

    var isConfirmStep = false
    var currentPattern: List<Int> = emptyList()
    var errorClearJob: Job? = null

    var isShowedReloadAds = false

    override fun init(view: View) {
        setupPatternLockView()
        setupClickListeners()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeSetupStep()
        observePatternMatchStatus()
        observeErrorMessages()
        observeButtonStates()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isShowedReloadAds = false
    }

    override fun onDestroy() {
        super.onDestroy()
        errorClearJob?.cancel()
    }

    companion object {
        const val TAG = "PatternSetupFragment"
    }
}
