package co.ziplock.framework.presentation.security.pattern

import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import co.ziplock.R
import co.ziplock.util.BundleKey
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import com.andrognito.patternlockview.PatternLockView
import com.andrognito.patternlockview.listener.PatternLockViewListener
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun PatternSetupFragment.setupPatternLockView() {
    binding.patternLockView.apply {
        addPatternLockListener(
            object : PatternLockViewListener {
                override fun onStarted() {
                    clearErrorState()
                }

                override fun onProgress(progressPattern: MutableList<PatternLockView.Dot>?) {
                    progressPattern?.let { dots ->
                        val patternIndices = dots.map { it.id }
                        currentPattern = patternIndices
                        viewModel.updatePatternValidation(patternIndices)
                    }
                }

                override fun onComplete(pattern: MutableList<PatternLockView.Dot>?) {
                    pattern?.let { dots ->
                        val patternIndices = dots.map { it.id }
                        currentPattern = patternIndices
                        handlePatternComplete(patternIndices)
                    }
                }

                override fun onCleared() {
                    currentPattern = emptyList()
                    viewModel.updatePatternValidation(emptyList())
                }
            },
        )
    }
}

fun PatternSetupFragment.setupClickListeners() {
    setupBackButtonClickListener()
    setupCancelButtonClickListener()
    setupNextButtonClickListener()
}

fun PatternSetupFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        if (isConfirmStep) {
            // Go back to first input step
            viewModel.goBackToFirstInput()
        } else {
            backEvent()
        }
    }
}

fun PatternSetupFragment.setupCancelButtonClickListener() {
    binding.btnCancel.setPreventDoubleClick {
        backEvent()
    }
}

fun PatternSetupFragment.setupNextButtonClickListener() {
    binding.btnNext.setPreventDoubleClick {
        handleNextButtonClick()
    }
}

fun PatternSetupFragment.backEvent() {
    findNavController().popBackStack(R.id.homeFragment, false)
}

fun PatternSetupFragment.observeSetupStep() {
    viewModel.uiState
        .map { it.currentStep }
        .distinctUntilChanged()
        .onEach { step ->
            when (step) {
                PatternSetupViewModel.SetupStep.FIRST_INPUT -> {
                    handleFirstInputStep()
                }

                PatternSetupViewModel.SetupStep.CONFIRM_INPUT -> {
                    handleConfirmInputStep()
                    showReloadAds()
                }

                PatternSetupViewModel.SetupStep.COMPLETED_WITHOUT_SAVING_FOR_FIRST_SETUP_PASSWORD_FLOW -> {
                    showInterApplyPattern {
                        safeNavInter(
                            R.id.patternSetupFragment,
                            R.id.action_patternSetupFragment_to_securityQuestionSetupFragment,
                            bundleOf(
                                BundleKey.KEY_PATTERN_CODE to viewModel.uiState.value.firstPattern,
                            ),
                        )
                    }
                }

                PatternSetupViewModel.SetupStep.COMPLETED_WITH_SAVING_FOR_PREVIOUSLY_SETUP_PASSWORD_FLOW -> {
                    // Navigate back to Home and show success message
                    showInterApplyPattern {
                        displayToast(getString(R.string.your_password_has_been_set))
                        safePopBackStack(R.id.patternSetupFragment, R.id.homeFragment)
                    }
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PatternSetupFragment.showInterApplyPattern(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "pattern-apply",
        spaceName = "pass-1ID_interstitial",
        destinationToShowAds = R.id.patternSetupFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun PatternSetupFragment.observePatternMatchStatus() {
    viewModel.uiState
        .map { it.isPatternMatched }
        .distinctUntilChanged()
        .onEach { isMatched ->
            if (isMatched) {
                setPatternSuccess()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PatternSetupFragment.observeErrorMessages() {
    viewModel.uiState
        .map { it.errorMessage }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            errorMessage?.let { error ->
                // Cancel any existing error clear timer
                errorClearJob?.cancel()
                showError(error)
                viewModel.clearError()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PatternSetupFragment.observeButtonStates() {
    viewModel.uiState
        .map { Pair(it.isNextEnabled, it.isApplyEnabled) }
        .distinctUntilChanged()
        .onEach { _ ->
            updateButtonStates(viewModel.uiState.value)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PatternSetupFragment.handleFirstInputStep() {
    isConfirmStep = false
    updateUI()
    clearPattern()
}

fun PatternSetupFragment.handleConfirmInputStep() {
    if (!isConfirmStep) {
        isConfirmStep = true
        updateUI()
        clearPattern()
    }
}

fun PatternSetupFragment.handlePatternComplete(pattern: List<Int>) {
    if (pattern.size < 4) {
        // Count as failed attempt if in confirm step
        if (isConfirmStep) {
            viewModel.handleInvalidPatternAttempt()
        } else {
            showError(getString(R.string.connect_at_least_4_dots))
            // Set pattern to error state but don't clear it immediately
            binding.patternLockView.setViewMode(PatternLockView.PatternViewMode.WRONG)
        }
        return
    }

    // Check if pattern matches previous one in case of confirmation
    if (isConfirmStep) {
        // Confirm input
        viewModel.confirmPatternInput(pattern)
    }
}

fun PatternSetupFragment.handleNextButtonClick() {
    if (currentPattern.size >= 4) {
        if (!isConfirmStep) {
            // First input - move to confirm step
            viewModel.setFirstPatternInput(currentPattern)
        } else {
            if (isFirstPasswordSetupFlow) {
                viewModel.goToSecurityQuestionScreenWithoutSaving()
            } else {
                // Apply - save pattern code
                viewModel.savePatternCode()
            }
        }
    }
}

fun PatternSetupFragment.updateUI() {
    binding.apply {
        if (isConfirmStep) {
            tvSubtitle.text = getString(R.string.re_draw_your_pattern)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
            btnNext.text = getString(R.string.apply)
        } else {
            tvSubtitle.text = getString(R.string.set_your_new_pattern)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
            tvHelperText.text = getString(R.string.connect_at_least_4_dots)
            tvHelperText.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.black_35496d,
                ),
            )
            btnNext.text = getString(R.string.next)
        }
    }

    updateButtonStates(viewModel.uiState.value)
}

fun PatternSetupFragment.updateButtonStates(state: PatternSetupViewModel.PatternSetupUiState) {
    binding.apply {
        btnNext.isEnabled =
            if (isConfirmStep) {
                state.isApplyEnabled
            } else {
                state.isNextEnabled
            }
        btnNext.alpha = if (btnNext.isEnabled) 1f else 0.2f
    }
}

fun PatternSetupFragment.showError(message: String) {
    binding.tvHelperText.text = message
    binding.tvHelperText.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))

    // Set pattern view to error state only for confirm step errors or timeout
    if (isConfirmStep) {
        binding.patternLockView.setViewMode(PatternLockView.PatternViewMode.WRONG)
    }

    // Auto clear error after 2 seconds for non-timeout errors
    startErrorClearTimer()
}

fun PatternSetupFragment.startErrorClearTimer() {
    // Cancel any existing timer
    errorClearJob?.cancel()

    errorClearJob =
        viewLifecycleOwner.lifecycleScope.launch {
            delay(2000) // Wait 2 seconds
            clearPattern()
            clearErrorState()
        }
}

fun PatternSetupFragment.setPatternSuccess() {
    // Set pattern view to correct state
    binding.patternLockView.setViewMode(PatternLockView.PatternViewMode.CORRECT)

    // Enable Apply button
    binding.btnNext.isEnabled = true
    binding.btnNext.alpha = 1f
}

fun PatternSetupFragment.clearPattern() {
    binding.patternLockView.clearPattern()
    currentPattern = emptyList()
    updateButtonStates(viewModel.uiState.value)
}

fun PatternSetupFragment.clearErrorState() {
    // Cancel any existing error clear timer
    errorClearJob?.cancel()

    binding.apply {
        // Reset subtitle and helper text to normal state
        if (isConfirmStep) {
            tvSubtitle.text = getString(R.string.re_draw_your_pattern)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        } else {
            tvSubtitle.text = getString(R.string.set_your_new_pattern)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        }

        tvHelperText.text = getString(R.string.connect_at_least_4_dots)
        tvHelperText.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
    }
}

fun PatternSetupFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "pattern-draw",
        spaceName = "pattern-draw_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}

fun PatternSetupFragment.showReloadAds() {
    if (!isShowedReloadAds) {
        isShowedReloadAds = true
        showLoadedNative(
            spaceNameConfig = "pattern-reDraw",
            spaceName = "pattern-reDraw_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
}
