package co.ziplock.framework.presentation.security.pattern

import android.app.Application
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import co.ziplock.R
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.PrefUtil
import javax.inject.Inject

@HiltViewModel
class PatternSetupViewModel @Inject constructor(
    private val application: Application,
    private val prefUtil: PrefUtil,
    private val securityManager: SecurityManager
) : BaseViewModel() {

    enum class SetupStep {
        FIRST_INPUT,
        CONFIRM_INPUT,
        COMPLETED_WITHOUT_SAVING_FOR_FIRST_SETUP_PASSWORD_FLOW,
        COMPLETED_WITH_SAVING_FOR_PREVIOUSLY_SETUP_PASSWORD_FLOW,
    }

    data class PatternSetupUiState(
        val currentStep: SetupStep = SetupStep.FIRST_INPUT,
        val firstPattern: List<Int> = emptyList(),
        val isLoading: Boolean = false,
        val errorMessage: String? = null,
        val successMessage: String? = null,
        val isFirstSetup: Boolean = true,
        val isNextEnabled: Boolean = false,
        val isApplyEnabled: Boolean = false,
        val isPatternMatched: Boolean = false
    )

    private val _uiState = MutableStateFlow(PatternSetupUiState())
    val uiState: StateFlow<PatternSetupUiState> = _uiState.asStateFlow()

    init {
        checkIfFirstSetup()
    }

    private fun checkIfFirstSetup() {
        val hasExistingPassword = securityManager.hasSecurity()
        _uiState.update { it.copy(isFirstSetup = !hasExistingPassword) }
    }

    fun setFirstPatternInput(pattern: List<Int>) {
        _uiState.update { 
            it.copy(
                firstPattern = pattern,
                currentStep = SetupStep.CONFIRM_INPUT,
                errorMessage = null,
                isNextEnabled = false,
                isApplyEnabled = false,
                isPatternMatched = false
            )
        }
    }

    fun confirmPatternInput(confirmPattern: List<Int>) {
        val currentState = _uiState.value
        
        if (currentState.firstPattern == confirmPattern) {
            // Pattern matches
            _uiState.update { 
                it.copy(
                    isPatternMatched = true,
                    isApplyEnabled = true,
                    errorMessage = null
                )
            }
        } else {
            // Pattern doesn't match
            _uiState.update { 
                it.copy(
                    errorMessage = "Incorrect Pattern. Please try again",
                    isApplyEnabled = false,
                    isPatternMatched = false
                )
            }
        }
    }



    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }

    fun clearSuccess() {
        _uiState.update { it.copy(successMessage = null) }
    }

    fun resetSetup() {
        _uiState.update { 
            PatternSetupUiState(
                isFirstSetup = _uiState.value.isFirstSetup
            )
        }
    }

    fun goBackToFirstInput() {
        _uiState.update { 
            it.copy(
                currentStep = SetupStep.FIRST_INPUT,
                firstPattern = emptyList(),
                errorMessage = null,
                isNextEnabled = false,
                isApplyEnabled = false,
                isPatternMatched = false
            )
        }
    }
    
    fun updatePatternValidation(pattern: List<Int>) {
        val isValid = pattern.size >= 4
        _uiState.update { 
            it.copy(
                isNextEnabled = isValid && it.currentStep == SetupStep.FIRST_INPUT
            )
        }
    }
    
    fun savePatternCode() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val currentState = _uiState.value
                
                // Setup pattern code (clears all existing security and sets new pattern)
                securityManager.setupPatternCode(currentState.firstPattern)

                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        currentStep = SetupStep.COMPLETED_WITH_SAVING_FOR_PREVIOUSLY_SETUP_PASSWORD_FLOW,
                        successMessage = application.getString(R.string.pattern_setup_completed_successfully)
                    )
                }
                
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        errorMessage = application.getString(R.string.failed_to_setup_pattern, e.message)
                    )
                }
            }
        }
    }

    fun goToSecurityQuestionScreenWithoutSaving() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }

            try {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        currentStep = SetupStep.COMPLETED_WITHOUT_SAVING_FOR_FIRST_SETUP_PASSWORD_FLOW,
                        successMessage = application.getString(R.string.pattern_setup_completed_successfully)
                    )
                }

            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = application.getString(R.string.failed_to_setup_pattern, e.message)
                    )
                }
            }
        }
    }
    
    fun handleInvalidPatternAttempt() {
        _uiState.update { 
            it.copy(
                errorMessage = "Connect at least 4 dots",
                isApplyEnabled = false,
                isPatternMatched = false
            )
        }
    }

}