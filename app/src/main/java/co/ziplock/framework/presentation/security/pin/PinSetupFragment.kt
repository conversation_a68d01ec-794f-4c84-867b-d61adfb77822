package co.ziplock.framework.presentation.security.pin

import android.view.View
import android.widget.EditText
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import co.ziplock.databinding.FragmentPinSetupBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.util.BundleKey

@AndroidEntryPoint
class PinSetupFragment : BaseFragment<FragmentPinSetupBinding, PinSetupViewModel>(
    FragmentPinSetupBinding::inflate,
    PinSetupViewModel::class.java
) {
    val isFirstPasswordSetupFlow: Boolean by lazy {
        arguments?.getBoolean(
            BundleKey.KEY_FIRST_SETUP_PASSWORD,
            false
        ) == true
    }
    val pinViewModel: PinSetupViewModel by viewModels()
    var isConfirmStep = false
    var currentPin = ""
    lateinit var pinInputs: List<EditText>
    var isPinVisible = false
    var errorClearJob: Job? = null

    var isShowedReloadAds = false

    override fun init(view: View) {
        setupPinInputs()
        setupClickListeners()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeSetupStep()
        observePinMatchStatus()
        observeErrorMessages()
        observeButtonState()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isShowedReloadAds = false
    }

    override fun onDestroy() {
        super.onDestroy()
        errorClearJob?.cancel()
    }

    companion object {
        const val TAG = "PinSetupFragment"
    }
}