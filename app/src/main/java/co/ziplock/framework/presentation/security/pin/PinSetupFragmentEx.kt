package co.ziplock.framework.presentation.security.pin

import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import co.ziplock.R
import co.ziplock.util.BundleKey
import co.ziplock.util.displayToast
import co.ziplock.util.edittext.AsteriskPasswordTransformationMethod
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun PinSetupFragment.setupPinInputs() {
    pinInputs =
        listOf(
            binding.etPin1,
            binding.etPin2,
            binding.etPin3,
            binding.etPin4,
        )

    pinInputs.forEach { editText ->
        editText.transformationMethod = AsteriskPasswordTransformationMethod()
    }

    pinInputs.forEachIndexed { index, editText ->
        editText.addTextChangedListener(
            object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int,
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    val text = s.toString()

                    // Only allow digits
                    if (text.isNotEmpty() && !text.matches(Regex("\\d"))) {
                        editText.setText("")
                        return
                    }

                    // Move to next input if current is filled
                    if (text.isNotEmpty() && index < pinInputs.size - 1) {
                        pinInputs[index + 1].requestFocus()
                    }

                    // Update current PIN
                    updateCurrentPin()

                    // Check PIN immediately when confirm step and PIN is complete
                    if (isConfirmStep && currentPin.length == 4) {
                        pinViewModel.confirmPinInput(currentPin)
                    } else {
                        updateButtonState()
                        updateUI()
                    }
                }
            },
        )

        // Handle backspace to move to previous input
        editText.setOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_DEL && editText.text.isEmpty() && index > 0) {
                pinInputs[index - 1].requestFocus()
                true
            } else {
                false
            }
        }
    }
}

fun PinSetupFragment.setupClickListeners() {
    setupBackButtonClickListener()
    setupCancelButtonClickListener()
    setupNextButtonClickListener()
    setupTogglePinVisibilityButtonClickListener()
}

fun PinSetupFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        if (isConfirmStep) {
            // Go back to first input step
            pinViewModel.goBackToFirstInput()
        } else {
            // Go back to home fragment
            findNavController().popBackStack(R.id.homeFragment, false)
        }
    }
}

fun PinSetupFragment.setupCancelButtonClickListener() {
    binding.btnCancel.setPreventDoubleClick {
        // Go back to home fragment
        findNavController().popBackStack(R.id.homeFragment, false)
    }
}

fun PinSetupFragment.setupNextButtonClickListener() {
    binding.btnNext.setPreventDoubleClick {
        handleNextButtonClick()
    }
}

fun PinSetupFragment.setupTogglePinVisibilityButtonClickListener() {
    binding.btnTogglePinVisibility.setPreventDoubleClick {
        togglePinVisibility()
    }
}

fun PinSetupFragment.observeSetupStep() {
    pinViewModel.uiState
        .map { it.currentStep }
        .distinctUntilChanged()
        .onEach { step ->
            when (step) {
                PinSetupViewModel.SetupStep.FIRST_INPUT -> {
                    isConfirmStep = false
                    updateUI()
                    clearPinInputs()
                }

                PinSetupViewModel.SetupStep.CONFIRM_INPUT -> {
                    if (!isConfirmStep) {
                        isConfirmStep = true
                        updateUI()
                        clearPinInputs()
                    }
                }

                PinSetupViewModel.SetupStep.COMPLETED_WITH_SAVING_FOR_PREVIOUSLY_SETUP_PASSWORD_FLOW -> {
                    // Navigate back to Home and show success message
                    showInterApplyPin {
                        displayToast(getString(R.string.your_password_has_been_set))
                        safePopBackStack(R.id.pinSetupFragment, R.id.homeFragment)
                    }
                }

                PinSetupViewModel.SetupStep.COMPLETED_WITHOUT_SAVING_FOR_FIRST_SETUP_PASSWORD_FLOW -> {
                    // Navigate to security question setup or complete setup
                    showInterApplyPin {
                        safeNavInter(
                            R.id.pinSetupFragment,
                            R.id.action_pinSetup_to_securityQuestionSetup,
                            bundleOf(
                                BundleKey.KEY_PIN_CODE to viewModel.uiState.value.firstPin,
                            ),
                        )
                    }
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PinSetupFragment.showInterApplyPin(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "pincode-apply",
        spaceName = "pass-1ID_interstitial",
        destinationToShowAds = R.id.pinSetupFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = action,
        onCloseAds = {},
    )
}

fun PinSetupFragment.observePinMatchStatus() {
    pinViewModel.uiState
        .map { it.isPinMatched }
        .distinctUntilChanged()
        .onEach { isPinMatched ->
            if (isPinMatched) {
                setPinInputsSuccess()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PinSetupFragment.observeErrorMessages() {
    pinViewModel.uiState
        .map { it.errorMessage }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            errorMessage?.let { error ->
                // Cancel any existing error clear timer
                errorClearJob?.cancel()
                showError(error)
                pinViewModel.clearError()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PinSetupFragment.observeButtonState() {
    pinViewModel.uiState
        .map { it.isApplyEnabled }
        .distinctUntilChanged()
        .onEach { isApplyEnabled ->
            binding.btnNext.isEnabled = isApplyEnabled || (!isConfirmStep && currentPin.length == 4)
            binding.btnNext.alpha = if (binding.btnNext.isEnabled) 1f else 0.2f
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PinSetupFragment.handleNextButtonClick() {
    if (currentPin.length == 4) {
        if (!isConfirmStep) {
            // First input - move to confirm step
            pinViewModel.setFirstPinInput(currentPin)
        } else {
            if (isFirstPasswordSetupFlow) {
                pinViewModel.goToSecurityQuestionScreenWithoutSaving()
            } else {
                // Apply - save PIN code
                pinViewModel.savePinCode()
            }
        }
    }
}

fun PinSetupFragment.updateCurrentPin() {
    currentPin = pinInputs.joinToString("") { it.text.toString() }
}

fun PinSetupFragment.updateButtonState() {
    val isComplete = currentPin.length == 4
    val currentState = pinViewModel.uiState.value

    binding.btnNext.isEnabled =
        if (isConfirmStep) {
            currentState.isApplyEnabled
        } else {
            isComplete
        }
    binding.btnNext.alpha = if (binding.btnNext.isEnabled) 1f else 0.2f
}

fun PinSetupFragment.updateUI() {
    binding.apply {
        if (isConfirmStep) {
            tvSubtitle.text = getString(R.string.re_enter_your_pin_code)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
            btnNext.text = getString(R.string.apply)
            showReEnterAds()
        } else {
            tvSubtitle.text = getString(R.string.set_your_new_pin_code)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
            btnNext.text = getString(R.string.next)
        }

        // Reset PIN input borders to normal
        setPinInputBordersNormal()
    }

    updateButtonState()
}

fun PinSetupFragment.showError(message: String) {
    binding.apply {
        tvSubtitle.text = message
        tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
        // Highlight incorrect inputs in PIN inputs
        pinInputs.forEach { editText ->
            editText.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
        }
    }

    // Disable Apply button and clear inputs during timeout
    binding.btnNext.isEnabled = false
    binding.btnNext.alpha = 0.2f

    // Auto clear error after 2 seconds for non-timeout errors
    startErrorClearTimer()
}

fun PinSetupFragment.startErrorClearTimer() {
    // Cancel any existing timer
    errorClearJob?.cancel()

    errorClearJob =
        viewLifecycleOwner.lifecycleScope.launch {
            delay(2000) // Wait 2 seconds
            clearErrorState()
            clearPinInputs()
        }
}

fun PinSetupFragment.setPinInputsSuccess() {
    // Set PIN inputs to success color (#2FAFFF)
    pinInputs.forEach { editText ->
        editText.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_2fafff))
    }
    // Enable Apply button
    binding.btnNext.isEnabled = true
    binding.btnNext.alpha = 1f
}

fun PinSetupFragment.clearPinInputs() {
    pinInputs.forEach {
        it.setText("")
        it.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
    }
    pinInputs[0].requestFocus()
    currentPin = ""
    updateButtonState()
}

fun PinSetupFragment.setPinInputBordersNormal() {
    pinInputs.forEach { editText ->
        editText.isSelected = false
        editText.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
    }
}

fun PinSetupFragment.togglePinVisibility() {
    isPinVisible = !isPinVisible

    binding.btnTogglePinVisibility.setImageResource(
        if (isPinVisible) R.drawable.ic_visibility else R.drawable.ic_visibility_off,
    )

    pinInputs.forEach { editText ->
        editText.transformationMethod =
            if (isPinVisible) {
                null
            } else {
                AsteriskPasswordTransformationMethod()
            }
        // Move cursor to end
        editText.setSelection(editText.text.length)
    }
}

fun PinSetupFragment.clearErrorState() {
    // Cancel any existing error clear timer
    errorClearJob?.cancel()

    binding.apply {
        // Reset subtitle to normal state
        if (isConfirmStep) {
            tvSubtitle.text = getString(R.string.re_enter_your_pin_code)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        } else {
            tvSubtitle.text = getString(R.string.set_your_new_pin_code)
            tvSubtitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_35496d))
        }
    }

    // Reset PIN input colors
    setPinInputBordersNormal()
}

fun PinSetupFragment.showReEnterAds() {
    if (!isShowedReloadAds) {
        isShowedReloadAds = true
        showLoadedNative(
            spaceNameConfig = "pincode-reEnter",
            spaceName = "pincode-reEnter_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
}

fun PinSetupFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "pincode-apply",
        spaceNameAds = "pass-1ID_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "pincode-enter",
        spaceName = "pincode-enter_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
