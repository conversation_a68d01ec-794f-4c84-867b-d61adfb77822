package co.ziplock.framework.presentation.security.pin

import android.app.Application
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import co.ziplock.R
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.PrefUtil
import javax.inject.Inject

@HiltViewModel
class PinSetupViewModel @Inject constructor(
    private val application: Application,
    private val prefUtil: PrefUtil,
    private val securityManager: SecurityManager
) : BaseViewModel() {

    enum class SetupStep {
        FIRST_INPUT,
        CONFIRM_INPUT,
        COMPLETED_WITHOUT_SAVING_FOR_FIRST_SETUP_PASSWORD_FLOW,
        COMPLETED_WITH_SAVING_FOR_PREVIOUSLY_SETUP_PASSWORD_FLOW,
    }

    data class PinSetupUiState(
        val currentStep: SetupStep = SetupStep.FIRST_INPUT,
        val firstPin: String = "",
        val isLoading: Boolean = false,
        val errorMessage: String? = null,
        val successMessage: String? = null,
        val isFirstSetup: Boolean = true,
        val isPinMatched: Boolean = false,
        val isApplyEnabled: Boolean = false
    )

    private val _uiState = MutableStateFlow(PinSetupUiState())
    val uiState: StateFlow<PinSetupUiState> = _uiState.asStateFlow()

    init {
        checkIfFirstSetup()
    }

    private fun checkIfFirstSetup() {
        val hasExistingPassword = securityManager.hasSecurity()
        _uiState.update { it.copy(isFirstSetup = !hasExistingPassword) }
    }

    fun setFirstPinInput(pin: String) {
        _uiState.update { 
            it.copy(
                firstPin = pin,
                currentStep = SetupStep.CONFIRM_INPUT,
                errorMessage = null,
                isPinMatched = false,
                isApplyEnabled = false
            )
        }
    }

    fun confirmPinInput(confirmPin: String) {
        val currentState = _uiState.value
        
        if (currentState.firstPin == confirmPin) {
            // PIN matches
            _uiState.update { 
                it.copy(
                    errorMessage = null,
                    isPinMatched = true,
                    isApplyEnabled = true
                )
            }
        } else {
            // PIN doesn't match
            _uiState.update { 
                it.copy(
                    errorMessage = "Incorrect PIN. Please try again",
                    isApplyEnabled = false,
                    isPinMatched = false
                )
            }
        }
    }

    fun showError() {
        _uiState.update { 
            it.copy(
                errorMessage = application.getString(R.string.your_pin_is_incorrect_try_again),
                isPinMatched = false,
                isApplyEnabled = false
            )
        }
    }

    fun savePinCode() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val currentState = _uiState.value
                
                // Setup PIN code (clears all existing security and sets new PIN)
                securityManager.setupPinCode(currentState.firstPin)

                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        currentStep = SetupStep.COMPLETED_WITH_SAVING_FOR_PREVIOUSLY_SETUP_PASSWORD_FLOW,
                        successMessage = application.getString(R.string.pin_setup_completed_successfully)
                    )
                }
                
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        errorMessage = application.getString(R.string.failed_to_setup_pin, e.message)
                    )
                }
            }
        }
    }

    fun goToSecurityQuestionScreenWithoutSaving() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }

            try {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        currentStep = SetupStep.COMPLETED_WITHOUT_SAVING_FOR_FIRST_SETUP_PASSWORD_FLOW,
                        successMessage = application.getString(R.string.pin_setup_completed_successfully)
                    )
                }

            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = application.getString(R.string.failed_to_setup_pin, e.message)
                    )
                }
            }
        }
    }

    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }

    fun clearSuccess() {
        _uiState.update { it.copy(successMessage = null) }
    }

    fun resetSetup() {
        _uiState.update { 
            PinSetupUiState(
                isFirstSetup = _uiState.value.isFirstSetup
            )
        }
    }

    fun goBackToFirstInput() {
        _uiState.update { 
            it.copy(
                currentStep = SetupStep.FIRST_INPUT,
                firstPin = "",
                errorMessage = null,
                isPinMatched = false,
                isApplyEnabled = false
            )
        }
    }

}