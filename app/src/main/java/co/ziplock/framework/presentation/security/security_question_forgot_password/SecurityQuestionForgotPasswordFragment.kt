package co.ziplock.framework.presentation.security.security_question_forgot_password

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentSecurityQuestionForgotPasswordBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@AndroidEntryPoint
class SecurityQuestionForgotPasswordFragment :
    BaseFragment<FragmentSecurityQuestionForgotPasswordBinding, SecurityQuestionForgotPasswordViewModel>(
        FragmentSecurityQuestionForgotPasswordBinding::inflate,
        SecurityQuestionForgotPasswordViewModel::class.java
    ) {

    @Inject
    lateinit var securityManager: SecurityManager

    override fun init(view: View) {
        viewModel.loadSecurityQuestions(requireContext())
        setupSpinner()
        setupTextWatcher()
        setupClickListeners()
    }

    override fun subscribeObserver(view: View) {
        observeButtonState()
        observeErrorMessages()
        observeAvailableQuestions()
    }

    companion object {
        const val TAG = "SecurityQuestionAnswerFragment"
    }
}