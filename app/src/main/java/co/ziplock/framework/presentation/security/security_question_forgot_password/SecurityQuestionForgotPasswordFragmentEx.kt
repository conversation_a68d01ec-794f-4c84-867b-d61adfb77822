package co.ziplock.framework.presentation.security.security_question_forgot_password

import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import co.ziplock.R
import co.ziplock.util.BundleKey
import co.ziplock.util.setPreventDoubleClick

fun SecurityQuestionForgotPasswordFragment.setupSpinner() {
    binding.spinnerSecurityQuestion.onItemSelectedListener =
        object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><*>?,
                view: View?,
                position: Int,
                id: Long
            ) {
                if (position > 0) { // Skip the "Select question" placeholder
                    val selectedQuestion = parent?.getItemAtPosition(position)?.toString() ?: ""
                    viewModel.setSecurityQuestion(selectedQuestion)
                } else {
                    viewModel.setSecurityQuestion("")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
}

fun SecurityQuestionForgotPasswordFragment.observeAvailableQuestions() {
    viewModel.uiState
        .map { it.availableSecurityQuestions }
        .filter { it.isNotEmpty() }
        .distinctUntilChanged()
        .onEach { availableSecurityQuestions ->
            val questions =
                listOf(getString(R.string.select_question)) + availableSecurityQuestions
            val adapter = ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                questions
            ).apply {
                setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            }

            binding.spinnerSecurityQuestion.adapter = adapter
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SecurityQuestionForgotPasswordFragment.setupTextWatcher() {
    binding.etSecurityAnswer.addTextChangedListener(object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        override fun afterTextChanged(s: Editable?) {
            val answer = s.toString().trim()
            viewModel.setSecurityAnswer(answer)

            // Hide error when user starts typing
            if (answer.isNotEmpty()) {
                hideError()
            }
        }
    })
}

fun SecurityQuestionForgotPasswordFragment.setupClickListeners() {
    setupBackButtonClickListener()
    setupRecoverViaEmailClickListener()
    setupSubmitButtonClickListener()
}

fun SecurityQuestionForgotPasswordFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        findNavController().popBackStack()
    }
}

fun SecurityQuestionForgotPasswordFragment.setupRecoverViaEmailClickListener() {
    binding.tvRecoverViaEmail.setPreventDoubleClick {
        //First back to home fragment
        findNavController().popBackStack(R.id.homeFragment, false)

        //Then navigate to email OTP verify fragment
        safeNav(
            R.id.homeFragment,
            R.id.action_homeFragment_to_emailPasswordRecoveryWhenForgotPasswordFragment,
        )
    }
}

fun SecurityQuestionForgotPasswordFragment.setupSubmitButtonClickListener() {
    binding.btnSubmit.setPreventDoubleClick {
        val answer = binding.etSecurityAnswer.text.toString().trim()
        viewModel.verifySecurityAnswerAndQuestion(
            context = requireContext(),
            answer,
            onSuccess = {
                viewModel.clearAllOldPasswordData()
                setFragmentResult(
                    BundleKey.KEY_REQUEST_OPEN_CHOOSE_PASSWORD_FROM_FORGOT_PASSWORD_FLOW,
                    bundleOf(
                        BundleKey.KEY_REQUEST_OPEN_CHOOSE_PASSWORD_FROM_FORGOT_PASSWORD_FLOW to true
                    )
                )
                // For now, just navigate back to home
                findNavController().popBackStack(R.id.homeFragment, false)
            }
        )
    }
}

fun SecurityQuestionForgotPasswordFragment.observeButtonState() {
    viewModel.uiState
        .map { it.isSubmitEnabled to it.isLoading }
        .distinctUntilChanged()
        .onEach { (isSubmitEnabled, isLoading) ->
            val isEnabled = isSubmitEnabled && !isLoading
            binding.btnSubmit.isEnabled = isEnabled
            binding.btnSubmit.alpha = if (isEnabled) 1f else 0.3f
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SecurityQuestionForgotPasswordFragment.observeErrorMessages() {
    viewModel.uiState
        .map { it.errorMessage }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            errorMessage?.let { error ->
                showError(error)
                viewModel.clearError()
            }
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SecurityQuestionForgotPasswordFragment.showError(message: String) {
    binding.tvError.text = message
    binding.tvError.isVisible = true
}

fun SecurityQuestionForgotPasswordFragment.hideError() {
    binding.tvError.isVisible = false
}