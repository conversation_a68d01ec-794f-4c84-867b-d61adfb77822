package co.ziplock.framework.presentation.security.security_question_forgot_password

import android.app.Application
import android.content.Context
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import co.ziplock.R
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import javax.inject.Inject

@HiltViewModel
class SecurityQuestionForgotPasswordViewModel @Inject constructor(
    private val application: Application,
    private val securityManager: SecurityManager
) : BaseViewModel() {

    data class SecurityQuestionAnswerUiState(
        val securityQuestion: String = "",
        val securityQuestionKey: String = "",
        val securityAnswer: String = "",
        val isLoading: Boolean = false,
        val errorMessage: String? = null,
        val isSubmitEnabled: Boolean = false,
        val isVerified: Boolean = false,
        val availableSecurityQuestions: List<String> = emptyList(),
        val availableSecurityQuestionKeys: List<String> = emptyList()
    )

    private val _uiState = MutableStateFlow(SecurityQuestionAnswerUiState())
    val uiState: StateFlow<SecurityQuestionAnswerUiState> = _uiState.asStateFlow()


    fun loadSecurityQuestions(context: Context) {
        val questions = context.resources.getStringArray(R.array.security_questions).toList()
        val questionKeys = context.resources.getStringArray(R.array.security_question_keys).toList()
        _uiState.update {
            it.copy(
                availableSecurityQuestions = questions,
                availableSecurityQuestionKeys = questionKeys
            )
        }
    }

    fun setSecurityQuestion(question: String) {
        val currentState = _uiState.value
        val questionIndex = currentState.availableSecurityQuestions.indexOf(question)
        val questionKey = if (questionIndex >= 0 && questionIndex < currentState.availableSecurityQuestionKeys.size) {
            currentState.availableSecurityQuestionKeys[questionIndex]
        } else {
            ""
        }

        _uiState.update {
            val newState = it.copy(
                securityQuestion = question,
                securityQuestionKey = questionKey
            )
            newState.copy(isSubmitEnabled = validateInputs(newState.securityQuestion, newState.securityAnswer))
        }
    }

    private fun validateInputs(question: String, answer: String): Boolean {
        return question.isNotEmpty() && answer.isNotEmpty()
    }

    fun setSecurityAnswer(answer: String) {
        _uiState.update { 
            it.copy(
                securityAnswer = answer,
                isSubmitEnabled = answer.isNotEmpty(),
                errorMessage = null
            )
        }
    }

    fun clearAllOldPasswordData() {
        securityManager.clearSecurity()
    }

    fun verifySecurityAnswerAndQuestion(context: Context, answer: String, onSuccess: () -> Unit) {
        _uiState.update { it.copy(isLoading = true) }

        val isCorrectQuestion = securityManager.getSecurityQuestionText(context = context)
            ?.equals(uiState.value.securityQuestion) == true
        val isCorrectAnswer = securityManager.verifySecurityAnswer(answer)

        if (isCorrectQuestion && isCorrectAnswer) {
            _uiState.update {
                it.copy(
                    isLoading = false,
                )
            }
            onSuccess()
        } else {
            _uiState.update {
                it.copy(
                    isLoading = false,
                    errorMessage = application.getString(R.string.your_question_answer_is_incorrect_try_again)
                )
            }
        }
    }

    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
}