package co.ziplock.framework.presentation.security.setup_recovery_email_first_open

import android.view.View
import co.ziplock.databinding.FragmentSecurityQuestionRecoveryEmailFirstOpenBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.security.change_recovery_email.showAds
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SetupRecoveryEmailFirstOpenFragment :
    BaseFragment<FragmentSecurityQuestionRecoveryEmailFirstOpenBinding, SetupRecoveryEmailFirstOpenViewModel>(
        FragmentSecurityQuestionRecoveryEmailFirstOpenBinding::inflate,
        SetupRecoveryEmailFirstOpenViewModel::class.java,
    ) {
    override fun init(view: View) {
        setupTextWatcher()
        setupClickListeners()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeButtonState()
        observeErrorMessages()
        observeRequestOtpState()
    }

    companion object {
        const val TAG = "SecurityQuestionRecoveryFragment"
    }
}
