package co.ziplock.framework.presentation.security.setup_recovery_email_from_recovery_email

import android.view.View
import co.ziplock.databinding.FragmentSecurityQuestionFromRecoveryEmailBinding
import co.ziplock.framework.presentation.common.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SetupRecoveryEmailFromRecoveryEmailFragment :
    BaseFragment<FragmentSecurityQuestionFromRecoveryEmailBinding, SetupRecoveryEmailFromRecoveryEmailViewModel>(
        FragmentSecurityQuestionFromRecoveryEmailBinding::inflate,
        SetupRecoveryEmailFromRecoveryEmailViewModel::class.java,
    ) {
    override fun init(view: View) {
        setupTextWatcher()
        setupClickListeners()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeButtonState()
        observeErrorMessages()
        observeRequestOtpState()
    }

    companion object {
        const val TAG = "SetupRecoveryEmailFromRecoveryEmailFragment"
    }
}
