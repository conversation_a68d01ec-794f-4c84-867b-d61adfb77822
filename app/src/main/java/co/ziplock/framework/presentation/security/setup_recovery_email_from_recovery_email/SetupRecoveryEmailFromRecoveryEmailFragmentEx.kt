package co.ziplock.framework.presentation.security.setup_recovery_email_from_recovery_email

import android.text.Editable
import android.text.TextWatcher
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import co.ziplock.R
import co.ziplock.framework.presentation.common.SendOtpStatus
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.util.BundleKey
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun SetupRecoveryEmailFromRecoveryEmailFragment.setupTextWatcher() {
    binding.etEmail.addTextChangedListener(
        object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int,
            ) {
            }

            override fun onTextChanged(
                s: CharSequence?,
                start: Int,
                before: Int,
                count: Int,
            ) {
            }

            override fun afterTextChanged(s: Editable?) {
                val email = s.toString().trim()
                viewModel.setEmail(email)

                // Hide error when user starts typing
                if (email.isNotEmpty()) {
                    hideError()
                }
            }
        },
    )
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.setupClickListeners() {
    setupSaveButtonClickListener()
    setupBackButtonClickListener()
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.setupSaveButtonClickListener() {
    binding.btnSave.setPreventDoubleClick {
        viewModel.validateEmailAndNextAction {
            val email =
                viewModel.uiState.value.email
                    .trim()
            commonViewModel.requestOtp(email)
        }
    }
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }

    onSystemBackEvent {
        backEvent()
    }
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "email-back",
        spaceName = "pass-1ID_interstitial",
        destinationToShowAds = R.id.setupRecoveryEmailFromRecoveryEmailFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safeNavigateUp(R.id.setupRecoveryEmailFromRecoveryEmailFragment)
        },
        onCloseAds = {},
    )
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.backToHomeScreen() {
    displayToast(getString(R.string.recovery_email_has_been_updated))
    findNavController().popBackStack(R.id.homeFragment, false)
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.observeButtonState() {
    viewModel.uiState
        .map { it.isSaveEnabled to it.isLoading }
        .distinctUntilChanged()
        .onEach { (isSaveEnabled, isLoading) ->
            binding.btnSave.isEnabled = isSaveEnabled && !isLoading

            // Update button appearance
            if (binding.btnSave.isEnabled) {
                binding.btnSave.backgroundTintList =
                    ContextCompat.getColorStateList(requireContext(), R.color.blue_1da1f2)
            } else {
                binding.btnSave.backgroundTintList =
                    ContextCompat.getColorStateList(requireContext(), R.color.blue_d6eeff)
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.observeErrorMessages() {
    viewModel.uiState
        .map { it.errorMessage }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            errorMessage?.let { error ->
                showError(error)
                viewModel.clearError()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.showError(message: String) {
    binding.tvError.text = message
    binding.tvError.isVisible = true
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.hideError() {
    binding.tvError.isVisible = false
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.observeRequestOtpState() {
    commonViewModel.sendOtpStatus
        .onEach {
            Timber.d("SendOtpStatus: $it")
            when (it) {
                SendOtpStatus.Limited -> {
                    displayToast(R.string.des_limit_request_otp)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                }

                SendOtpStatus.Error -> {
                    displayToast(R.string.something_error)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                }

                SendOtpStatus.None -> {
                    updateUiLoadingState(false)
                }

                SendOtpStatus.Standby -> {
                    updateUiLoadingState(true)
                }

                is SendOtpStatus.Success -> {
                    commonViewModel.setOtpCode(it.otp)
                    updateUiLoadingState(false)
                    commonViewModel.resetOtpRequestStatus()
                    displayToast(getString(R.string.otp_sent_successfully))

                    // Navigate to OTP verify screen
                    showLoadedInter(
                        spaceNameConfig = "email-save",
                        spaceName = "pass-1ID_interstitial",
                        destinationToShowAds = R.id.setupRecoveryEmailFromRecoveryEmailFragment,
                        isShowLoadingView = true,
                        isScreenType = false,
                        navOrBack = {
                            safeNavInter(
                                R.id.setupRecoveryEmailFromRecoveryEmailFragment,
                                R.id.action_setupRecoveryEmailFromRecoveryEmailFragment_to_otpVerifyWhenSetupOrChangeEmailFragment,
                                bundleOf(BundleKey.KEY_CURRENT_EMAIL to viewModel.uiState.value.email),
                            )
                        },
                        onCloseAds = {},
                    )
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.updateUiLoadingState(isLoading: Boolean) {
    binding.btnSave.isEnabled = !isLoading
    binding.btnSave.alpha = if (!isLoading) 1f else 0.7f
    binding.progressBar.isVisible = isLoading
}

fun SetupRecoveryEmailFromRecoveryEmailFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "email",
        spaceName = "email_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
    safePreloadAds(
        listSpaceNameConfig = listOf("email-save", "email-back"),
        spaceNameAds = "pass-1ID_interstitial",
    )
}
