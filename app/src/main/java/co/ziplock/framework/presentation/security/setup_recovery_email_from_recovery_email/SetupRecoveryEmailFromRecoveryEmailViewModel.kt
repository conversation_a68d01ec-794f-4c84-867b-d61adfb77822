package co.ziplock.framework.presentation.security.setup_recovery_email_from_recovery_email

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.EmailUtils.validateEmail
import javax.inject.Inject

@HiltViewModel
class SetupRecoveryEmailFromRecoveryEmailViewModel @Inject constructor(
    private val securityManager: SecurityManager,
) : BaseViewModel() {

    data class PasswordRecoveryUiState(
        val email: String = "",
        val isLoading: Boolean = false,
        val errorMessage: String? = null,
        val isEmailValid: Boolean = false,
        val isSaveEnabled: Boolean = false,
    )

    private val _uiState = MutableStateFlow(PasswordRecoveryUiState())
    val uiState: StateFlow<PasswordRecoveryUiState> = _uiState.asStateFlow()

    fun setEmail(email: String) {
        val isValid = validateEmail(email)
        _uiState.update { 
            it.copy(
                email = email,
                isEmailValid = isValid,
                isSaveEnabled = email.isNotEmpty(), // Enable button if email is not empty
                errorMessage = null
            )
        }
    }

    fun validateEmailAndNextAction(onNextAction: () -> Unit) {
        val currentState = _uiState.value
        
        // Validate email when Save button is pressed
        if (!validateEmail(currentState.email)) {
            _uiState.update { it.copy(errorMessage = "Enter a valid email address") }
            return
        }

        _uiState.update {
            it.copy(
                errorMessage = null,
                isLoading = false,
            )
        }

        onNextAction.invoke()
    }

    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
}