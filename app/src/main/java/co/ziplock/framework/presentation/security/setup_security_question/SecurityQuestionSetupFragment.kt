package co.ziplock.framework.presentation.security.setup_security_question

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentSecurityQuestionSetupBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.util.BundleKey

@AndroidEntryPoint
class SecurityQuestionSetupFragment : BaseFragment<FragmentSecurityQuestionSetupBinding, SecurityQuestionSetupViewModel>(
    FragmentSecurityQuestionSetupBinding::inflate,
    SecurityQuestionSetupViewModel::class.java
) {
    val previousPinCode: String? by lazy {
        arguments?.getString(BundleKey.KEY_PIN_CODE)
    }

    val previousPattern: List<Int>? by lazy {
        arguments?.getIntegerArrayList(BundleKey.KEY_PATTERN_CODE)?.toList()
    }

    val isFromChangeSecurityQuestion: Boolean by lazy {
        arguments?.getBoolean(BundleKey.KEY_FROM_CHANGE_SECURITY_QUESTION, false) ?: false
    }

    var isApplySaveDone = false

    override fun init(view: View) {
        viewModel.loadSecurityQuestions(requireContext())
        setupSpinner()
        setupTextWatcher()
        setupClickListeners()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeAvailableQuestions()
        observeButtonState()
        observeErrorMessages()
        observeSuccessMessages()
    }

    companion object {
        const val TAG = "SecurityQuestionSetupFragment"
    }
}