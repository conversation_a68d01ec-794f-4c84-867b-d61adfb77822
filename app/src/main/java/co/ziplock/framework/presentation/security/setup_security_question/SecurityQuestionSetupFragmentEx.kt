package co.ziplock.framework.presentation.security.setup_security_question

import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.dialog.CancelSavingSecurityQuestionDialogFragment
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun SecurityQuestionSetupFragment.setupSpinner() {
    binding.spinnerSecurityQuestion.onItemSelectedListener =
        object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                if (position > 0) { // Skip the "Select question" placeholder
                    val selectedQuestion = parent?.getItemAtPosition(position)?.toString() ?: ""
                    viewModel.setSecurityQuestion(selectedQuestion)
                } else {
                    viewModel.setSecurityQuestion("")
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
}

fun SecurityQuestionSetupFragment.setupTextWatcher() {
    binding.etSecurityAnswer.addTextChangedListener(
        object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int,
            ) {
            }

            override fun onTextChanged(
                s: CharSequence?,
                start: Int,
                before: Int,
                count: Int,
            ) {
            }

            override fun afterTextChanged(s: Editable?) {
                val answer = s.toString().trim()
                Timber.d("AfterTextChanged: length=${answer.length}, text=$answer")
                viewModel.setSecurityAnswer(answer)
            }
        },
    )
}

fun SecurityQuestionSetupFragment.setupClickListeners() {
    setupSaveButtonClickListener()
    setupBackButtonClickListener()
}

fun SecurityQuestionSetupFragment.setupSaveButtonClickListener() {
    binding.btnSave.setPreventDoubleClick {
        validateAndSave()
    }
}

fun SecurityQuestionSetupFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }

    onSystemBackEvent {
        backEvent()
    }
}

fun SecurityQuestionSetupFragment.backEvent() {
    if (!isApplySaveDone) {
        CancelSavingSecurityQuestionDialogFragment
            .Builder()
            .setOnClickExitButton {
                showBackInterAndDoAction {
                    safePopBackStack(R.id.securityQuestionSetupFragment, R.id.homeFragment)
                }
            }.build()
            .show(childFragmentManager, SecurityQuestionSetupFragment.TAG)
    } else {
        showBackInterAndDoAction {
            safePopBackStack(R.id.securityQuestionSetupFragment, R.id.homeFragment)
        }
    }
}

fun SecurityQuestionSetupFragment.showBackInterAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "question-back",
        spaceName = "pass-1ID_interstitial",
        destinationToShowAds = R.id.securityQuestionSetupFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun SecurityQuestionSetupFragment.showApplyInterAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "question-save",
        spaceName = "pass-1ID_interstitial",
        destinationToShowAds = R.id.securityQuestionSetupFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun SecurityQuestionSetupFragment.observeAvailableQuestions() {
    viewModel.uiState
        .map { it.availableSecurityQuestions }
        .filter { it.isNotEmpty() }
        .distinctUntilChanged()
        .onEach { availableSecurityQuestions ->
            val questions = listOf(getString(R.string.select_question)) + availableSecurityQuestions
            val adapter =
                ArrayAdapter(
                    requireContext(),
                    android.R.layout.simple_spinner_item,
                    questions,
                ).apply {
                    setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
                }

            binding.spinnerSecurityQuestion.adapter = adapter
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SecurityQuestionSetupFragment.observeButtonState() {
    viewModel.uiState
        .map { it.isSaveEnabled to it.isLoading }
        .distinctUntilChanged()
        .onEach { (isSaveEnabled, isLoading) ->
            binding.btnSave.isEnabled = isSaveEnabled && !isLoading
            binding.btnSave.alpha = if (binding.btnSave.isEnabled) 1f else 0.2f
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SecurityQuestionSetupFragment.observeErrorMessages() {
    viewModel.uiState
        .map { it.errorMessage }
        .distinctUntilChanged()
        .onEach { errorMessage ->
            errorMessage?.let { error ->
                showError(error)
                viewModel.clearError()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SecurityQuestionSetupFragment.observeSuccessMessages() {
    viewModel.uiState
        .map { it.successMessage }
        .distinctUntilChanged()
        .onEach { successMessage ->
            successMessage?.let { message ->
                // Show success message if needed
                viewModel.clearSuccess()
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SecurityQuestionSetupFragment.validateAndSave() {
    val selectedPosition = binding.spinnerSecurityQuestion.selectedItemPosition
    val selectedQuestion =
        if (selectedPosition > 0) {
            binding.spinnerSecurityQuestion.selectedItem?.toString() ?: ""
        } else {
            ""
        }
    val answer =
        binding.etSecurityAnswer.text
            .toString()
            .trim()

    // Validate inputs
    val error = viewModel.validateAndShowError(selectedQuestion, answer)
    if (error != null) {
        showError(error)
        return
    }

    // Hide error and complete setup
    hideError()

    viewModel.setSecurityQuestion(selectedQuestion)
    viewModel.setSecurityAnswer(answer)
    viewModel.completeSetupAndSavePinCode(previousPinCode, previousPattern) {
        isApplySaveDone = true

        if (isFromChangeSecurityQuestion) {
            showApplyInterAndDoAction {
                safePopBackStack(R.id.securityQuestionSetupFragment, R.id.homeFragment)
            }
        }

        if (viewModel.isExistRecoveredEmail()) {
            backToHomeScreenAndShowSuccessMessage()
        } else {
            // Navigate to SecurityQuestionRecoveryFragment
            showApplyInterAndDoAction {
                safeNavInter(
                    R.id.securityQuestionSetupFragment,
                    R.id.action_securityQuestionSetupFragment_to_setupRecoveryEmailFirstOpenFragment,
                )
            }
        }
    }
}

fun SecurityQuestionSetupFragment.backToHomeScreenAndShowSuccessMessage() {
    // Navigate back to Home and show success message
    showApplyInterAndDoAction {
        displayToast(getString(R.string.your_password_has_been_set))
        safePopBackStack(R.id.securityQuestionSetupFragment, R.id.homeFragment)
    }
}

fun SecurityQuestionSetupFragment.showError(message: String) {
    binding.tvError.text = message
    binding.tvError.isVisible = true
    binding.tvError.setTextColor(ContextCompat.getColor(requireContext(), R.color.red_error))
}

fun SecurityQuestionSetupFragment.hideError() {
    binding.tvError.isVisible = false
}

fun SecurityQuestionSetupFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("question-save", "question-back"),
        spaceNameAds = "pass-1ID_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "question",
        spaceName = "question_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
