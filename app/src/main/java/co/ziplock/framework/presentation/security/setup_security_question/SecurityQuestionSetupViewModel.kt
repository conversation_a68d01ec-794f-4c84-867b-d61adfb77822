package co.ziplock.framework.presentation.security.setup_security_question

import android.app.Application
import android.content.Context
import androidx.lifecycle.viewModelScope
import co.ziplock.R
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.manager.SecurityManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SecurityQuestionSetupViewModel
    @Inject
    constructor(
        private val application: Application,
        private val securityManager: SecurityManager,
    ) : BaseViewModel() {
        data class SecurityQuestionUiState(
            val securityQuestion: String = "",
            val securityQuestionKey: String = "",
            val securityAnswer: String = "",
            val isLoading: Boolean = false,
            val errorMessage: String? = null,
            val successMessage: String? = null,
            val isSaveEnabled: Boolean = false,
            val availableSecurityQuestions: List<String> = emptyList(),
            val availableSecurityQuestionKeys: List<String> = emptyList(),
        )

        private val _uiState = MutableStateFlow(SecurityQuestionUiState())
        val uiState: StateFlow<SecurityQuestionUiState> = _uiState.asStateFlow()

        fun loadSecurityQuestions(context: Context) {
            val questions = context.resources.getStringArray(R.array.security_questions).toList()
            val questionKeys = context.resources.getStringArray(R.array.security_question_keys).toList()
            _uiState.update {
                it.copy(
                    availableSecurityQuestions = questions,
                    availableSecurityQuestionKeys = questionKeys,
                )
            }
        }

        fun setSecurityQuestion(question: String) {
            val currentState = _uiState.value
            val questionIndex = currentState.availableSecurityQuestions.indexOf(question)
            val questionKey =
                if (questionIndex >= 0 && questionIndex < currentState.availableSecurityQuestionKeys.size) {
                    currentState.availableSecurityQuestionKeys[questionIndex]
                } else {
                    ""
                }

            _uiState.update {
                val newState =
                    it.copy(
                        securityQuestion = question,
                        securityQuestionKey = questionKey,
                    )
                newState.copy(
                    isSaveEnabled =
                        validateInputs(
                            newState.securityQuestion,
                            newState.securityAnswer,
                        ),
                )
            }
        }

        fun setSecurityAnswer(answer: String) {
            _uiState.update {
                val newState = it.copy(securityAnswer = answer.trim())
                newState.copy(
                    isSaveEnabled =
                        validateInputs(
                            newState.securityQuestion,
                            newState.securityAnswer,
                        ),
                )
            }
        }

        private fun validateInputs(
            question: String,
            answer: String,
        ): Boolean = question.isNotEmpty() && answer.isNotEmpty()

        fun validateSecurityAnswer(answer: String): String? =
            when {
                answer.isBlank() -> application.getString(R.string.please_provide_your_answer)
                answer.length !in 3..256 -> application.getString(R.string.answer_must_be_between_3_and_256_characters)
                else -> null
            }

        fun validateAndShowError(
            question: String,
            answer: String,
        ): String? =
            when {
                question.isEmpty() -> application.getString(R.string.please_select_a_security_question)
                answer.length !in 3..256 -> application.getString(R.string.answer_must_be_between_3_and_256_characters)
                else -> null
            }

        fun isExistRecoveredEmail(): Boolean = securityManager.hasRecoveryEmail()

        fun completeSetupAndSavePinCode(
            previousPinCode: String?,
            previousPattern: List<Int>?,
            onSuccess: () -> Unit,
        ) {
            viewModelScope.launch {
                _uiState.update { it.copy(isLoading = true) }

                try {
                    val currentState = _uiState.value

                    // Validate security answer
                    val answerError = validateSecurityAnswer(currentState.securityAnswer)
                    if (answerError != null) {
                        _uiState.update {
                            it.copy(
                                isLoading = false,
                                errorMessage = answerError,
                            )
                        }
                        return@launch
                    }

                    // Save security question key and answer
                    securityManager.setSecurityQuestionKey(currentState.securityQuestionKey)
                    securityManager.setSecurityAnswer(currentState.securityAnswer)

                    if (previousPinCode != null) {
                        securityManager.setupPinCode(previousPinCode)
                    }

                    if (previousPattern != null) {
                        securityManager.setupPatternCode(previousPattern)
                    }

                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            successMessage = application.getString(R.string.security_question_setup_completed_successfully),
                        )
                    }

                    onSuccess()
                } catch (e: Exception) {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage =
                                application.getString(
                                    R.string.failed_to_setup_security_question,
                                    e.message,
                                ),
                        )
                    }
                }
            }
        }

        fun clearError() {
            _uiState.update { it.copy(errorMessage = null) }
        }

        fun clearSuccess() {
            _uiState.update { it.copy(successMessage = null) }
        }
    }
