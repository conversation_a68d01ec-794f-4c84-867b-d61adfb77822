package co.ziplock.framework.presentation.sound

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.databinding.FragmentSoundBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.common.SoundPlayerManager
import co.ziplock.framework.presentation.sound.adapter.SoundAdapter
import co.ziplock.util.BundleKey
import javax.inject.Inject

@AndroidEntryPoint
class SoundFragment : BaseFragment<FragmentSoundBinding, SoundViewModel>(
    FragmentSoundBinding::inflate,
    SoundViewModel::class.java
) {
    @Inject
    lateinit var soundPlayerManager: SoundPlayerManager
    
    var soundAdapter: SoundAdapter? = null

    val isReEditFlow by lazy {
        arguments?.getBoolean(BundleKey.KEY_FROM_RE_EDIT_FLOW) ?: false
    }

    override fun init(view: View) {
        setupRecyclerView()
        setupTabLayout()
        setupSystemBackEvent()
        setupBackButtonClickListener()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeSoundCategories()
        observeCurrentCategorySounds()
        observeSelectedCategory()
        observeLoadingState()
        observeSelectedSoundUrl()
        observeSoundPlayerState()
    }
    
    override fun onPause() {
        super.onPause()
        // Pause sound when fragment is paused
        soundPlayerManager.pauseSound()
    }

    companion object {
        const val TAG = "SoundFragment"
        
        fun newInstance() = SoundFragment()
    }
}