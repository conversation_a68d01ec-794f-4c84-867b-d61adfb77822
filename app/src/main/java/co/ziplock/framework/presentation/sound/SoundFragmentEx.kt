package co.ziplock.framework.presentation.sound

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import co.ziplock.R
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.framework.presentation.sound.adapter.SoundAdapter
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.disableTooltipsForTabs
import co.ziplock.util.setPreventDoubleClick
import com.google.android.material.tabs.TabLayout
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun SoundFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun SoundFragment.setupSystemBackEvent() {
    onSystemBackEvent {
        backEvent()
    }
}

fun SoundFragment.backEvent() {
    findNavController().navigateUp()
}

fun SoundFragment.navigateToEditLayout(sound: SoundResponse) {
    // Handle "No Sound" selection
    if (sound.id == Constant.NO_SOUND_ID) {
        // Clear selected sound for "No Sound"
        commonViewModel.setSelectedSound(null)
    } else {
        // Save selected sound URL to preferences for preview
        commonViewModel.setSelectedSound(sound)
    }

    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.SOUND_DATA, sound)
        }
    safeNavInter(
        R.id.soundFragment,
        R.id.action_soundFragment_to_editLayoutFragment,
        bundle,
    )
}

fun SoundFragment.showInterChooseSound(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "listsound-choosect",
        spaceName = "list-1ID_interstitial",
        destinationToShowAds = R.id.soundFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun SoundFragment.setupRecyclerView() {
    soundAdapter =
        SoundAdapter(
            onSoundClick = { sound ->
                // Handle "No Sound" selection
                if (sound.id == Constant.NO_SOUND_ID) {
                    // Clear selected sound URL for "No Sound"
                    soundAdapter?.setSelectedSoundUrl(null)
                } else {
                    // Set selected sound URL for regular sounds
                    soundAdapter?.setSelectedSoundUrl(sound.fileUrl)
                }

                if (isReEditFlow) {
                    // If coming from EditLayoutFragment, set data to CommonViewModel and go back
                    showInterChooseSound {
                        commonViewModel.setEditLayoutSound(sound)
                        safeNavigateUp(R.id.soundFragment)
                    }
                } else {
                    // Normal flow - set data to CommonViewModel and navigate to EditLayout
                    showInterChooseSound {
                        commonViewModel.setEditLayoutSound(sound)
                        navigateToEditLayout(sound)
                    }
                }
            },
            onPlayPauseClick = { sound ->
                // Handle play/pause click
                handleSoundPlayPause(sound)
            },
        )
    soundAdapter?.setFragment(this)
    binding.rvSounds.apply {
        layoutManager = LinearLayoutManager(context)
        adapter = soundAdapter
    }

    // Set initial selected sound URL based on flow type
    val initialSelectedSoundUrl =
        if (isReEditFlow) {
            commonViewModel.editLayoutUiState.value.soundResponse
                ?.fileUrl
        } else {
            prefUtil.selectedSoundUrl
        }

    soundAdapter?.setSelectedSoundUrl(initialSelectedSoundUrl)
}

fun SoundFragment.setupTabLayout() {
    binding.tabLayout.disableTooltipsForTabs()

    binding.tabLayout.addOnTabSelectedListener(
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                val tabText = tab.text.toString()
                val category =
                    when (tabText) {
                        getString(R.string.all) -> Constant.ALL
                        else -> tabText
                    }
                commonViewModel.selectSoundCategory(category)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {}

            override fun onTabReselected(tab: TabLayout.Tab) {}
        },
    )
}

fun SoundFragment.observeSoundCategories() {
    commonViewModel.soundUiState
        .map { it.categories }
        .distinctUntilChanged()
        .onEach { categories ->
            updateTabLayout(categories)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SoundFragment.observeCurrentCategorySounds() {
    commonViewModel.soundUiState
        .map { it.currentCategorySounds }
        .distinctUntilChanged()
        .onEach { sounds ->
            // Add "No Sound" option at the beginning of the list
            val soundsWithNoSound =
                mutableListOf<SoundResponse>().apply {
                    add(SoundResponse.createNoSound())
                    addAll(sounds)
                }

            soundAdapter?.submitList(filterListAds(soundsWithNoSound))
            // Update selected sound URL when list changes based on flow type
            val selectedSoundUrl =
                if (isReEditFlow) {
                    commonViewModel.editLayoutUiState.value.soundResponse
                        ?.fileUrl
                } else {
                    prefUtil.selectedSoundUrl
                }
            soundAdapter?.setSelectedSoundUrl(selectedSoundUrl)
            // Show/hide empty state
            binding.emptyState.isVisible =
                sounds.isEmpty() &&
                !commonViewModel.soundUiState.value.isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SoundFragment.filterListAds(rawList: List<SoundResponse>): List<SoundResponse> {
    val tag = "filterListAdsSound"
    if (!checkConditionShowAds(
            context = requireContext(),
            spaceNameConfig = "listsound-ct",
        )
    ) {
        Timber.tag(tag).d("rawList $rawList")
        return rawList
    }

    val editedList = mutableListOf<SoundResponse>()
    var count = 0
    var isLastTimeAddAds1 = false

    val itemAds1 =
        AdsItem(
            configName = "listsound-ct",
            admobIdName = "listsound-ct_native1",
        )
    val zipItemAds1 =
        SoundResponse(
            adsItem = itemAds1,
        )

    val itemAds2 =
        AdsItem(
            configName = "listsound-ct",
            admobIdName = "listsound-ct_native2",
        )

    val zipItemAds2 =
        SoundResponse(
            adsItem = itemAds2,
        )

    rawList.forEachIndexed { index, item ->
        editedList.add(item)
        count++
        if (index == 1 || count == Constant.numberOfContentBetweenSoundList) {
            if (!isLastTimeAddAds1) {
                editedList.add(zipItemAds1)
                isLastTimeAddAds1 = true
            } else {
                editedList.add(zipItemAds2)
                isLastTimeAddAds1 = false
            }
            count = 0
        }
    }
    Timber.tag(tag).d("editedList $editedList")
    return editedList
}

fun SoundFragment.observeSelectedCategory() {
    commonViewModel.soundUiState
        .map { it.selectedCategory }
        .distinctUntilChanged()
        .onEach { selectedCategory ->
            selectedCategory?.let { category ->
                selectTabByCategory(category)
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SoundFragment.observeLoadingState() {
    commonViewModel.soundUiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressBar.isVisible = isLoading
            binding.rvSounds.isVisible = !isLoading
            // Hide empty state when loading
            if (isLoading) {
                binding.emptyState.isVisible = false
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SoundFragment.observeSelectedSoundUrl() {
    if (isReEditFlow) {
        // For re-edit flow, observe editLayoutUiState changes
        commonViewModel.editLayoutUiState
            .map { it.soundResponse?.fileUrl }
            .distinctUntilChanged()
            .onEach { selectedSoundUrl ->
                soundAdapter?.setSelectedSoundUrl(selectedSoundUrl)
            }.launchIn(viewLifecycleOwner.lifecycleScope)
    } else {
        // For normal flow, observe prefUtil changes
        viewLifecycleOwner.lifecycleScope.launch {
            var lastSelectedSoundUrl = prefUtil.selectedSoundUrl

            // Update adapter with current selected sound URL
            soundAdapter?.setSelectedSoundUrl(lastSelectedSoundUrl)

            // Check for changes when fragment resumes
            viewLifecycleOwner.lifecycle.addObserver(
                object : LifecycleEventObserver {
                    override fun onStateChanged(
                        source: LifecycleOwner,
                        event: Lifecycle.Event,
                    ) {
                        if (event == Lifecycle.Event.ON_RESUME) {
                            val currentSelectedSoundUrl = prefUtil.selectedSoundUrl
                            if (currentSelectedSoundUrl != lastSelectedSoundUrl) {
                                lastSelectedSoundUrl = currentSelectedSoundUrl
                                soundAdapter?.setSelectedSoundUrl(currentSelectedSoundUrl)
                            }
                        }
                    }
                },
            )
        }
    }
}

private fun SoundFragment.updateTabLayout(categories: List<String>) {
    binding.tabLayout.removeAllTabs()

    categories.forEach { category ->
        val tab = binding.tabLayout.newTab()
        tab.text =
            when (category) {
                Constant.ALL -> getString(R.string.all)
                else -> category
            }
        binding.tabLayout.addTab(tab)
    }
}

private fun SoundFragment.selectTabByCategory(category: String) {
    val tabCount = binding.tabLayout.tabCount
    for (i in 0 until tabCount) {
        val tab = binding.tabLayout.getTabAt(i)
        val tabText = tab?.text.toString()

        // Check if this tab matches the category
        val matches =
            when (category) {
                Constant.ALL -> tabText == getString(R.string.all)
                else -> tabText == category
            }

        if (matches) {
            tab?.select()
            break
        }
    }
}

fun SoundFragment.handleSoundPlayPause(sound: SoundResponse) {
    if (sound.id == Constant.NO_SOUND_ID || sound.fileUrl.isEmpty()) {
        return
    }

    soundPlayerManager.playSound(sound.fileUrl)
}

fun SoundFragment.observeSoundPlayerState() {
    // Observe playing state changes
    soundPlayerManager.playingState
        .onEach { state ->
            val currentUrl = soundPlayerManager.getCurrentPlayingUrl()
            soundAdapter?.updatePlayingState(currentUrl, state)
        }.launchIn(viewLifecycleOwner.lifecycleScope)

    // Observe current playing URL changes
    soundPlayerManager.currentPlayingSoundUrl
        .onEach { playingUrl ->
            val state = soundPlayerManager.playingState.value
            soundAdapter?.updatePlayingState(playingUrl, state)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SoundFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "listsound",
        spaceName = "listsound_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
    safePreloadAds(
        spaceNameConfig = "listsound-ct",
        spaceNameAds = "listsound-ct_native1",
        includeHasBeenOpened = false,
    )
    safePreloadAds(
        spaceNameConfig = "listsound-ct",
        spaceNameAds = "listsound-ct_native2",
        includeHasBeenOpened = false,
    )

    safePreloadAds(
        spaceNameConfig = "listsound-choosect",
        spaceNameAds = "list-1ID_interstitial",
    )
}
