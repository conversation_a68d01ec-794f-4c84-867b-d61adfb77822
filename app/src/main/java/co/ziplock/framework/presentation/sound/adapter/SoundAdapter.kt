package co.ziplock.framework.presentation.sound.adapter

import android.view.LayoutInflater
import androidx.core.view.isVisible
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import co.ziplock.R
import co.ziplock.databinding.ItemSoundBinding
import co.ziplock.databinding.ViewItemNativeSoundBinding
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.SoundPlayerManager
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.util.Constant
import co.ziplock.util.changeTextColor
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import pion.datlt.libads.utils.adsuntils.showLoadedNative

class SoundAdapter(
    private val onSoundClick: (SoundResponse) -> Unit,
    private val onPlayPauseClick: (SoundResponse) -> Unit,
) : BaseListAdapter<SoundResponse, ViewDataBinding>(
        createDiffCallback(
            areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
        ),
    ) {
    private var selectedSoundUrl: String? = null
    private var currentPlayingUrl: String? = null
    private var playingState: SoundPlayerManager.PlayingState =
        SoundPlayerManager.PlayingState.Stopped

    override fun getLayoutRes(viewType: Int): Int = if (viewType == ITEM_TYPE_ADS) R.layout.view_item_native_sound else R.layout.item_sound

    override fun getItemViewType(position: Int): Int = if (getItem(position).adsItem != null) ITEM_TYPE_ADS else ITEM_TYPE_NORMAL

    private var fragment: Fragment? = null

    fun setFragment(fragment: Fragment) {
        this.fragment = fragment
    }

    override fun bindView(
        binding: ViewDataBinding,
        item: SoundResponse,
        position: Int,
    ) {
        if (binding is ItemSoundBinding) {
            binding.apply {
                // Set sound name - use localized string for "No Sound"
                tvSoundName.text =
                    if (item.id == Constant.NO_SOUND_ID) {
                        binding.root.context.getString(R.string.no_sound)
                    } else {
                        item.name.ifEmpty { "Unknown Sound" }
                    }

                // Set duration - for "No Sound", show empty or special text
                /*tvDuration.text = if (item.id == Constant.NO_SOUND_ID) {
                    ""
                } else {
                    item.duration.ifEmpty { Constant.DEFAULT_DISPLAY_TIME }
                }*/

                // Set selection state - for "No Sound", check if selectedSoundUrl is null or empty
                val isSelected =
                    if (item.id == Constant.NO_SOUND_ID) {
                        selectedSoundUrl.isNullOrEmpty()
                    } else {
                        item.fileUrl == selectedSoundUrl
                    }
                rbSelect.isChecked = isSelected

                // Load thumbnail image - use special icon for "No Sound"
                if (item.id == Constant.NO_SOUND_ID) {
                    // Use a special "no sound" icon
                    ivThumbnail.setImageResource(R.drawable.ic_no_sound)
                } else if (item.thumbnailUrl.isNotEmpty()) {
                    Glide
                        .with(ivThumbnail.context)
                        .load(item.thumbnailUrl)
                        .placeholder(R.drawable.ic_music_note)
                        .error(R.drawable.ic_music_note)
                        .centerCrop()
                        .into(ivThumbnail)
                } else {
                    ivThumbnail.setImageResource(R.drawable.ic_music_note)
                }

                // Set click listener for item selection
                root.setPreventDoubleClick {
                    onSoundClick(item)
                }

                // Set up Play/Pause button
                setupPlayPauseButton(binding, item)

                // Update card appearance based on selection
                if (isSelected) {
                    tvSoundName.changeTextColor(R.color.blue_2fafff)
                    // tvDuration.changeTextColor(R.color.blue_2fafff)
                } else {
                    tvSoundName.changeTextColor(R.color.black_35496d)
                    // tvDuration.changeTextColor(R.color.black_35496d)
                }
            }
        }

        if (binding is ViewItemNativeSoundBinding) {
            binding.layoutAds.post {
                val view =
                    LayoutInflater
                        .from(binding.root.context)
                        .inflate(R.layout.layout_native_sound_list, null)
                fragment?.showLoadedNative(
                    spaceNameConfig = item.adsItem?.configName ?: "",
                    spaceName = item.adsItem?.admobIdName ?: "",
                    viewAdsInflateFromXml = view,
                    ratioView = "328:64",
                    layoutToAttachAds = binding.adViewGroup,
                    layoutContainAds = binding.layoutAds,
                    onAdsClick = {},
                )
            }
        }
    }

    private fun setupPlayPauseButton(
        binding: ItemSoundBinding,
        item: SoundResponse,
    ) {
        binding.apply {
            // Hide play/pause button for "No Sound" item
            if (item.id == Constant.NO_SOUND_ID) {
                btnPlayPause.isVisible = false
                return
            }

            btnPlayPause.isVisible = true

            val isCurrentlyPlaying = currentPlayingUrl == item.fileUrl
            val isLoading =
                isCurrentlyPlaying && playingState is SoundPlayerManager.PlayingState.Loading
            val isPlaying =
                isCurrentlyPlaying && playingState is SoundPlayerManager.PlayingState.Playing

            // Show/hide loading spinner
            progressLoading.isVisible = isLoading
            ivPlayPause.isVisible = !isLoading

            // Set play/pause icon
            if (!isLoading) {
                ivPlayPause.setImageResource(
                    if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play,
                )
            }

            // Set click listener for play/pause button
            btnPlayPause.setPreventDoubleClick {
                onPlayPauseClick(item)
            }
        }
    }

    fun setSelectedSoundUrl(soundUrl: String?) {
        val previousSelectedUrl = selectedSoundUrl
        selectedSoundUrl = soundUrl

        // Notify changes for items that match previous or current selected URL
        if (previousSelectedUrl != soundUrl) {
            currentList.forEachIndexed { index, soundResponse ->
                // Handle "No Sound" case
                val shouldUpdate =
                    if (soundResponse.id == Constant.NO_SOUND_ID) {
                        // Update "No Sound" item if selection changed to/from null/empty
                        (previousSelectedUrl.isNullOrEmpty() != soundUrl.isNullOrEmpty())
                    } else {
                        // Update regular sound items
                        soundResponse.fileUrl == previousSelectedUrl || soundResponse.fileUrl == selectedSoundUrl
                    }

                if (shouldUpdate) {
                    notifyItemChanged(index)
                }
            }
        }
    }

    fun updatePlayingState(
        playingUrl: String?,
        state: SoundPlayerManager.PlayingState,
    ) {
        val previousPlayingUrl = currentPlayingUrl
        currentPlayingUrl = playingUrl
        playingState = state

        // Update items that were previously playing or are currently playing
        currentList.forEachIndexed { index, soundResponse ->
            val shouldUpdate =
                soundResponse.fileUrl == previousPlayingUrl ||
                    soundResponse.fileUrl == currentPlayingUrl
            if (shouldUpdate) {
                notifyItemChanged(index)
            }
        }
    }

    companion object {
        const val ITEM_TYPE_ADS = 1
        const val ITEM_TYPE_NORMAL = 0
    }
}
