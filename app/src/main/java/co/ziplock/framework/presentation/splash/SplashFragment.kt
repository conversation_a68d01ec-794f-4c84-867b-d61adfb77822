package co.ziplock.framework.presentation.splash

import android.view.View
import co.ziplock.databinding.FragmentSplashBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.util.Constant
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SplashFragment :
    BaseFragment<FragmentSplashBinding, SplashViewModel>(
        FragmentSplashBinding::inflate,
        SplashViewModel::class.java,
    ) {
    @Inject
    lateinit var securityManager: SecurityManager

    var countClickIconApp = 0

    override fun init(view: View) {
        startAnimation()
        if (Constant.isLanguageToSplash) {
            showAds()
            return
        }
        initView()
        backEvent()
    }

    override fun subscribeObserver(view: View) {
        if (Constant.isLanguageToSplash) return
        observerIapRemoteData()
    }
}
