package co.ziplock.framework.presentation.splash

import androidx.lifecycle.lifecycleScope
import co.ziplock.BuildConfig
import co.ziplock.R
import co.ziplock.framework.presentation.common.launchIO
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.remoteConfig.RemoteConfigData
import co.ziplock.util.Constant
import co.ziplock.util.displayToast
import co.ziplock.util.haveNetworkConnection
import co.ziplock.util.safeDelay
import com.example.libiap.IAPConnector
import com.example.libiap.utils.StateCheckIap
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import pion.datlt.libads.AdsController
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showSplashInter
import pion.datlt.libads.utils.loadAndShowConsentFormIfRequire
import pion.datlt.libads.utils.requestConsentInfoUpdate

fun SplashFragment.backEvent() {
    onSystemBackEvent {
    }
}

fun SplashFragment.initView() {
    commonViewModel.fetchRemoteConfigData()
    securityManager.resetFailedAttemptsWhenOpenApp()
    commonViewModel.loadAllTemplateDataWithCategories()
    binding.txtAppName.setOnClickListener {
        if (BuildConfig.DEBUG && countClickIconApp >= 5) {
            displayToast("User active OTP Test Mode")
            prefUtil.testOTPMode = true
        } else {
            countClickIconApp++
        }
    }
}

fun SplashFragment.startAnimation() {
    binding.loadingView.startAnim(20000L) {
    }
}

fun SplashFragment.goToLanguageScreen() {
    if (isPremium() || Constant.isLanguageToSplash) {
        Constant.isLanguageToSplash = false
        safeNavInter(R.id.splashFragment, R.id.action_splashFragment_to_homeFragment)
    } else {
        safeNavInter(R.id.splashFragment, R.id.action_splashFragment_to_languageFragment)
    }
}

fun SplashFragment.observerIapRemoteData() {
    if (context?.haveNetworkConnection() != true) {
        goToLanguageScreen()
        return
    }
    combine(
        IAPConnector.stateCheckIap,
        commonViewModel.remoteConfigDataStateFlow,
    ) { stateCheckIap, remoteConfigData ->
        // Only proceed when both IAP check is complete and remote config data is available
        if (stateCheckIap !in
            listOf(
                StateCheckIap.DONE,
                StateCheckIap.FAILED,
            ) ||
            remoteConfigData == null
        ) {
            return@combine
        }

        // Update premium status based on IAP check result
        updatePremiumStatus(stateCheckIap == StateCheckIap.DONE)

        // Process remote config data
        handlerLogicRemoteConfig(remoteConfigData)
    }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun SplashFragment.handlerLogicRemoteConfig(remoteConfigData: RemoteConfigData?) {
    if (remoteConfigData == null) return

    mapRemoteConfigData(remoteConfigData)

    AdsController.getInstance().requestConsentInfoUpdate(
        onFailed = { _ -> showAds() },
        onSuccess = { isRequire, _ ->
            if (isRequire) {
                AdsController.getInstance().loadAndShowConsentFormIfRequire(
                    onConsentError = { _ -> showAds() },
                    onConsentDone = { showAds() },
                )
            } else {
                showAds()
            }
        },
    )
}

fun SplashFragment.mapRemoteConfigData(data: RemoteConfigData) {
    runCatching {
        AdsController.setConfigAds(data.configShowAds)
    }
    runCatching {
        AdsController.getInstance().setListAdsData(listJsonData = arrayListOf(data.admobId))
    }
    Constant.isRemoteConfigSuccess = data.isRealData
    Constant.isShowOnboard1 = data.showOnboarding1Fragment
    Constant.isShowOnboard2 = data.showOnboarding2Fragment
    Constant.isShowOnboard3 = data.showOnboarding3Fragment
    Constant.isShowOnboard4 = data.showOnboarding4Fragment
    Constant.numberOfContentBetweenThemeList = data.numberOfContentBetweenThemeList
    Constant.numberOfContentBetweenZipList = data.numberOfContentBetweenZipList
    Constant.numberOfContentBetweenRowList = data.numberOfContentBetweenRowList
    Constant.numberOfContentBetweenBackgroundList = data.numberOfContentBetweenBackgroundList
    Constant.numberOfContentBetweenWallpaperList = data.numberOfContentBetweenWallpaperList
    Constant.numberOfContentBetweenSoundList = data.numberOfContentBetweenSoundList
    initNativeFullAfterInter()
}

fun SplashFragment.initNativeFullAfterInter() {
    if (activity == null) return
    AdsController.getInstance().initNativeInter(
        activity = activity!!,
        configName = "afterinterstitial",
        listSpaceName = listOf("afterinterstitial_native1", "afterinterstitial_native2"),
    )
}

private fun SplashFragment.updatePremiumStatus(isIapCheckSuccessful: Boolean) {
    if (isIapCheckSuccessful) {
        val productModel = IAPConnector.getAllProductModel().find { it.isPurchase }
        val isPremium = productModel?.isPurchase == true
        Constant.isPremium = isPremium
        prefUtil.isPremium = isPremium
        AdsConstant.isPremium = isPremium
    } else {
        Constant.isPremium = false
        prefUtil.isPremium = false
        AdsConstant.isPremium = false
    }
}

fun SplashFragment.showAds() {
    startFacebookMMP()
    safePreloadAds(spaceNameConfig = "Splash", spaceNameAds = "splash_intertitial1")
    safePreloadAds(spaceNameConfig = "Splash", spaceNameAds = "splash_intertitial2")
    safePreloadAds(spaceNameConfig = "Splash", spaceNameAds = "splash_openad3")
    preloadAdsLanguage()

    val navigateToNextScreen = {
        goToLanguageScreen()
    }

    safeDelay(15000L) {
        navigateToNextScreen.invoke()
    }

    if (view != null && isAdded) {
        runCatching {
            showSplashInter(
                spaceNameConfig = "Splash",
                spaceNameInter1 = "splash_intertitial1",
                spaceNameInter2 = "splash_intertitial2",
                spaceNameOpenAds = "splash_openad3",
                timeOut = 15000L,
                destinationToShowAds = R.id.splashFragment,
                navOrBack = {
                    navigateToNextScreen()
                },
            )
        }
    }
}

fun SplashFragment.preloadAdsLanguage() {
    if (Constant.isLanguageToSplash) return
    val languageAds11ConfigName = "Language1.1"
    val languageAds12ConfigName = "Language1.2"
    val languageAds21ConfigName = "Language2.1"
    val languageAds22ConfigName = "Language2.2"
    safePreloadAds(spaceNameConfig = languageAds11ConfigName, spaceNameAds = "language1_native1")
    safePreloadAds(spaceNameConfig = languageAds11ConfigName, spaceNameAds = "language1_native2")
    safePreloadAds(spaceNameConfig = languageAds11ConfigName, spaceNameAds = "language1_native3")

    if (AdsConstant.listConfigAds[languageAds11ConfigName]?.isOn == true) {
        safePreloadAds(
            spaceNameConfig = languageAds12ConfigName,
            spaceNameAds = "language1_native4",
        )
        safePreloadAds(
            spaceNameConfig = languageAds12ConfigName,
            spaceNameAds = "language1_native5",
        )
        safePreloadAds(
            spaceNameConfig = languageAds12ConfigName,
            spaceNameAds = "language1_native6",
        )
    }
    safePreloadAds(spaceNameConfig = languageAds21ConfigName, spaceNameAds = "Language2.1_native")

    if (AdsConstant.listConfigAds[languageAds21ConfigName]?.isOn == true) {
        safePreloadAds(
            spaceNameConfig = languageAds22ConfigName,
            spaceNameAds = "Language2.2_native",
        )
    }
}

fun SplashFragment.startFacebookMMP() {
//    FacebookSdk.apply {
//        setAutoInitEnabled(true)
//        fullyInitialize()
//        setAutoLogAppEventsEnabled(true)
//        setAdvertiserIDCollectionEnabled(true)
//
//        if (BuildConfig.DEBUG) {
//            setIsDebugEnabled(BuildConfig.DEBUG)
//            addLoggingBehavior(LoggingBehavior.APP_EVENTS)
//        }
//    }
}
