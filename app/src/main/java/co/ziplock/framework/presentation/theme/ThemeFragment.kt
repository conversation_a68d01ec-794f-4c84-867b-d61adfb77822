package co.ziplock.framework.presentation.theme

import android.view.View
import android.widget.AdapterView
import co.ziplock.R
import co.ziplock.databinding.FragmentThemeBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.framework.presentation.model.theme.ThemeCategoryModel
import co.ziplock.framework.presentation.theme.adapter.ThemeCategorySpinnerAdapter
import co.ziplock.framework.presentation.theme.adapter.ThemeGridAdapter
import co.ziplock.framework.presentation.theme.dialog.RewardContentDialog
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import dagger.hilt.android.AndroidEntryPoint
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.showLoadedRewardVideo

@AndroidEntryPoint
class ThemeFragment :
    BaseFragment<FragmentThemeBinding, ThemeViewModel>(
        FragmentThemeBinding::inflate,
        ThemeViewModel::class.java,
    ),
    ThemeGridAdapter.Listener,
    RewardContentDialog.Listener {

    private val themeGridAdapter = ThemeGridAdapter()
    private var categorySpinnerAdapter: ThemeCategorySpinnerAdapter? = null
    var currentTheme: HotTrendingThemeModel? = null

    override fun init(view: View) {
        setupBackEvent()
        setupRecyclerView()
        setupCategorySpinner()
        setupErrorHandling()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeHotTrendingThemes()
    }

    override fun onThemeClick(theme: HotTrendingThemeModel) {
        currentTheme = theme
        if (checkConditionShowAds(
                context = requireContext(),
                spaceNameConfig = "themepro",
            ) &&
            theme.isPro &&
            !commonViewModel.isHotTrendingThemeUnlocked(theme.id)
        ) {
            val dialog = RewardContentDialog()
            dialog.setListener(this)
            dialog.show(childFragmentManager)
        } else {
            showInterWhenClickTheme()
        }
    }

    override fun onSeeMoreClick() {
        loadMoreThemes()
    }

    override fun onWatchVideoEvent() {
        showLoadedRewardVideo(
            spaceNameConfig = "themepro",
            spaceName = "themepro_rewarded",
            destinationToShowAds = R.id.themeFragment,
            isShowLoadingView = true,
            isScreenType = false,
            onRewardDone = { isSuccess ->
                if (!isSuccess) {
                    showInterWhenClickTheme()
                }
            },
            onGetReward = {
                if (currentTheme == null) {
                    displayToast(R.string.something_error)
                    return@showLoadedRewardVideo
                }
                onThemeClick(currentTheme!!)
            },
        )
    }

    override fun onBuyVipVersion() {
        safeNav(R.id.themeFragment, R.id.action_to_iapFragment)
    }

    companion object {
        const val TAG = "ThemeFragment"
    }
}
