package co.ziplock.framework.presentation.theme

import android.view.View
import co.ziplock.R
import co.ziplock.databinding.FragmentThemeBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.home.adapter.HotTrendingThemeModelAdapter
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.framework.presentation.theme.dialog.RewardContentDialog
import co.ziplock.util.displayToast
import dagger.hilt.android.AndroidEntryPoint
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.showLoadedRewardVideo

@AndroidEntryPoint
class ThemeFragment :
    BaseFragment<FragmentThemeBinding, ThemeViewModel>(
        FragmentThemeBinding::inflate,
        ThemeViewModel::class.java,
    ),
    HotTrendingThemeModelAdapter.Listener,
    RewardContentDialog.Listener {
    val hotTrendingAdapter = HotTrendingThemeModelAdapter()
    var currentTheme: HotTrendingThemeModel? = null

    override fun init(view: View) {
        setupBackEvent()
        setupRecyclerView()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeHotTrendingThemes()
    }

    override fun onTryNowClick(item: HotTrendingThemeModel) {
        currentTheme = item
        if (checkConditionShowAds(
                context = requireContext(),
                spaceNameConfig = "themepro",
            ) &&
            item.isPro &&
            !commonViewModel.isHotTrendingThemeUnlocked(item.id)
        ) {
            val dialog = RewardContentDialog()
            dialog.setListener(this)
            dialog.show(childFragmentManager)
        } else {
            showInterWhenClickTheme()
        }
    }

    override fun onWatchVideoEvent() {
        showLoadedRewardVideo(
            spaceNameConfig = "themepro",
            spaceName = "themepro_rewarded",
            destinationToShowAds = R.id.themeFragment,
            isShowLoadingView = true,
            isScreenType = false,
            onRewardDone = { isSuccess ->
                if (!isSuccess) {
                    showInterWhenClickTheme()
                }
            },
            onGetReward = {
                if (currentTheme == null) {
                    displayToast(R.string.something_error)
                    return@showLoadedRewardVideo
                }
                onThemeClick(currentTheme!!)
            },
        )
    }

    override fun onBuyVipVersion() {
        safeNav(R.id.themeFragment, R.id.action_to_iapFragment)
    }

    companion object {
        const val TAG = "ThemeFragment"
    }
}
