package co.ziplock.framework.presentation.theme

import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.framework.presentation.model.theme.ThemeCategoryModel
import co.ziplock.framework.presentation.theme.adapter.ThemeCategorySpinnerAdapter
import co.ziplock.util.BundleKey
import co.ziplock.util.safeNavInter
import co.ziplock.util.Constant
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun ThemeFragment.setupBackEvent() {
    binding.btnBack.setPreventDoubleClick {
        findNavController().navigateUp()
    }

    onSystemBackEvent {
        findNavController().navigateUp()
    }
}

fun ThemeFragment.showInterWhenClickTheme() {
    showLoadedInter(
        spaceNameConfig = "theme-nopro",
        spaceName = "content_1ID_interstitial",
        destinationToShowAds = R.id.themeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            if (currentTheme == null) {
                displayToast(R.string.something_error)
                return@showLoadedInter
            }
            onThemeClick(currentTheme!!)
        },
        onCloseAds = {},
    )
}

fun ThemeFragment.setupRecyclerView() {
    themeGridAdapter.setListener(this)

    val gridLayoutManager = GridLayoutManager(requireContext(), 2)
    gridLayoutManager.spanSizeLookup =
        object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                // See more button spans full width
                return if (themeGridAdapter.getItemViewType(position) == 1) 2 else 1
            }
        }

    binding.rvThemes.apply {
        layoutManager = gridLayoutManager
        adapter = themeGridAdapter
    }
}

fun ThemeFragment.onThemeClick(theme: HotTrendingThemeModel) {
    // Apply theme and navigate to edit layout
    if (theme.isPro) {
        commonViewModel.addUnlockedHotTrendingTheme(theme.id ?: "")
    }
    applyTheme(theme)

    // Create bundle with theme data
    val bundle =
        Bundle().apply {
            theme.zipper?.let { putParcelable(BundleKey.ZIPPER_DATA, it) }
            theme.wallpaper?.let { putParcelable(BundleKey.WALLPAPER_DATA, it) }
            theme.background?.let { putParcelable(BundleKey.BACKGROUND_DATA, it) }
            theme.sound?.let { putParcelable(BundleKey.SOUND_DATA, it) }
            theme.row?.let { putParcelable(BundleKey.ROW_DATA, it) }
        }
    safeNavInter(R.id.themeFragment, R.id.action_themeFragment_to_editLayoutFragment, bundle)
}

fun ThemeFragment.applyTheme(theme: HotTrendingThemeModel) {
    // Apply theme using CommonViewModel and PrefUtil
    theme.zipper?.let {
        commonViewModel.setSelectedZipperResponse(it)
    }

    theme.wallpaper?.let {
        commonViewModel.setSelectedWallpaperImagePath(it)
    }

    theme.background?.let {
        commonViewModel.setSelectedBackgroundImagePath(it)
    }

    theme.sound?.let {
        commonViewModel.setSelectedSound(it)
    }

    theme.row?.let {
        commonViewModel.setSelectedRowResponse(it)
    }

    // Show toast to confirm theme selection
    Toast
        .makeText(
            requireContext(),
            "Theme applied successfully!",
            Toast.LENGTH_SHORT,
        ).show()
}

fun ThemeFragment.setupCategorySpinner() {
    // Initialize with default categories
    val defaultCategories = ThemeCategoryModel.getDefaultCategories()
    categorySpinnerAdapter = ThemeCategorySpinnerAdapter(requireContext(), defaultCategories)
    binding.spinnerCategory.adapter = categorySpinnerAdapter

    binding.spinnerCategory.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
        override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
            val selectedCategory = defaultCategories[position]
            filterThemesByCategory(selectedCategory)
        }

        override fun onNothingSelected(parent: AdapterView<*>?) {}
    }
}

fun ThemeFragment.setupErrorHandling() {
    binding.btnTryAgain.setPreventDoubleClick {
        commonViewModel.loadAllTemplateDataWithCategories()
    }
}

fun ThemeFragment.filterThemesByCategory(category: ThemeCategoryModel) {
    val currentState = commonViewModel.hotTrendingUiState.value
    val filteredThemes = if (category.id == ThemeCategoryModel.getAllCategories()) {
        currentState.hotTrendingThemes
    } else {
        currentState.hotTrendingThemes.filter { theme ->
            theme.category?.equals(category.name, ignoreCase = true) == true
        }
    }

    // Reset pagination and show first 12 items
    val itemsToShow = filteredThemes.take(12)
    val hasMore = filteredThemes.size > 12

    themeGridAdapter.updateThemes(itemsToShow, hasMore)

    // Update UI state (this would be better handled in ViewModel)
    // For now, we'll manage pagination locally
}

fun ThemeFragment.loadMoreThemes() {
    val currentState = commonViewModel.hotTrendingUiState.value
    val selectedCategory = categorySpinnerAdapter?.let { adapter ->
        val selectedPosition = binding.spinnerCategory.selectedItemPosition
        if (selectedPosition >= 0 && selectedPosition < adapter.count) {
            adapter.getItem(selectedPosition)
        } else null
    } ?: ThemeCategoryModel.getDefaultCategories().first()

    val filteredThemes = if (selectedCategory.id == ThemeCategoryModel.getAllCategories()) {
        currentState.hotTrendingThemes
    } else {
        currentState.hotTrendingThemes.filter { theme ->
            theme.category?.equals(selectedCategory.name, ignoreCase = true) == true
        }
    }

    val currentThemeCount = themeGridAdapter.itemCount - 1 // Subtract see more button
    val nextItems = filteredThemes.drop(currentThemeCount).take(12)
    val hasMore = filteredThemes.size > currentThemeCount + nextItems.size

    if (nextItems.isNotEmpty()) {
        themeGridAdapter.addMoreThemes(nextItems, hasMore)
    }
}

fun ThemeFragment.observeHotTrendingThemes() {
    commonViewModel.hotTrendingUiState
        .onEach { uiState ->
            handleLoadingState(uiState.isLoading)
            handleErrorState(uiState.error)

            if (uiState.hotTrendingThemes.isNotEmpty()) {
                // Initialize with first 12 themes
                val initialThemes = uiState.hotTrendingThemes.take(12)
                val hasMore = uiState.hotTrendingThemes.size > 12
                themeGridAdapter.updateThemes(initialThemes, hasMore)
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ThemeFragment.handleLoadingState(isLoading: Boolean) {
    binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
    binding.rvThemes.visibility = if (isLoading) View.GONE else View.VISIBLE
    binding.layoutError.visibility = View.GONE
}

fun ThemeFragment.handleErrorState(error: Throwable?) {
    if (error != null) {
        binding.progressBar.visibility = View.GONE
        binding.rvThemes.visibility = View.GONE
        binding.layoutError.visibility = View.VISIBLE
    } else {
        binding.layoutError.visibility = View.GONE
    }
}

fun ThemeFragment.filterListAds(rawList: List<HotTrendingThemeModel>): List<HotTrendingThemeModel> {
    val tag = "filterListAdsTheme"
    if (!checkConditionShowAds(
            context = requireContext(),
            spaceNameConfig = "listtheme-ct",
        )
    ) {
        Timber.tag(tag).d("rawList $rawList")
        return rawList
    }

    val editedList = mutableListOf<HotTrendingThemeModel>()
    var count = 0
    var isLastTimeAddAds1 = false

    val itemAds1 =
        AdsItem(
            configName = "listtheme-ct",
            admobIdName = "listtheme-ct_native1",
        )
    val hotItemAds1 = HotTrendingThemeModel(adsItem = itemAds1)

    val itemAds2 =
        AdsItem(
            configName = "listtheme-ct",
            admobIdName = "listtheme-ct_native2",
        )

    val hotItemAds2 = HotTrendingThemeModel(adsItem = itemAds2)

    rawList.forEachIndexed { index, item ->
        editedList.add(item)
        count++
        if (index == 2 || count == Constant.numberOfContentBetweenThemeList * 3) {
            if (!isLastTimeAddAds1) {
                editedList.add(hotItemAds1)
                isLastTimeAddAds1 = true
            } else {
                editedList.add(hotItemAds2)
                isLastTimeAddAds1 = false
            }
            count = 0
        }
    }
    Timber.tag(tag).d("editedList $editedList")
    return editedList
}

fun ThemeFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "themepro",
        spaceNameAds = "themepro_rewarded",
    )

    safePreloadAds(
        spaceNameConfig = "theme-nopro",
        spaceNameAds = "content_1ID_interstitial",
    )

    safePreloadAds(
        spaceNameConfig = "listtheme-ct",
        spaceNameAds = "listtheme-ct_native1",
        includeHasBeenOpened = false,
    )

    safePreloadAds(
        spaceNameConfig = "listtheme-ct",
        spaceNameAds = "listtheme-ct_native2",
        includeHasBeenOpened = false,
    )

    showLoadedNative(
        spaceNameConfig = "listtheme",
        spaceName = "listtheme_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
