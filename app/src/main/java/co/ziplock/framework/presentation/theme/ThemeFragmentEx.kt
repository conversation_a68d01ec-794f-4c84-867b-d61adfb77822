package co.ziplock.framework.presentation.theme

import android.os.Bundle
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun ThemeFragment.setupBackEvent() {
    binding.btnBack.setPreventDoubleClick {
        findNavController().navigateUp()
    }

    onSystemBackEvent {
        findNavController().navigateUp()
    }
}

fun ThemeFragment.showInterWhenClickTheme() {
    showLoadedInter(
        spaceNameConfig = "theme-nopro",
        spaceName = "content_1ID_interstitial",
        destinationToShowAds = R.id.themeFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            if (currentTheme == null) {
                displayToast(R.string.something_error)
                return@showLoadedInter
            }
            onThemeClick(currentTheme!!)
        },
        onCloseAds = {},
    )
}

fun ThemeFragment.setupRecyclerView() {
    hotTrendingAdapter.setListener(this)
    hotTrendingAdapter.setFragment(this)

    val gridLayoutManager = GridLayoutManager(requireContext(), 3)
    gridLayoutManager.spanSizeLookup =
        object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                val item = hotTrendingAdapter.currentList.getOrNull(position)
                return if (item?.adsItem != null) 3 else 1
            }
        }

    binding.rvThemes.apply {
        layoutManager = gridLayoutManager
        adapter = hotTrendingAdapter
    }
}

fun ThemeFragment.onThemeClick(theme: HotTrendingThemeModel) {
    // Apply theme and navigate to edit layout
    if (theme.isPro) {
        commonViewModel.addUnlockedHotTrendingTheme(theme.id ?: "")
    }
    applyTheme(theme)

    // Create bundle with theme data
    val bundle =
        Bundle().apply {
            theme.zipper?.let { putParcelable(BundleKey.ZIPPER_DATA, it) }
            theme.wallpaper?.let { putParcelable(BundleKey.WALLPAPER_DATA, it) }
            theme.background?.let { putParcelable(BundleKey.BACKGROUND_DATA, it) }
            theme.sound?.let { putParcelable(BundleKey.SOUND_DATA, it) }
            theme.row?.let { putParcelable(BundleKey.ROW_DATA, it) }
        }
    safeNavInter(R.id.themeFragment, R.id.action_themeFragment_to_editLayoutFragment, bundle)
}

fun ThemeFragment.applyTheme(theme: HotTrendingThemeModel) {
    // Apply theme using CommonViewModel and PrefUtil
    theme.zipper?.let {
        commonViewModel.setSelectedZipperResponse(it)
    }

    theme.wallpaper?.let {
        commonViewModel.setSelectedWallpaperImagePath(it)
    }

    theme.background?.let {
        commonViewModel.setSelectedBackgroundImagePath(it)
    }

    theme.sound?.let {
        commonViewModel.setSelectedSound(it)
    }

    theme.row?.let {
        commonViewModel.setSelectedRowResponse(it)
    }

    // Show toast to confirm theme selection
    Toast
        .makeText(
            requireContext(),
            "Theme applied successfully!",
            Toast.LENGTH_SHORT,
        ).show()
}

fun ThemeFragment.observeHotTrendingThemes() {
    commonViewModel.hotTrendingUiState
        .map { it.hotTrendingThemes }
        .distinctUntilChanged()
        .onEach { themes ->
            hotTrendingAdapter.submitList(filterListAds(themes))
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ThemeFragment.filterListAds(rawList: List<HotTrendingThemeModel>): List<HotTrendingThemeModel> {
    val tag = "filterListAdsTheme"
    if (!checkConditionShowAds(
            context = requireContext(),
            spaceNameConfig = "listtheme-ct",
        )
    ) {
        Timber.tag(tag).d("rawList $rawList")
        return rawList
    }

    val editedList = mutableListOf<HotTrendingThemeModel>()
    var count = 0
    var isLastTimeAddAds1 = false

    val itemAds1 =
        AdsItem(
            configName = "listtheme-ct",
            admobIdName = "listtheme-ct_native1",
        )
    val hotItemAds1 = HotTrendingThemeModel(adsItem = itemAds1)

    val itemAds2 =
        AdsItem(
            configName = "listtheme-ct",
            admobIdName = "listtheme-ct_native2",
        )

    val hotItemAds2 = HotTrendingThemeModel(adsItem = itemAds2)

    rawList.forEachIndexed { index, item ->
        editedList.add(item)
        count++
        if (index == 2 || count == Constant.numberOfContentBetweenThemeList * 3) {
            if (!isLastTimeAddAds1) {
                editedList.add(hotItemAds1)
                isLastTimeAddAds1 = true
            } else {
                editedList.add(hotItemAds2)
                isLastTimeAddAds1 = false
            }
            count = 0
        }
    }
    Timber.tag(tag).d("editedList $editedList")
    return editedList
}

fun ThemeFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "themepro",
        spaceNameAds = "themepro_rewarded",
    )

    safePreloadAds(
        spaceNameConfig = "theme-nopro",
        spaceNameAds = "content_1ID_interstitial",
    )

    safePreloadAds(
        spaceNameConfig = "listtheme-ct",
        spaceNameAds = "listtheme-ct_native1",
        includeHasBeenOpened = false,
    )

    safePreloadAds(
        spaceNameConfig = "listtheme-ct",
        spaceNameAds = "listtheme-ct_native2",
        includeHasBeenOpened = false,
    )

    showLoadedNative(
        spaceNameConfig = "listtheme",
        spaceName = "listtheme_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
