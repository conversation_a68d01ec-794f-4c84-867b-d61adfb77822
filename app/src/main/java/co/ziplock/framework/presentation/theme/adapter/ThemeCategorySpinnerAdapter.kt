package co.ziplock.framework.presentation.theme.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import co.ziplock.R
import co.ziplock.framework.presentation.model.theme.ThemeCategoryModel

class ThemeCategorySpinnerAdapter(
    private val context: Context,
    private val categories: List<ThemeCategoryModel>
) : BaseAdapter() {

    private val inflater: LayoutInflater = LayoutInflater.from(context)

    override fun getCount(): Int = categories.size

    override fun getItem(position: Int): ThemeCategoryModel = categories[position]

    override fun getItemId(position: Int): Long = position.toLong()

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: inflater.inflate(R.layout.item_spinner_category, parent, false)
        val textView = view.findViewById<TextView>(R.id.tvCategoryName)
        textView.text = categories[position].displayName
        return view
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: inflater.inflate(R.layout.item_spinner_category_dropdown, parent, false)
        val textView = view.findViewById<TextView>(R.id.tvCategoryName)
        textView.text = categories[position].displayName
        return view
    }
}