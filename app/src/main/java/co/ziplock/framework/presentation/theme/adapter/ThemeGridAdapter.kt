package co.ziplock.framework.presentation.theme.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import co.ziplock.R
import co.ziplock.databinding.ItemThemeGridBinding
import co.ziplock.databinding.ItemSeeMoreBinding
import co.ziplock.framework.presentation.model.home.HotTrendingThemeModel
import com.bumptech.glide.Glide

class ThemeGridAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_THEME = 0
        private const val VIEW_TYPE_SEE_MORE = 1
        private const val VIEW_TYPE_LOADING = 2
    }

    private val themes = mutableListOf<HotTrendingThemeModel>()
    private var showSeeMore = false
    private var isLoading = false
    private var listener: Listener? = null

    interface Listener {
        fun onThemeClick(theme: HotTrendingThemeModel)
        fun onSeeMoreClick()
    }

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    fun updateThemes(newThemes: List<HotTrendingThemeModel>, showSeeMore: Boolean = false) {
        this.themes.clear()
        this.themes.addAll(newThemes)
        this.showSeeMore = showSeeMore
        this.isLoading = false
        notifyDataSetChanged()
    }

    fun addMoreThemes(newThemes: List<HotTrendingThemeModel>, showSeeMore: Boolean = false) {
        val startPosition = themes.size
        this.themes.addAll(newThemes)
        this.showSeeMore = showSeeMore
        this.isLoading = false
        notifyItemRangeInserted(startPosition, newThemes.size)
        if (showSeeMore) {
            notifyItemChanged(itemCount - 1) // Update see more button
        }
    }

    fun setLoading(loading: Boolean) {
        this.isLoading = loading
        if (showSeeMore) {
            notifyItemChanged(itemCount - 1)
        }
    }

    override fun getItemCount(): Int {
        return themes.size + if (showSeeMore) 1 else 0
    }

    override fun getItemViewType(position: Int): Int {
        return when {
            position < themes.size -> VIEW_TYPE_THEME
            isLoading -> VIEW_TYPE_LOADING
            else -> VIEW_TYPE_SEE_MORE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_THEME -> {
                val binding = ItemThemeGridBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ThemeViewHolder(binding)
            }
            VIEW_TYPE_SEE_MORE, VIEW_TYPE_LOADING -> {
                val binding = ItemSeeMoreBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SeeMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ThemeViewHolder -> {
                holder.bind(themes[position])
            }
            is SeeMoreViewHolder -> {
                holder.bind(isLoading)
            }
        }
    }

    inner class ThemeViewHolder(private val binding: ItemThemeGridBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(theme: HotTrendingThemeModel) {
            // Load thumbnail image
            Glide.with(binding.root.context)
                .load(theme.previewImageUrl)
                .placeholder(R.drawable.placeholder_theme)
                .error(R.drawable.placeholder_theme)
                .into(binding.ivThumbnail)

            // Show pro badge if needed
            binding.ivProBadge.visibility = if (theme.isPro) View.VISIBLE else View.GONE

            // Set click listener
            binding.root.setOnClickListener {
                listener?.onThemeClick(theme)
            }
        }
    }

    inner class SeeMoreViewHolder(private val binding: ItemSeeMoreBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(isLoading: Boolean) {
            if (isLoading) {
                binding.progressBar.visibility = View.VISIBLE
                binding.btnSeeMore.visibility = View.GONE
            } else {
                binding.progressBar.visibility = View.GONE
                binding.btnSeeMore.visibility = View.VISIBLE
                binding.btnSeeMore.setOnClickListener {
                    listener?.onSeeMoreClick()
                }
            }
        }
    }
}