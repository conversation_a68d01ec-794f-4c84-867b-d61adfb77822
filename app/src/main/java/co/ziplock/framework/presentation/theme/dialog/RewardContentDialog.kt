package co.ziplock.framework.presentation.theme.dialog

import android.os.Bundle
import co.ziplock.R
import co.ziplock.databinding.DialogRewardContentBinding
import co.ziplock.framework.presentation.common.BaseDialogFragment
import co.ziplock.util.setPreventDoubleClick

class RewardContentDialog : BaseDialogFragment<DialogRewardContentBinding>(R.layout.dialog_reward_content) {
    interface Listener {
        fun onWatchVideoEvent()

        fun onBuyVipVersion()
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    override fun getDialogFragmentInfo(): DialogFragmentInfo = DialogFragmentInfo()

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        binding.btnWatchVideoAd.setPreventDoubleClick {
            listener?.onWatchVideoEvent()
            dismiss()
        }

        binding.btnBuyVipVersion.setPreventDoubleClick {
            listener?.onBuyVipVersion()
            dismiss()
        }
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        binding.btnClose.setPreventDoubleClick {
            dismiss()
        }
    }
}
