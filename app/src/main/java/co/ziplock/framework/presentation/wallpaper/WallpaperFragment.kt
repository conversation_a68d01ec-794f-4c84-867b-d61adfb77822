package co.ziplock.framework.presentation.wallpaper

import android.view.View
import co.ziplock.databinding.FragmentWallpaperBinding
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.manager.PermissionManager
import co.ziplock.framework.presentation.wallpaper.adapter.WallpaperAdapter
import co.ziplock.util.BundleKey
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WallpaperFragment :
    BaseFragment<FragmentWallpaperBinding, WallpaperViewModel>(
        FragmentWallpaperBinding::inflate,
        WallpaperViewModel::class.java,
    ) {
    lateinit var wallpaperAdapter: WallpaperAdapter

    val permissionManager by lazy {
        PermissionManager(requireContext())
    }

    val isReEditFlow by lazy {
        arguments?.getBoolean(BundleKey.KEY_FROM_RE_EDIT_FLOW) ?: false
    }

    var isTabsInitialized = false

    override fun init(view: View) {
        setupRecyclerView()
        setupTabLayout()
        setupSystemBackEvent()
        setupBackButtonClickListener()
        setupFloatingActionButton()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeWallpaperCategories()
        observeWallpaperData()
        observeSelectedCategory()
        observeLoadingState()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isTabsInitialized = false
    }

    companion object {
        const val TAG = "WallpaperFragment"

        fun newInstance() = WallpaperFragment()
    }
}
