package co.ziplock.framework.presentation.wallpaper

import android.os.Bundle
import androidx.core.view.forEach
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.dialog.FilesPermissionRequiredDialogFragment
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.framework.presentation.model.wallpaper.WallpaperItem
import co.ziplock.framework.presentation.wallpaper.adapter.WallpaperAdapter
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.disableTooltipsForTabs
import co.ziplock.util.setPreventDoubleClick
import com.google.android.material.tabs.TabLayout
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber
import kotlin.collections.map

fun WallpaperFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun WallpaperFragment.setupSystemBackEvent() {
    onSystemBackEvent {
        backEvent()
    }
}

fun WallpaperFragment.backEvent() {
    findNavController().navigateUp()
}

fun WallpaperFragment.navigateToEditLayout(wallpaper: WallpaperResponse) {
    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.WALLPAPER_DATA, wallpaper)
        }
    safeNavInter(
        R.id.wallpaperFragment,
        R.id.action_wallpaperFragment_to_editLayoutFragment,
        bundle,
    )
}

fun WallpaperFragment.navigateToEditLayoutWithLocalWallpaper(localWallpaper: LocalImageData) {
    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.LOCAL_WALLPAPER_DATA, localWallpaper)
        }
    safeNavInter(
        R.id.wallpaperFragment,
        R.id.action_wallpaperFragment_to_editLayoutFragment,
        bundle,
    )
}

fun WallpaperFragment.navigateToPickPhoto() {
    showLoadedInter(
        spaceNameConfig = "listwall-gallery",
        spaceName = "list-1ID_interstitial",
        destinationToShowAds = R.id.wallpaperFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            val bundle =
                Bundle().apply {
                    putString(BundleKey.KEY_SAVE_CONTEXT, Constant.SAVE_CONTEXT_WALLPAPER)
                }
            safeNavInter(
                R.id.wallpaperFragment,
                R.id.action_wallpaperFragment_to_pickPhotoFragment,
                bundle,
            )
        },
        onCloseAds = {},
    )
}

fun WallpaperFragment.setupFloatingActionButton() {
    binding.fabFromGallery.setPreventDoubleClick {
        val isFilesPermissionGranted = permissionManager.isMediaPermissionGranted()
        if (isFilesPermissionGranted) {
            navigateToPickPhoto()
        } else {
            FilesPermissionRequiredDialogFragment
                .Builder()
                .setOnClickAllowButton {
                    permissionManager.requestPermission(
                        fragment = this,
                        permission = permissionManager.getMediaPermission(),
                        onAllPermissionGranted = {
                            // navigateToPickPhoto()
                        },
                    )
                }.build()
                .show(parentFragmentManager, WallpaperFragment.TAG)
        }
    }
}

fun WallpaperFragment.showInterChooseContent(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "listwall-choosect",
        spaceName = "listwall-choosect_interstitial",
        destinationToShowAds = R.id.wallpaperFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = action,
        onCloseAds = {},
    )
}

fun WallpaperFragment.setupRecyclerView() {
    wallpaperAdapter =
        WallpaperAdapter(
            onRemoteWallpaperClick = { wallpaper ->
                if (isReEditFlow) {
                    // If coming from EditLayoutFragment, set data to CommonViewModel and go back
                    showInterChooseContent {
                        commonViewModel.setEditLayoutWallpaper(wallpaper)
                        safeNavigateUp(R.id.wallpaperFragment)
                    }
                } else {
                    // Normal flow - set data to CommonViewModel and navigate to EditLayout
                    showInterChooseContent {
                        commonViewModel.setEditLayoutWallpaper(wallpaper)
                        navigateToEditLayout(wallpaper)
                    }
                }
            },
            onLocalWallpaperClick = { localWallpaper ->
                if (isReEditFlow) {
                    // If coming from EditLayoutFragment, set data to CommonViewModel and go back
                    showInterChooseContent {
                        commonViewModel.setEditLayoutLocalWallpaper(localWallpaper)
                        safeNavigateUp(R.id.wallpaperFragment)
                    }
                } else {
                    // Normal flow - set data to CommonViewModel and navigate to EditLayout
                    showInterChooseContent {
                        commonViewModel.setEditLayoutLocalWallpaper(localWallpaper)
                        navigateToEditLayoutWithLocalWallpaper(localWallpaper)
                    }
                }
            },
        )
    wallpaperAdapter.setFragment(this)

    val gridLayoutManager = GridLayoutManager(requireContext(), 3)
    gridLayoutManager.spanSizeLookup =
        object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                val item = wallpaperAdapter.currentList.getOrNull(position)
                return if (item is WallpaperItem.AdsWallpaperItem) 3 else 1
            }
        }

    binding.rvWallpapers.apply {
        layoutManager = gridLayoutManager
        adapter = wallpaperAdapter
    }
}

fun WallpaperFragment.setupTabLayout() {
    binding.tabLayout.disableTooltipsForTabs()

    binding.tabLayout.addOnTabSelectedListener(
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                val tabText = tab.text.toString()
                val category =
                    when (tabText) {
                        getString(R.string.all) -> Constant.ALL
                        getString(R.string.local_files) -> Constant.LOCAL_FILES
                        else -> tabText
                    }
                if (!isTabsInitialized) {
                    isTabsInitialized = true
                    commonViewModel.selectWallpaperCategory(category)
                } else {
                    showLoadedInter(
                        spaceNameConfig = "listwall-choosecate",
                        spaceName = "list-1ID_interstitial",
                        destinationToShowAds = R.id.wallpaperFragment,
                        isShowLoadingView = true,
                        isScreenType = false,
                        navOrBack = {
                            commonViewModel.selectWallpaperCategory(category)
                        },
                        onCloseAds = {},
                    )
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {}

            override fun onTabReselected(tab: TabLayout.Tab) {}
        },
    )
}

fun createListFromBackground(
    remoteWallpapers: List<WallpaperResponse>,
    localWallpapers: List<LocalImageData>,
    category: String,
): MutableList<WallpaperItem> {
    val items = mutableListOf<WallpaperItem>()

    when (category) {
        Constant.ALL -> {
            // Show both remote and local wallpapers
            items.addAll(remoteWallpapers.map { WallpaperItem.RemoteWallpaper(it) })
            items.addAll(localWallpapers.map { WallpaperItem.LocalWallpaperItem(it) })
        }

        Constant.LOCAL_FILES -> {
            // Show only local wallpapers
            items.addAll(localWallpapers.map { WallpaperItem.LocalWallpaperItem(it) })
        }

        else -> {
            // Show only remote wallpapers for specific categories
            items.addAll(remoteWallpapers.map { WallpaperItem.RemoteWallpaper(it) })
        }
    }
    return items
}

fun WallpaperFragment.filterListAds(rawList: List<WallpaperItem>): List<WallpaperItem> {
    val tag = "filterListWallpaper"
    if (!checkConditionShowAds(
            context = requireContext(),
            spaceNameConfig = "listwall-ct",
        )
    ) {
        Timber.tag(tag).d("rawList $rawList")
        return rawList
    }

    val editedList = mutableListOf<WallpaperItem>()
    var count = 0
    var isLastTimeAddAds1 = false

    val itemAds1 =
        AdsItem(
            configName = "listwall-ct",
            admobIdName = "listwall-ct_native1",
        )
    val zipItemAds1 =
        WallpaperItem.AdsWallpaperItem(
            adsItem = itemAds1,
        )

    val itemAds2 =
        AdsItem(
            configName = "listwall-ct",
            admobIdName = "listwall-ct_native2",
        )

    val zipItemAds2 =
        WallpaperItem.AdsWallpaperItem(
            adsItem = itemAds2,
        )

    rawList.forEachIndexed { index, item ->
        editedList.add(item)
        count++
        if (index == 2 || count == Constant.numberOfContentBetweenWallpaperList * 3) {
            if (!isLastTimeAddAds1) {
                editedList.add(zipItemAds1)
                isLastTimeAddAds1 = true
            } else {
                editedList.add(zipItemAds2)
                isLastTimeAddAds1 = false
            }
            count = 0
        }
    }
    Timber.tag(tag).d("editedList $editedList")
    return editedList
}

fun WallpaperFragment.observeWallpaperCategories() {
    commonViewModel.wallpaperUiState
        .map { it.categories }
        .distinctUntilChanged()
        .onEach { categories ->
            updateTabLayout(categories)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun WallpaperFragment.observeWallpaperData() {
    commonViewModel.wallpaperUiState
        .map {
            Triple(
                it.currentCategoryWallpapers,
                it.currentLocalWallpapers,
                it.selectedCategory,
            )
        }.onEach { (remoteWallpapers, localWallpapers, selectedCategory) ->
            Timber.d("observeWallpaperData: remote=${remoteWallpapers.size}, local=${localWallpapers.size}, category=$selectedCategory")
        }.distinctUntilChanged()
        .onEach { (remoteWallpapers, localWallpapers, selectedCategory) ->
            selectedCategory?.let { category ->
                // Submit wallpapers to the optimized adapter
                wallpaperAdapter.submitList(
                    filterListAds(
                        createListFromBackground(
                            remoteWallpapers,
                            localWallpapers,
                            category,
                        ),
                    ),
                )

                // Show/hide empty state based on category
                val isEmpty =
                    when (category) {
                        Constant.ALL -> remoteWallpapers.isEmpty() && localWallpapers.isEmpty()
                        Constant.LOCAL_FILES -> localWallpapers.isEmpty()
                        else -> remoteWallpapers.isEmpty()
                    }
                binding.emptyState.isVisible =
                    isEmpty &&
                    !commonViewModel.wallpaperUiState.value.isLoading
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun WallpaperFragment.observeSelectedCategory() {
    commonViewModel.wallpaperUiState
        .map { it.selectedCategory }
        .distinctUntilChanged()
        .onEach { selectedCategory ->
            selectedCategory?.let { category ->
                selectTabByCategory(category)
                // Refresh local wallpapers when switching to All or Local Files tab
                if (category == Constant.ALL || category == Constant.LOCAL_FILES) {
                    commonViewModel.refreshLocalWallpapers()
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun WallpaperFragment.observeLoadingState() {
    commonViewModel.wallpaperUiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressBar.isVisible = isLoading
            binding.rvWallpapers.isVisible = !isLoading
            // Hide empty state when loading
            if (isLoading) {
                binding.emptyState.isVisible = false
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

private fun WallpaperFragment.updateTabLayout(categories: List<String>) {
    binding.tabLayout.removeAllTabs()

    categories.forEach { category ->
        val tab = binding.tabLayout.newTab()
        tab.text =
            when (category) {
                Constant.ALL -> getString(R.string.all)
                Constant.LOCAL_FILES -> getString(R.string.local_files)
                else -> category
            }
        binding.tabLayout.addTab(tab)
    }
}

private fun WallpaperFragment.selectTabByCategory(category: String) {
    val tabCount = binding.tabLayout.tabCount
    for (i in 0 until tabCount) {
        val tab = binding.tabLayout.getTabAt(i)
        val tabText = tab?.text.toString()

        // Check if this tab matches the category
        val matches =
            when (category) {
                Constant.ALL -> tabText == getString(R.string.all)
                Constant.LOCAL_FILES -> tabText == getString(R.string.local_files)
                else -> tabText == category
            }

        if (matches) {
            tab?.select()
            break
        }
    }
}

fun WallpaperFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "listwall-ct",
        spaceNameAds = "listwall-ct_native1",
        includeHasBeenOpened = false,
    )

    safePreloadAds(
        spaceNameConfig = "listwall-ct",
        spaceNameAds = "listwall-ct_native2",
        includeHasBeenOpened = false,
    )

    safePreloadAds(
        listSpaceNameConfig = listOf("listwall-choosecate", "listwall-gallery"),
        spaceNameAds = "list-1ID_interstitial",
    )

    safePreloadAds(
        spaceNameConfig = "listwall-choosect",
        spaceNameAds = "listwall-choosect_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "listwall",
        spaceName = "listwall_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
