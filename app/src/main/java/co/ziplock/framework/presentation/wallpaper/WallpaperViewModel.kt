package co.ziplock.framework.presentation.wallpaper

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import dagger.hilt.android.lifecycle.HiltViewModel
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.presentation.common.BaseViewModel
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import javax.inject.Inject
import kotlinx.parcelize.IgnoredOnParcel

@HiltViewModel
class WallpaperViewModel @Inject constructor() : BaseViewModel() {

}

@Parcelize
data class WallpaperUiState(
    val isLoading: Boolean = false,
    val categories: List<String> = emptyList(),
    val wallpaperCategories: Map<String, List<WallpaperResponse>> = emptyMap(),
    val allWallpapers: List<WallpaperResponse> = emptyList(),
    val localWallpapers: List<LocalImageData> = emptyList(),
    val selectedCategory: String? = null,
    val currentCategoryWallpapers: List<WallpaperResponse> = emptyList(),
    val currentLocalWallpapers: List<LocalImageData> = emptyList(),
    val selectedWallpaper: WallpaperResponse? = null,
    @IgnoredOnParcel
    val error: Throwable? = null
) : Parcelable