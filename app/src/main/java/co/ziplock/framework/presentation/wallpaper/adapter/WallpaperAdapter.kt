package co.ziplock.framework.presentation.wallpaper.adapter

import android.net.Uri
import android.view.LayoutInflater
import androidx.core.view.isVisible
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import co.ziplock.R
import co.ziplock.databinding.ItemWallpaperBinding
import co.ziplock.databinding.ViewItemNativeHotTrendingBinding
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.framework.presentation.model.wallpaper.LocalImageData
import co.ziplock.framework.presentation.model.wallpaper.WallpaperItem
import co.ziplock.util.Constant
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import java.io.File

/**
 * Optimized wallpaper adapter that handles both remote and local wallpapers
 * Uses a single adapter instead of multiple adapters
 */
class WallpaperAdapter(
    private val onRemoteWallpaperClick: (WallpaperResponse) -> Unit,
    private val onLocalWallpaperClick: (LocalImageData) -> Unit,
) : BaseListAdapter<WallpaperItem, ViewDataBinding>(
        createDiffCallback(
            areItemsTheSame = { oldItem, newItem ->
                when {
                    oldItem is WallpaperItem.RemoteWallpaper && newItem is WallpaperItem.RemoteWallpaper -> {
                        oldItem.wallpaper.fileUrl == newItem.wallpaper.fileUrl
                    }

                    oldItem is WallpaperItem.LocalWallpaperItem && newItem is WallpaperItem.LocalWallpaperItem -> {
                        oldItem.wallpaper.filePath == newItem.wallpaper.filePath
                    }

                    else -> false
                }
            },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
        ),
    ) {
    override fun getItemViewType(position: Int): Int =
        when (getItem(position)) {
            is WallpaperItem.AdsWallpaperItem -> ITEM_TYPE_ADS
            else -> ITEM_TYPE_NORMAL
        }

    private var selectedItem: WallpaperItem? = null

    override fun getLayoutRes(viewType: Int): Int =
        if (viewType == ITEM_TYPE_ADS) R.layout.view_item_native_hot_trending else R.layout.item_wallpaper

    private var fragment: Fragment? = null

    fun setFragment(fragment: Fragment) {
        this.fragment = fragment
    }

    override fun bindView(
        binding: ViewDataBinding,
        item: WallpaperItem,
        position: Int,
    ) {
        if (binding is ItemWallpaperBinding) {
            when (item) {
                is WallpaperItem.RemoteWallpaper -> {
                    bindRemoteWallpaper(binding, item.wallpaper)
                }

                is WallpaperItem.LocalWallpaperItem -> {
                    bindLocalWallpaper(binding, item.wallpaper)
                }

                is WallpaperItem.AdsWallpaperItem -> {}
            }

            // Show selection overlay if this item is selected
            binding.overlay.isVisible = selectedItem == item
        }
        if (binding is ViewItemNativeHotTrendingBinding && item is WallpaperItem.AdsWallpaperItem) {
            binding.layoutAds.post {
                val view =
                    LayoutInflater
                        .from(binding.root.context)
                        .inflate(R.layout.layout_native_trending_list, null)
                fragment?.showLoadedNative(
                    spaceNameConfig = item.adsItem.configName,
                    spaceName = item.adsItem.admobIdName,
                    includeHasBeenOpened = true,
                    viewAdsInflateFromXml = view,
                    ratioView = "328:136",
                    layoutToAttachAds = binding.adViewGroup,
                    layoutContainAds = binding.layoutAds,
                    onAdsClick = {},
                )
            }
        }
    }

    private fun bindRemoteWallpaper(
        binding: ItemWallpaperBinding,
        wallpaper: WallpaperResponse,
    ) {
        // Load remote wallpaper image from network
        Glide
            .with(binding.root.context)
            .load(wallpaper.previewThumbnail)
            .placeholder(android.R.drawable.ic_menu_gallery)
            .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
            .format(DecodeFormat.PREFER_RGB_565)
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .skipMemoryCache(false)
            .centerCrop()
            .into(binding.ivWallpaper)

        // Set click listener for remote wallpaper
        binding.root.setPreventDoubleClick {
            setSelectedRemoteWallpaper(wallpaper)
            onRemoteWallpaperClick(wallpaper)
        }
    }

    private fun bindLocalWallpaper(
        binding: ItemWallpaperBinding,
        localWallpaper: LocalImageData,
    ) {
        // Load local wallpaper image from device storage
        val file = File(localWallpaper.filePath)
        val uri = Uri.fromFile(file)

        Glide
            .with(binding.root.context)
            .load(uri)
            .placeholder(android.R.drawable.ic_menu_gallery)
            .override(binding.ivWallpaper.width, binding.ivWallpaper.height)
            .format(DecodeFormat.PREFER_RGB_565)
            .diskCacheStrategy(DiskCacheStrategy.DATA)
            .skipMemoryCache(false)
            .centerCrop()
            .into(binding.ivWallpaper)

        // Set click listener for local wallpaper
        binding.root.setPreventDoubleClick {
            setSelectedLocalWallpaper(localWallpaper)
            onLocalWallpaperClick(localWallpaper)
        }
    }

    /**
     * Submit wallpapers for different categories
     * @param remoteWallpapers List of remote wallpapers from network
     * @param localWallpapers List of local wallpapers from device storage
     * @param category Current selected category to determine which wallpapers to show
     */
    fun submitWallpapers(
        remoteWallpapers: List<WallpaperResponse>,
        localWallpapers: List<LocalImageData>,
        category: String,
    ) {
        val items = mutableListOf<WallpaperItem>()

        when (category) {
            Constant.ALL -> {
                // Show both remote and local wallpapers
                items.addAll(remoteWallpapers.map { WallpaperItem.RemoteWallpaper(it) })
                items.addAll(localWallpapers.map { WallpaperItem.LocalWallpaperItem(it) })
            }

            Constant.LOCAL_FILES -> {
                // Show only local wallpapers
                items.addAll(localWallpapers.map { WallpaperItem.LocalWallpaperItem(it) })
            }

            else -> {
                // Show only remote wallpapers for specific categories
                items.addAll(remoteWallpapers.map { WallpaperItem.RemoteWallpaper(it) })
            }
        }

        submitList(items)
    }

    /**
     * Set selected wallpaper item with visual feedback
     */
    fun setSelectedItem(item: WallpaperItem?) {
        val oldSelected = selectedItem
        selectedItem = item

        // Notify changes for old and new selected items to update overlay
        oldSelected?.let { old ->
            currentList.indexOf(old).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
        item?.let { new ->
            currentList.indexOf(new).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
    }

    /**
     * Set selected remote wallpaper by creating WallpaperItem wrapper
     */
    fun setSelectedRemoteWallpaper(wallpaper: WallpaperResponse?) {
        val item = wallpaper?.let { WallpaperItem.RemoteWallpaper(it) }
        setSelectedItem(item)
    }

    /**
     * Set selected local wallpaper by creating WallpaperItem wrapper
     */
    fun setSelectedLocalWallpaper(wallpaper: LocalImageData?) {
        val item = wallpaper?.let { WallpaperItem.LocalWallpaperItem(it) }
        setSelectedItem(item)
    }

    companion object {
        const val ITEM_TYPE_ADS = 1
        const val ITEM_TYPE_NORMAL = 0
    }
}
