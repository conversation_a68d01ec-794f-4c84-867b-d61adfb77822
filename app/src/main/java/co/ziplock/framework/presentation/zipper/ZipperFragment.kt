package co.ziplock.framework.presentation.zipper

import android.view.View
import co.ziplock.R
import co.ziplock.databinding.FragmentZipperBinding
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseFragment
import co.ziplock.framework.presentation.theme.dialog.RewardContentDialog
import co.ziplock.framework.presentation.zipper.adapter.ZipperAdapter
import co.ziplock.util.BundleKey
import co.ziplock.util.displayToast
import dagger.hilt.android.AndroidEntryPoint
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.showLoadedRewardVideo

@AndroidEntryPoint
class ZipperFragment :
    BaseFragment<FragmentZipperBinding, ZipperViewModel>(
        FragmentZipperBinding::inflate,
        ZipperViewModel::class.java,
    ),
    ZipperAdapter.Listener,
    RewardContentDialog.Listener {
    lateinit var zipperAdapter: ZipperAdapter
    var isTabsInitialized = false

    val isReEditFlow by lazy {
        arguments?.getBoolean(BundleKey.KEY_FROM_RE_EDIT_FLOW) ?: false
    }

    var currentZip: ZipperResponse? = null

    override fun init(view: View) {
        setupRecyclerView()
        setupTabLayout()
        setupSystemBackEvent()
        setupBackButtonClickListener()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeZipperCategories()
        observeCurrentCategoryZippers()
        observeSelectedCategory()
        observeLoadingState()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isTabsInitialized = false
    }

    override fun onZipperClick(zipper: ZipperResponse) {
        currentZip = zipper
        if (checkConditionShowAds(
                context = requireContext(),
                spaceNameConfig = "zippro",
            ) &&
            zipper.isPro == true &&
            !commonViewModel.isZipperUnlocked(zipper.id)
        ) {
            val dialog = RewardContentDialog()
            dialog.setListener(this)
            dialog.show(childFragmentManager)
        } else {
            showInterWhenClickZipper()
        }
    }

    override fun onWatchVideoEvent() {
        showLoadedRewardVideo(
            spaceNameConfig = "zippro",
            spaceName = "zippro_rewarded",
            destinationToShowAds = R.id.zipperFragment,
            isShowLoadingView = true,
            isScreenType = false,
            onRewardDone = { isSuccess ->
                if (!isSuccess) {
                    showInterWhenClickZipper()
                }
            },
            onGetReward = {
                if (currentZip == null) {
                    displayToast(R.string.something_error)
                    return@showLoadedRewardVideo
                }
                logicClickZipper()
            },
        )
    }

    override fun onBuyVipVersion() {
        safeNav(R.id.zipperFragment, R.id.action_to_iapFragment)
    }

    companion object {
        const val TAG = "ZipperFragment"

        fun newInstance() = ZipperFragment()
    }
}
