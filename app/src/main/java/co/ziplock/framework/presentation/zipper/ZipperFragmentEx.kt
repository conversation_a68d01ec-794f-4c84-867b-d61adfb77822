package co.ziplock.framework.presentation.zipper

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import co.ziplock.R
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.doActionWhenResume
import co.ziplock.framework.presentation.common.onSystemBackEvent
import co.ziplock.framework.presentation.model.AdsItem
import co.ziplock.framework.presentation.zipper.adapter.ZipperAdapter
import co.ziplock.util.BundleKey
import co.ziplock.util.Constant
import co.ziplock.util.disableTooltipsForTabs
import co.ziplock.util.displayToast
import co.ziplock.util.setPreventDoubleClick
import com.google.android.material.tabs.TabLayout
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.adsuntils.checkConditionShowAds
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun ZipperFragment.setupBackButtonClickListener() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun ZipperFragment.setupSystemBackEvent() {
    onSystemBackEvent {
        backEvent()
    }
}

fun ZipperFragment.backEvent() {
    findNavController().navigateUp()
}

fun ZipperFragment.logicClickZipper() {
    if (currentZip == null) {
        displayToast(R.string.something_error)
        return
    }
    if (currentZip!!.isPro == true) {
        commonViewModel.addUnlockedZipper(currentZip!!.id ?: "")
    }
    // Handle zipper item click
    zipperAdapter.setSelectedZipper(currentZip)

    if (isReEditFlow) {
        // If coming from EditLayoutFragment, set data to CommonViewModel and go back
        commonViewModel.setEditLayoutZipper(currentZip!!)
        safeNavigateUp(R.id.zipperFragment)
    } else {
        // Normal flow - set data to CommonViewModel and navigate to EditLayout
        commonViewModel.setEditLayoutZipper(currentZip!!)
        navigateToEditLayout(currentZip!!)
    }
}

fun ZipperFragment.showInterWhenClickZipper() {
    showLoadedInter(
        spaceNameConfig = "zip-nopro",
        spaceName = "content_1ID_interstitial",
        destinationToShowAds = R.id.zipperFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            logicClickZipper()
        },
        onCloseAds = {},
    )
}

fun ZipperFragment.navigateToEditLayout(zipper: ZipperResponse) {
    val bundle =
        Bundle().apply {
            putParcelable(BundleKey.ZIPPER_DATA, zipper)
        }
    safeNavInter(
        R.id.zipperFragment,
        R.id.action_zipperFragment_to_editLayoutFragment,
        bundle,
    )
}

fun ZipperFragment.setupRecyclerView() {
    zipperAdapter = ZipperAdapter()
    zipperAdapter.setListener(this)
    zipperAdapter.setFragment(this)

    val gridLayoutManager = GridLayoutManager(requireContext(), 3)
    gridLayoutManager.spanSizeLookup =
        object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                val item = zipperAdapter.currentList.getOrNull(position)
                return if (item?.adsItem != null) 3 else 1
            }
        }

    binding.rvZippers.apply {
        layoutManager = gridLayoutManager
        adapter = zipperAdapter
    }
}

fun ZipperFragment.setupTabLayout() {
    binding.tabLayout.disableTooltipsForTabs()

    binding.tabLayout.addOnTabSelectedListener(
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                // Only show ads if tabs are initialized and this is a user interaction
                val tabText = tab.text.toString()
                val category =
                    when (tabText) {
                        getString(R.string.all) -> Constant.ALL
                        else -> tabText
                    }
                if (!isTabsInitialized) {
                    isTabsInitialized = true
                    commonViewModel.selectZipperCategory(category)
                } else {
                    showLoadedInter(
                        spaceNameConfig = "listzip-choosecate",
                        spaceName = "list-1ID_interstitial",
                        destinationToShowAds = R.id.zipperFragment,
                        isShowLoadingView = true,
                        isScreenType = false,
                        navOrBack = {
                            doActionWhenResume {
                                commonViewModel.selectZipperCategory(category)
                            }
                        },
                        onCloseAds = {},
                    )
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
            }
        },
    )
}

fun ZipperFragment.observeZipperCategories() {
    commonViewModel.zipperUiState
        .map { it.categories }
        .distinctUntilChanged()
        .onEach { categories ->
            updateTabLayout(categories)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ZipperFragment.observeCurrentCategoryZippers() {
    commonViewModel.zipperUiState
        .map { it.currentCategoryZippers }
        .distinctUntilChanged()
        .onEach { zippers ->
            zipperAdapter.submitList(filterListAds(zippers))
            // Show/hide empty state
            binding.emptyState.isVisible =
                zippers.isEmpty() &&
                !commonViewModel.zipperUiState.value.isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ZipperFragment.filterListAds(rawList: List<ZipperResponse>): List<ZipperResponse> {
    val tag = "filterListAdsZip"
    if (!checkConditionShowAds(
            context = requireContext(),
            spaceNameConfig = "listzip-ct",
        )
    ) {
        Timber.tag(tag).d("rawList $rawList")
        return rawList
    }

    val editedList = mutableListOf<ZipperResponse>()
    var count = 0
    var isLastTimeAddAds1 = false

    val itemAds1 =
        AdsItem(
            configName = "listzip-ct",
            admobIdName = "listzip-ct_native1",
        )
    val zipItemAds1 = ZipperResponse(fileUrl = null, adsItem = itemAds1, isPro = false, id = null)

    val itemAds2 =
        AdsItem(
            configName = "listzip-ct",
            admobIdName = "listzip-ct_native2",
        )

    val zipItemAds2 = ZipperResponse(fileUrl = null, adsItem = itemAds2, isPro = false, id = null)

    rawList.forEachIndexed { index, item ->
        editedList.add(item)
        count++
        if (index == 2 || count == Constant.numberOfContentBetweenZipList * 3) {
            if (!isLastTimeAddAds1) {
                editedList.add(zipItemAds1)
                isLastTimeAddAds1 = true
            } else {
                editedList.add(zipItemAds2)
                isLastTimeAddAds1 = false
            }
            count = 0
        }
    }
    Timber.tag(tag).d("editedList $editedList")
    return editedList
}

fun ZipperFragment.observeSelectedCategory() {
    commonViewModel.zipperUiState
        .map { it.selectedCategory }
        .distinctUntilChanged()
        .onEach { selectedCategory ->
            selectedCategory?.let { category ->
                selectTabByCategory(category)
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ZipperFragment.observeLoadingState() {
    commonViewModel.zipperUiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressBar.isVisible = isLoading
            binding.rvZippers.isVisible = !isLoading
            // Hide empty state when loading
            if (isLoading) {
                binding.emptyState.isVisible = false
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

private fun ZipperFragment.updateTabLayout(categories: List<String>) {
    binding.tabLayout.removeAllTabs()

    categories.forEach { category ->
        val tab = binding.tabLayout.newTab()
        tab.text =
            when (category) {
                Constant.ALL -> getString(R.string.all)
                else -> category
            }
        binding.tabLayout.addTab(tab)
    }
}

private fun ZipperFragment.selectTabByCategory(category: String) {
    val tabCount = binding.tabLayout.tabCount
    for (i in 0 until tabCount) {
        val tab = binding.tabLayout.getTabAt(i)
        val tabText = tab?.text.toString()

        // Check if this tab matches the category
        val matches =
            when (category) {
                Constant.ALL -> tabText == getString(R.string.all)
                else -> tabText == category
            }

        if (matches) {
            tab?.select()
            break
        }
    }
}

fun ZipperFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "listzip-ct",
        spaceNameAds = "listzip-ct_native1",
        includeHasBeenOpened = false,
    )
    safePreloadAds(
        spaceNameConfig = "listzip-ct",
        spaceNameAds = "listzip-ct_native2",
        includeHasBeenOpened = false,
    )
    safePreloadAds(
        spaceNameConfig = "listzip-choosecate",
        spaceNameAds = "list-1ID_interstitial",
    )
    showLoadedNative(
        spaceNameConfig = "listzip",
        spaceName = "listzip_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
