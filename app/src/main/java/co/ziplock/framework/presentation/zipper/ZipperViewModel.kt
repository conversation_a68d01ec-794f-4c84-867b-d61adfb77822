package co.ziplock.framework.presentation.zipper

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import dagger.hilt.android.lifecycle.HiltViewModel
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseViewModel
import javax.inject.Inject
import kotlinx.parcelize.IgnoredOnParcel

@HiltViewModel
class ZipperViewModel @Inject constructor() : BaseViewModel() {

}

@Parcelize
data class ZipperUiState(
    val isLoading: Boolean = false,
    val categories: List<String> = emptyList(),
    val zipperCategories: Map<String, List<ZipperResponse>> = emptyMap(),
    val allZippers: List<ZipperResponse> = emptyList(),
    val selectedCategory: String? = null,
    val currentCategoryZippers: List<ZipperResponse> = emptyList(),
    val selectedZipper: ZipperResponse? = null,
    @IgnoredOnParcel
    val error: Throwable? = null
) : Parcelable