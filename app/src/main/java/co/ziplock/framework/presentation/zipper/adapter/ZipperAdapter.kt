package co.ziplock.framework.presentation.zipper.adapter

import android.view.LayoutInflater
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import co.ziplock.R
import co.ziplock.databinding.ItemZipperBinding
import co.ziplock.databinding.ViewItemNativeHotTrendingBinding
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.common.BaseListAdapter
import co.ziplock.framework.presentation.common.createDiffCallback
import co.ziplock.util.setPreventDoubleClick
import com.bumptech.glide.Glide
import pion.datlt.libads.utils.adsuntils.showLoadedNative

class ZipperAdapter() : BaseListAdapter<ZipperResponse, ViewDataBinding>(
        createDiffCallback(
            areItemsTheSame = { oldItem, newItem -> oldItem.fileUrl == newItem.fileUrl },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
        ),
    ) {
    private var selectedZipper: ZipperResponse? = null

    private var fragment: Fragment? = null

    fun setFragment(fragment: Fragment) {
        this.fragment = fragment
    }

    interface Listener {
        fun onZipperClick(zipper: ZipperResponse)
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    override fun getLayoutRes(viewType: Int): Int =
        if (viewType == ITEM_TYPE_ADS) R.layout.view_item_native_hot_trending else R.layout.item_zipper

    override fun getItemViewType(position: Int): Int = if (getItem(position).adsItem != null) ITEM_TYPE_ADS else ITEM_TYPE_NORMAL

    override fun bindView(
        binding: ViewDataBinding,
        item: ZipperResponse,
        position: Int,
    ) {
        if (binding is ItemZipperBinding) {
            // Load zipper image
            Glide
                .with(binding.root.context)
                .load(item.fileUrl)
                .override(binding.ivZipper.width, binding.ivZipper.height)
                .fitCenter()
                .into(binding.ivZipper)

            // Set click listener
            binding.root.setPreventDoubleClick {
                listener?.onZipperClick(item)
            }
        }

        if (binding is ViewItemNativeHotTrendingBinding) {
            binding.layoutAds.post {
                val view =
                    LayoutInflater
                        .from(binding.root.context)
                        .inflate(R.layout.layout_native_trending_list, null)
                fragment?.showLoadedNative(
                    spaceNameConfig = item.adsItem?.configName ?: "",
                    spaceName = item.adsItem?.admobIdName ?: "",
                    includeHasBeenOpened = true,
                    viewAdsInflateFromXml = view,
                    ratioView = "328:136",
                    layoutToAttachAds = binding.adViewGroup,
                    layoutContainAds = binding.layoutAds,
                    onAdsClick = {},
                )
            }
        }
    }

    fun setSelectedZipper(zipper: ZipperResponse?) {
        val oldSelected = selectedZipper
        selectedZipper = zipper

        // Notify changes for old and new selected items
        oldSelected?.let { old ->
            currentList.indexOf(old).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
        zipper?.let { new ->
            currentList.indexOf(new).takeIf { it >= 0 }?.let { index ->
                notifyItemChanged(index)
            }
        }
    }

    companion object {
        const val ITEM_TYPE_ADS = 1
        const val ITEM_TYPE_NORMAL = 0
    }
}
