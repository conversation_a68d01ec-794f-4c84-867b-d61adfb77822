package co.ziplock.framework.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import dagger.hilt.android.AndroidEntryPoint
import co.ziplock.framework.service.ZipperOverlayService
import co.ziplock.util.PrefUtil
import javax.inject.Inject

@AndroidEntryPoint
class ScreenReceiver : BroadcastReceiver() {

    @Inject
    lateinit var prefUtil: PrefUtil

    companion object {
        private const val TAG = "ScreenReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_SCREEN_ON -> {
                Log.d(TAG, "Screen turned on")
            }
            
            Intent.ACTION_SCREEN_OFF -> {
                Log.d(TAG, "Screen turned off")
                if (prefUtil.lockScreenEnabled) {
                    showZipperOverlay(context.applicationContext)
                }
            }
            
            Intent.ACTION_USER_PRESENT -> {
                Log.d(TAG, "User present (unlocked)")
            }
            
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.d(TAG, "Boot completed")
                // Start service on boot if lock screen is enabled
                if (prefUtil.lockScreenEnabled) {
                    ZipperOverlayService.startService(context.applicationContext, ZipperOverlayService.ACTION_START_FOREGROUND)
                }
            }

            Intent.ACTION_LOCKED_BOOT_COMPLETED -> {
                Log.d(TAG, "Locked boot completed")
                // Start service on locked boot if lock screen is enabled
                if (prefUtil.lockScreenEnabled) {
                    ZipperOverlayService.startService(context.applicationContext, ZipperOverlayService.ACTION_START_FOREGROUND)
                }
            }
        }
    }

    private fun showZipperOverlay(context: Context) {
        ZipperOverlayService.startService(context.applicationContext, ZipperOverlayService.ACTION_SHOW_OVERLAY)
    }

    private fun hideZipperOverlay(context: Context) {
        ZipperOverlayService.startService(context.applicationContext, ZipperOverlayService.ACTION_HIDE_OVERLAY)
    }
}
