package co.ziplock.framework.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import co.ziplock.R

class SettingsNotificationService : Service() {

    private val autoCloseHandler = Handler(Looper.getMainLooper())
    private var autoCloseRunnable: Runnable? = null

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "settings_notification_channel"
        private const val AUTO_CLOSE_DELAY = 3000L // 3 seconds

        fun showNotification(context: Context) {
            val intent = Intent(context, SettingsNotificationService::class.java)
            context.startService(intent)
        }

        fun hideNotification(context: Context) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(NOTIFICATION_ID)
            
            val intent = Intent(context, SettingsNotificationService::class.java)
            context.stopService(intent)
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        showCustomNotification()
        scheduleAutoClose()
        return START_NOT_STICKY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Settings Notification",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notification for settings guidance"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun showCustomNotification() {
        // Create custom notification layout
        val bigCustomView = RemoteViews(packageName, R.layout.notification_settings_custom_big_size)
        val smallCustomView = RemoteViews(packageName, R.layout.notification_settings_custom_small_size)

        // Build notification
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_app)
            .setCustomContentView(smallCustomView)
            .setCustomBigContentView(bigCustomView)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setStyle(NotificationCompat.DecoratedCustomViewStyle())
            .setAutoCancel(false)
            .setOngoing(false)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun scheduleAutoClose() {
        // Cancel any existing auto-close
        autoCloseRunnable?.let { autoCloseHandler.removeCallbacks(it) }

        // Schedule new auto-close
        autoCloseRunnable = Runnable {
            hideNotification(this)
            stopSelf()
        }
        autoCloseHandler.postDelayed(autoCloseRunnable!!, AUTO_CLOSE_DELAY)
    }

    override fun onDestroy() {
        // Cancel auto-close
        autoCloseRunnable?.let { autoCloseHandler.removeCallbacks(it) }
        autoCloseRunnable = null
        super.onDestroy()
    }
}