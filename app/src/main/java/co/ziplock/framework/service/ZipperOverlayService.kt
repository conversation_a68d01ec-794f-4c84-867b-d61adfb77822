package co.ziplock.framework.service

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.content.pm.ServiceInfo
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.hardware.camera2.CameraAccessException
import android.hardware.camera2.CameraManager
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.core.app.NotificationCompat
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import co.ziplock.BuildConfig
import co.ziplock.R
import co.ziplock.customview.StyleRow
import co.ziplock.customview.ZipperView
import co.ziplock.framework.network.ApiOtpInterface
import co.ziplock.framework.network.model.OtpRequestBody
import co.ziplock.framework.otpworkmanager.ResetLimitOtpWorker
import co.ziplock.framework.presentation.common.SendOtpStatus
import co.ziplock.framework.presentation.manager.SecurityManager
import co.ziplock.framework.presentation.model.FontData
import co.ziplock.framework.receiver.ScreenReceiver
import co.ziplock.util.Constant
import co.ziplock.util.LocaleHelper
import co.ziplock.util.PrefUtil
import com.bumptech.glide.load.engine.DiskCacheStrategy
import timber.log.Timber
import java.util.Calendar
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@AndroidEntryPoint
class ZipperOverlayService : Service() {

    private var screenReceiver: ScreenReceiver? = null
    private var languageChangeReceiver: BroadcastReceiver? = null

    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var zipperView: ZipperView? = null
    private var isOverlayShowing = false

    @Inject
    lateinit var securityManager: SecurityManager
    
    @Inject
    lateinit var prefUtil: PrefUtil
    
    @Inject
    lateinit var apiOtpInterface: ApiOtpInterface

    // Camera and Flashlight management
    private var cameraManager: CameraManager? = null
    private var cameraId: String? = null
    private var isFlashlightOn = false
    
    // OTP management
    private var otpCode: String? = null
    private var serviceScope: CoroutineScope? = null
    
    // Context management for language changes
    private var currentLocalizedContext: Context? = null

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "zipper_overlay_channel"
        private const val CHANNEL_NAME = "Zipper Overlay Service"
        const val ACTION_SHOW_OVERLAY = "${BuildConfig.APPLICATION_ID}.SHOW_OVERLAY"
        const val ACTION_HIDE_OVERLAY = "${BuildConfig.APPLICATION_ID}.HIDE_OVERLAY"
        const val ACTION_START_FOREGROUND = "${BuildConfig.APPLICATION_ID}.START_FOREGROUND"
        const val ACTION_REFRESH_CONTEXT = "${BuildConfig.APPLICATION_ID}.REFRESH_CONTEXT"

        fun startService(context: Context, action: String) {
            val intent = Intent(context, ZipperOverlayService::class.java).apply {
                this.action = action
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        fun refreshContext(context: Context) {
            val intent = Intent(context, ZipperOverlayService::class.java).apply {
                action = ACTION_REFRESH_CONTEXT
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        fun stopService(context: Context) {
            val intent = Intent(context, ZipperOverlayService::class.java)
            context.stopService(intent)
        }
    }

    override fun attachBaseContext(base: Context?) {
        // Áp dụng ngôn ngữ đã chọn trong app cho Service
        val localizedContext = base?.let { LocaleHelper.createLocalizedContext(it) } ?: base
        super.attachBaseContext(localizedContext)
    }

    override fun onCreate() {
        super.onCreate()
        // Initialize localized context
        currentLocalizedContext = LocaleHelper.createLocalizedContext(this)
        
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        //securityManager = SecurityManager(this)
        setupCameraManager()
        createNotificationChannel()
        setupCoroutineScope()
        setupScreenReceiver()
        setupLanguageChangeReceiver()
    }

    private fun setupScreenReceiver() {
        // Register screen receiver
        screenReceiver = ScreenReceiver()
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)
            addAction(Intent.ACTION_BOOT_COMPLETED)
            addAction(Intent.ACTION_LOCKED_BOOT_COMPLETED)
        }
        registerReceiver(screenReceiver, intentFilter)
    }
    
    private fun setupLanguageChangeReceiver() {
        // Create a custom broadcast receiver for language changes
        languageChangeReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_LOCALE_CHANGED -> {
                        Log.d("ZipperOverlayService", "System locale changed, refreshing context")
                        refreshServiceContext()
                    }
                    ACTION_REFRESH_CONTEXT -> {
                        Log.d("ZipperOverlayService", "Custom refresh context action received")
                        refreshServiceContext()
                    }
                }
            }
        }
        
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_LOCALE_CHANGED)
            addAction(ACTION_REFRESH_CONTEXT)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            registerReceiver(languageChangeReceiver, intentFilter, RECEIVER_NOT_EXPORTED)
        }
    }
    
    private fun setupCameraManager() {
        try {
            cameraManager = getSystemService(CAMERA_SERVICE) as CameraManager
            cameraId = cameraManager?.cameraIdList?.get(0) // Usually back camera
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForeground(NOTIFICATION_ID, createNotification())
        when (intent?.action) {
            ACTION_START_FOREGROUND -> {
                // Already handled in onCreate
            }
            ACTION_SHOW_OVERLAY -> showOverlay().also {
                Log.d("ZipperOverlayService", "Overlay shown")
            }
            ACTION_HIDE_OVERLAY -> hideOverlay().also {
                Log.d("ZipperOverlayService", "Overlay hidden")
            }
            ACTION_REFRESH_CONTEXT -> refreshServiceContext().also {
                Log.d("ZipperOverlayService", "Context refreshed")
            }
        }
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getLocalizedString(R.string.zipper_lock_active),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getLocalizedString(R.string.service_notification_description)
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager?.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val contentIntent = PendingIntent.getActivity(
            this,
            0,
            packageManager.getLaunchIntentForPackage(packageName),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getLocalizedString(R.string.zipper_lock_active))
            .setContentText(getLocalizedString(R.string.service_is_running))
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(contentIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }

    private fun showOverlay() {
        if (isOverlayShowing || !canDrawOverlays()) {
            Toast.makeText(this, getLocalizedString(R.string.overlay_is_already_showing_or_permission_not_granted), Toast.LENGTH_SHORT).show()
            return
        }

        try {
            // Use localized context for inflating layout if available
            val contextToUse = currentLocalizedContext ?: this
            // Inflate the overlay layout
            overlayView = LayoutInflater.from(contextToUse).inflate(R.layout.overlay_zipper_lock, null)
            zipperView = overlayView?.findViewById(R.id.zipper)
            
            //zipperView?.setStyle(StyleRow.STYLE_1)
            // Apply saved settings
            zipperView?.setVibrationEnabled(prefUtil.vibrationEnabled)
            zipperView?.setSoundEnabled(prefUtil.soundEnabled)

            // Set SecurityManager and PrefUtil for ZipperView
            zipperView?.setSecurityManager(securityManager)
            zipperView?.setPrefUtil(prefUtil)

            // Apply saved font and color settings
            applyFontAndColorSettings()
            
            // Load saved zipper image
            loadZipperImage()
            
            // Load saved wallpaper image
            loadWallpaperImage()
            
            // Load saved background image
            loadBackgroundImage()
            
            // Load saved sound URL
            loadSoundUrl()
            
            // Load saved row response
            loadRowResponse()

            // Set up unlock listener
            zipperView?.setOnUnlockListener {
                hideOverlay()
                Toast.makeText(this, getLocalizedString(R.string.unlocked), Toast.LENGTH_SHORT).show()
            }

            // Set up authentication required listener (for PIN/Pattern)
            zipperView?.requestOnAuthenticationRequiredListener()

            // Set up camera and flashlight callbacks
            /*zipperView?.setOnCameraRequestListener {
                Log.d("ZipperOverlayService", "Camera request received")
                openCamera()
            }*/

            zipperView?.setOnFlashlightToggleListener {
                Log.d("ZipperOverlayService", "Flashlight toggle request received")
                toggleFlashlight()
            }
            
            // Set up OTP callbacks for forgot password functionality
            setupOtpCallbacks()

            // Set up window layout parameters
            val layoutParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
                x = 0
                y = 0
            }

            // Add the overlay view to window manager
            windowManager?.addView(overlayView, layoutParams)
            isOverlayShowing = true

            // Update notification
            updateNotification(getLocalizedString(R.string.overlay_is_showing))

        } catch (e: Exception) {
            e.printStackTrace()
            val errorMessage = try {
                String.format(getLocalizedString(R.string.error_showing_overlay), e.message)
            } catch (ex: Exception) {
                getLocalizedString(R.string.error_showing_overlay)
            }
            Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show()
        }
    }

    private fun hideOverlay() {
        if (!isOverlayShowing || overlayView == null) {
            return
        }

        try {
            windowManager?.removeView(overlayView)
            overlayView = null
            zipperView = null
            isOverlayShowing = false

            // Update notification
            updateNotification(getLocalizedString(R.string.service_is_running))

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateNotification(text: String) {
        val notification = createNotification()
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager?.notify(NOTIFICATION_ID, notification)
    }

    /**
     * Refresh service context when language is changed
     * This will recreate the overlay with new language context
     */
    private fun refreshServiceContext() {
        try {
            Log.d("ZipperOverlayService", "Refreshing service context for language change")
            
            // If overlay is currently showing, hide it first
            val wasOverlayShowing = isOverlayShowing
            if (wasOverlayShowing) {
                hideOverlay()
            }
            
            // Force recreate the base context with new locale
            currentLocalizedContext = LocaleHelper.createLocalizedContext(this)
            
            // Update notification channel and notification with new language
            createNotificationChannel()
            updateNotification(getLocalizedString(R.string.service_is_running))
            
            // If overlay was showing before, show it again with new context
            if (wasOverlayShowing) {
                // Add a small delay to ensure context is properly updated
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    showOverlay()
                }, 100)
            }
            
            Log.d("ZipperOverlayService", "Service context refreshed successfully")
            
        } catch (e: Exception) {
            Log.e("ZipperOverlayService", "Error refreshing service context: ${e.message}", e)
        }
    }
    
    /**
     * Get localized string using current context or cached localized context
     */
    private fun getLocalizedString(resId: Int): String {
        return try {
            currentLocalizedContext?.getString(resId) ?: getString(resId)
        } catch (e: Exception) {
            getString(resId)
        }
    }

    private fun openCamera() {
        try {
            // Check if camera permission is granted
            if (checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                Log.d("ZipperOverlayService", "Camera permission not granted")
                return
            }

            // Hide overlay temporarily
            hideOverlay()

            // Create an activity launcher intent for the main activity
            // This is needed to launch camera from background
            /*val launcherIntent = Intent(this, MainNewActivity::class.java).apply {
                action = "${BuildConfig.APPLICATION_ID}.ACTION_OPEN_CAMERA"
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }*/

            Log.d("ZipperOverlayService", "Starting main activity with camera action")
            //startActivity(launcherIntent)

        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("ZipperOverlayService", "Error opening camera: ${e.message}")
            // Show overlay back if error occurred
            showOverlay()
        }
    }

    private fun toggleFlashlight() {
        try {
            if (cameraManager == null || cameraId == null) {
                Toast.makeText(this, getString(R.string.camera_unavailable), Toast.LENGTH_SHORT).show()
                return
            }

            // Check if device has flashlight
            if (!packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH)) {
                Toast.makeText(this, getString(R.string.no_flash_on_device), Toast.LENGTH_SHORT).show()
                return
            }

            isFlashlightOn = !isFlashlightOn
            cameraManager?.setTorchMode(cameraId!!, isFlashlightOn)

            val message = if (isFlashlightOn) getString(R.string.flashlight_turned_on) else getString(R.string.flashlight_turned_off)
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()

            // Update flashlight icon in zipper view
            zipperView?.updateFlashlightState(isFlashlightOn)

        } catch (e: CameraAccessException) {
            e.printStackTrace()
            Toast.makeText(this, getString(R.string.camera_access_error, e.message), Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, getString(R.string.flashlight_control_error, e.message), Toast.LENGTH_SHORT).show()
        }
    }

    private fun turnOffFlashlight() {
        try {
            if (isFlashlightOn && cameraManager != null && cameraId != null) {
                cameraManager?.setTorchMode(cameraId!!, false)
                isFlashlightOn = false
                zipperView?.updateFlashlightState(false)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun applyFontAndColorSettings() {
        try {
            // Get saved font family ID
            val savedFontFamilyId = prefUtil.selectedFontFamilyId
            val fontFamily = FontData.fontFamilies.find { it.id == savedFontFamilyId }
            
            // Get saved font color ID
            val savedFontColorId = prefUtil.selectedFontColorId
            val fontColor = FontData.fontColors.find { it.id == savedFontColorId }
            
            // Apply font and color to zipper view
            zipperView?.applyFontAndColorSettings(
                fontFamily?.fontResource,
                fontColor?.colorRes
            )
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun loadZipperImage() {
        val zipperResponse = prefUtil.selectedZipperResponse
        if (zipperResponse != null) {
            try {
                zipperView?.setZipperResponse(zipperResponse)
            } catch (e: Exception) {
                Log.e("ZipperOverlayService", "Error loading zipper image", e)
            }
        }
    }

    private fun loadWallpaperImage() {
        val wallpaperImagePath = prefUtil.selectedWallpaperResponse?.fileUrl
        Log.d("ZipperOverlayService", "Loading wallpaper image from path: $wallpaperImagePath")
        if (!wallpaperImagePath.isNullOrEmpty()) {
            try {
                Glide.with(this)
                    .asBitmap()
                    .load(wallpaperImagePath)
                    .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.RESOURCE)
                    .skipMemoryCache(true)
                    .centerCrop()
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                            Log.d("ZipperOverlayService", "Loaded wallpaper image from path: $wallpaperImagePath")
                            val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                            zipperView?.setWallpaperBitmap(copyBitmap)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                            // Handle cleanup if needed
                        }

                        override fun onLoadFailed(errorDrawable: Drawable?) {
                            Log.e("ZipperOverlayService", "Failed to load wallpaper image from path: $wallpaperImagePath")
                            // Set default wallpaper if image loading fails
                            //zipperView?.setWallpaperBitmap(null)
                        }
                    })
            } catch (e: Exception) {
                Log.e("ZipperOverlayService", "Error loading wallpaper image", e)
                // Set default wallpaper if error occurred
                //zipperView?.setWallpaperBitmap(null)
            }
        } else {
            // Set default wallpaper if no wallpaper path
            //zipperView?.setWallpaperBitmap(null)
        }
    }

    private fun loadBackgroundImage() {
        val backgroundImagePath = prefUtil.selectedBackgroundResponse?.fileUrl
        Log.d("ZipperOverlayService", "Loading background image from path: $backgroundImagePath")
        if (!backgroundImagePath.isNullOrEmpty()) {
            try {
                Glide.with(this)
                    .asBitmap()
                    .load(backgroundImagePath)
                    .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                    .skipMemoryCache(true)
                    .centerCrop()
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                            Log.d("ZipperOverlayService", "Loaded background image from path: $backgroundImagePath")
                            val copyBitmap = resource.copy(Bitmap.Config.ARGB_8888, true)
                            zipperView?.setBackgroundBitmap(copyBitmap)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                            // Handle cleanup if needed
                        }

                        override fun onLoadFailed(errorDrawable: Drawable?) {
                            Log.e("ZipperOverlayService", "Failed to load background image from path: $backgroundImagePath")
                            // Set default background if image loading fails
                            //zipperView?.setBackgroundBitmap(null)
                        }
                    })
            } catch (e: Exception) {
                Log.e("ZipperOverlayService", "Error loading background image", e)
                // Set default background if error occurred
                //zipperView?.setBackgroundBitmap(null)
            }
        } else {
            // Set default background if no background path
            //zipperView?.setBackgroundBitmap(null)
        }
    }

    private fun loadSoundUrl() {
        val soundUrl = prefUtil.selectedSoundUrl
        Log.d("ZipperOverlayService", "Loading sound from URL: $soundUrl")
        if (!soundUrl.isNullOrEmpty()) {
            try {
                zipperView?.setSoundUrl(soundUrl)
                Log.d("ZipperOverlayService", "Sound URL set successfully: $soundUrl")
            } catch (e: Exception) {
                Log.e("ZipperOverlayService", "Error setting sound URL", e)
                // Clear sound URL if error occurred
                zipperView?.setSoundUrl(null)
            }
        } else {
            // Clear sound URL if no sound URL saved
            zipperView?.setSoundUrl(null)
            Log.d("ZipperOverlayService", "No sound URL saved, cleared sound")
        }
    }

    private fun loadRowResponse() {
        val rowResponse = prefUtil.selectedRowResponse
        Log.d("ZipperOverlayService", "Loading RowResponse: $rowResponse")
        try {
            zipperView?.setRowResponse(rowResponse)
            if (rowResponse != null) {
                Log.d("ZipperOverlayService", "RowResponse set successfully - Left: ${rowResponse.imageRowLeft}, Right: ${rowResponse.imageRowRight}, Preview: ${rowResponse.previewThumbnail}")
            } else {
                Log.d("ZipperOverlayService", "No RowResponse saved, cleared row data")
            }
        } catch (e: Exception) {
            Log.e("ZipperOverlayService", "Error setting RowResponse", e)
            // Clear row response if error occurred
            zipperView?.setRowResponse(null)
        }
    }

    private fun canDrawOverlays(): Boolean {
        return Settings.canDrawOverlays(this)
    }
    
    private fun setupCoroutineScope() {
        serviceScope = CoroutineScope(Dispatchers.Main + Job())
    }
    
    private fun setupOtpCallbacks() {
        // Setup OTP request callback for forgot password
        zipperView?.setOnRequestOtpListener { email ->
            Log.d("ZipperOverlayService", "OTP requested for email: $email")
            requestOtp(email)
        }
        
        // Setup OTP validation callback for forgot password
        zipperView?.setOnOTPValidationListener { otpCode, onSuccess, onFailure ->
            Log.d("ZipperOverlayService", "OTP validation requested: $otpCode")
            submitOtpCode(otpCode, onSuccess, onFailure)
        }
        
        Log.d("ZipperOverlayService", "OTP callbacks setup completed")
    }
    
    fun requestOtp(email: String?) {
        if (prefUtil.testOTPMode) {
            handleOtpRequestResult(SendOtpStatus.Success("123456"))
            return
        }

        serviceScope?.launch(
            CoroutineExceptionHandler { _, throwable ->
                Timber.e("${throwable.message}")
                handleOtpRequestResult(SendOtpStatus.Error)
            }
        ) {
            if (prefUtil.countRequestOtp >= Constant.TIMES_LIMITS_REQUEST_OTP) {
                handleOtpRequestResult(SendOtpStatus.Limited)
                return@launch
            }

            if (otpCode != null) {
                handleOtpRequestResult(SendOtpStatus.Success(otpCode!!))
                return@launch
            }

            handleOtpRequestResult(SendOtpStatus.Standby)

            if (email == null) {
                handleOtpRequestResult(SendOtpStatus.Error)
                return@launch
            }

            val otp = generateOtp()
            val body = OtpRequestBody(email = email, otp = otp)
            val response = apiOtpInterface.sendOtp(apiKey = BuildConfig.API_KEY, request = body)

            if (prefUtil.countRequestOtp == 0) {
                resetLimitOtp()
            }

            prefUtil.countRequestOtp += 1

            if (response.status == 200) {
                otpCode = otp
                handleOtpRequestResult(SendOtpStatus.Success(otp))
            } else {
                handleOtpRequestResult(SendOtpStatus.Error)
            }
        }
    }
    
    fun submitOtpCode(
        code: String,
        onSuccess: () -> Unit,
        onFailure: () -> Unit,
    ) {
        if (code == otpCode) {
            otpCode = null
            onSuccess.invoke()
        } else {
            onFailure.invoke()
        }
    }
    
    private fun handleOtpRequestResult(status: SendOtpStatus) {
        when (status) {
            is SendOtpStatus.Success -> {
                // OTP sent successfully
                Toast.makeText(this, getString(R.string.otp_sent_successfully), Toast.LENGTH_SHORT).show()
            }
            is SendOtpStatus.Error -> {
                // Error sending OTP
                Toast.makeText(this, getString(R.string.error_sending_otp), Toast.LENGTH_SHORT).show()
            }
            is SendOtpStatus.Limited -> {
                // Rate limited
                Toast.makeText(this, getString(R.string.otp_request_limit_exceeded), Toast.LENGTH_SHORT).show()
            }
            is SendOtpStatus.Standby -> {
                // Show loading or standby state
            }
            is SendOtpStatus.None -> {
                // Reset state
            }
        }
    }
    
    /***
     *  Reset limit OTP every day at midnight using WorkManager
     */
    private fun resetLimitOtp() {
        // Thời gian hiện tại
        val now = Calendar.getInstance()

        // Đặt thời gian đến 0h00 ngày hôm sau
        val nextMidnight = Calendar.getInstance().apply {
            timeInMillis = now.timeInMillis
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            add(Calendar.DAY_OF_YEAR, 1) // sang ngày mai
        }

        val initialDelay = nextMidnight.timeInMillis - now.timeInMillis

        val workRequest = PeriodicWorkRequestBuilder<ResetLimitOtpWorker>(1, TimeUnit.DAYS)
            .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
            .build()

        WorkManager
            .getInstance(applicationContext)
            .enqueueUniquePeriodicWork(
                "ResetLimitOtpDaily",
                ExistingPeriodicWorkPolicy.REPLACE,
                workRequest
            )
    }

    private fun generateOtp(): String {
        val randomNumbers = (1..6).map { (0..9).random() }
        return randomNumbers.joinToString("")
    }

    override fun onDestroy() {
        super.onDestroy()
        turnOffFlashlight()
        hideOverlay()
        serviceScope?.cancel()
        serviceScope = null
        screenReceiver?.let { unregisterReceiver(it) }
        languageChangeReceiver?.let { unregisterReceiver(it) }
    }

}