package co.ziplock.repository

import kotlinx.coroutines.flow.Flow
import co.ziplock.framework.network.model.AppCategoryData
import co.ziplock.framework.presentation.model.template.TemplateDataResult
import co.ziplock.util.Result

interface ApiRepository {
    suspend fun getAppCategory(): Flow<Result<List<AppCategoryData>>>
    suspend fun<T> getCategoryData(categoryId: String): Flow<Result<List<T>>>
    // New method to load all template data with category mapping
    suspend fun loadAllTemplateDataWithCategories(): Flow<Result<TemplateDataResult>>
}