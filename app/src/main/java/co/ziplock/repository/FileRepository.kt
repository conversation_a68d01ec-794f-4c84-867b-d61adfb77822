package co.ziplock.repository

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import co.ziplock.framework.presentation.model.ImageDCIMDataItem
import co.ziplock.framework.presentation.model.pickphoto.PhotoAlbum
import java.io.File

interface FileRepository {
    fun saveBitmapToInternalStorage(bitmap: Bitmap, filename: String): String?
    suspend fun saveImageToDCIM(
        bitmap: Bitmap,
        saveFolder: String,
        onSuccess: suspend (savedUri: Uri) -> Unit = {},
        onError: suspend () -> Unit = {}
    ): Uri?

    suspend fun saveImageToCacheDir(
        bitmap: Bitmap,
        onSuccess: suspend (filePath: String?) -> Unit = {},
        onError: suspend () -> Unit = {}
    ): File?

    fun deleteCacheFile(fileName: String): Boolean

    suspend fun readImagesFromSpecificDCIMPath(
        pathSpecific: String,
        maxImages: Int = Int.MAX_VALUE
    ): List<ImageDCIMDataItem>

    suspend fun readAllImagesDCIM(
        maxImages: Int = Int.MAX_VALUE
    ): List<ImageDCIMDataItem>

    suspend fun deleteImageFromDCIM(
        filePath: String,
        onSuccess: suspend (String) -> Unit = {},
        onError: suspend () -> Unit = {}
    )

    suspend fun copyAllFilesToNewFolderDCIMDir(
        oldPath: String,
        newPath: String,
        isDeleteOldPath: Boolean
    ): Boolean

    fun makeDirInDCIMIfNotExist(path: String)

    fun getDefaultFileName(): String

    fun getFileNameFromPath(path: String?): String?

    suspend fun saveVideoToDCIM(
        sourceFile: File,
        targetPath: String,
        onSuccess: suspend (Uri) -> Unit = {},
        onError: suspend () -> Unit = {}
    ): Uri?


    suspend fun saveVideoToCacheDir(
        sourceFile: File,
        targetPath: String,
        onSuccess: suspend (Uri) -> Unit = {},
        onError: suspend () -> Unit = {}
    ): Uri?

    fun copyUriToTempFile(context: Context, uri: Uri): File?
    
    /**
     * Save bitmap to internal storage in Wallpaper folder
     * @param bitmap The bitmap to save
     * @param filename The filename to save as
     * @return The path to the saved file or null if saving failed
     */
    fun saveBitmapToWallpaperFolder(bitmap: Bitmap, filename: String): String?
    
    /**
     * Load wallpaper images from internal storage Wallpaper folder
     * @return List of file paths to wallpaper images
     */
    fun loadWallpaperImages(): List<String>
    
    /**
     * Get all photo albums from device
     * @return List of PhotoAlbum containing album information
     */
    suspend fun getAllPhotoAlbums(): List<PhotoAlbum>
    
    /**
     * Get photos from a specific album
     * @param albumId The album ID to get photos from
     * @param maxImages Maximum number of images to retrieve
     * @return List of ImageDCIMDataItem from the specified album
     */
    suspend fun getPhotosFromAlbum(albumId: Long, maxImages: Int = Int.MAX_VALUE): List<ImageDCIMDataItem>
    
    /**
     * Save bitmap to internal storage in Background folder
     * @param bitmap The bitmap to save
     * @param filename The filename to save as
     * @return The path to the saved file or null if saving failed
     */
    fun saveBitmapToBackgroundFolder(bitmap: Bitmap, filename: String): String?
    
    /**
     * Load background images from internal storage Background folder
     * @return List of file paths to background images
     */
    fun loadBackgroundImages(): List<String>
}