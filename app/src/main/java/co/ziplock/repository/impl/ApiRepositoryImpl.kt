package co.ziplock.repository.impl

import co.ziplock.framework.network.ApiInterface
import co.ziplock.framework.network.model.AppCategoryData
import co.ziplock.framework.network.model.BackgroundCategoryResponse
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.HotTrendingThemeResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.SoundCategoryResponse
import co.ziplock.framework.network.model.SoundResponse
import co.ziplock.framework.network.model.ThemeCategoryResponse
import co.ziplock.framework.network.model.WallpaperCategoryResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperCategoryResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.model.OnboardingFavoriteStyle
import co.ziplock.framework.presentation.model.template.BaseTemplateData
import co.ziplock.framework.presentation.model.template.CategoryTemplateData
import co.ziplock.framework.presentation.model.template.TemplateDataResult
import co.ziplock.repository.ApiRepository
import co.ziplock.util.Constant
import co.ziplock.util.Result
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import timber.log.Timber
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.collections.get

@Singleton
class ApiRepositoryImpl
@Inject
constructor(
    private val apiInterface: ApiInterface,
) : ApiRepository {
    override suspend fun getAppCategory(): Flow<Result<List<AppCategoryData>>> =
        flow<Result<List<AppCategoryData>>> {
            emit(Result.Success(apiInterface.getAppCategory().dataResponse))
        }.catch {
            emit(Result.Error(it))
        }.flowOn(Dispatchers.IO)

    override suspend fun <T> getCategoryData(categoryId: String): Flow<Result<List<T>>> =
        flow<Result<List<T>>> {
            emit(
                Result.Success(
                    apiInterface
                        .getAllTemplate<T>(categoryId)
                        .dataResponse
                        .map {
                            it.customField
                        }.toList(),
                ),
            )
        }.catch {
            emit(Result.Error(it))
        }.flowOn(Dispatchers.IO)

    override suspend fun loadAllTemplateDataWithCategories(): Flow<Result<TemplateDataResult>> =
        flow {
            try {
                val gson = Gson()

                // Bước 1: Lấy categories và tạo map categoryId
                Timber.d("Step 1: Getting app categories...")
                val categoryIdMap = getAllCategoriesData()
                Timber.d("Step 1 completed: Found ${categoryIdMap.size} categories")

                // Bước 2: Lấy song song 5 API (Row, Sound, Background, Wallpaper, Zipper) cho base templates
                Timber.d("Step 2: Loading base template data in parallel...")
                val baseTemplateData = getBaseTemplateData(categoryIdMap, gson)
                Timber.d("Step 2 completed: Base template data loaded")

                // Bước 3: Lấy các category con và map với base data
                Timber.d("Step 3 Loading category template data...")
                val categoryTemplateData =
                    getAllCategoriesItemData(categoryIdMap, baseTemplateData, gson)
                Timber.d("Step 3 completed: Category template data loaded")

                // Bước 4: Lấy Hot Trending Themes Category
                Timber.d("Step 4: Loading hot trending themes category...")
                val hotTrendingThemesCategory = getHotTrendingThemesCategory(categoryIdMap)
                Timber.d("Step 4 completed: Found ${hotTrendingThemesCategory.size} hot trending themes category")

                // Bước 5: Lấy Hot Trending Themes và map với base data
                Timber.d("Step 5: Loading hot trending themes...")
                val hotTrendingThemes = getHotTrendingThemes(categoryIdMap, gson, hotTrendingThemesCategory)
                Timber.d("Step 5 completed: Found ${hotTrendingThemes.size} hot trending themes")

                val result =
                    TemplateDataResult(
                        categoryIdMap = categoryIdMap,
                        baseTemplateData = baseTemplateData,
                        hotTrendingThemes = hotTrendingThemes,
                        categoryTemplateData = categoryTemplateData,
                    )

                emit(Result.Success(result))
                Timber.d("All steps completed successfully")
            } catch (e: Exception) {
                Timber.e(e, "Error loading template data")
                emit(Result.Error(e))
            }
        }.flowOn(Dispatchers.IO)

    private suspend fun getHotTrendingThemesCategory(
        categoryIdMap: Map<String, String>,
    ): List<ThemeCategoryResponse>  {
        val categories = kotlin.runCatching {
            apiInterface.getAllTemplate<ThemeCategoryResponse>(categoryIdMap[Constant.REMOTE_TABLE_THEME_CATEGORY] ?: "").dataResponse
        }.getOrElse { emptyList() }
        return categories.map {
            ThemeCategoryResponse(it.id, it.name)
        }
    }

    private suspend fun getAllCategoriesData(): Map<String, String> {
        val categories = kotlin.runCatching { apiInterface.getAppCategory().dataResponse }
            .getOrDefault(emptyList())
        val categoryIdMap =
            buildMap {
                categories.forEach { category ->
                    put(category.name, category.id)
                }
            }

        return categoryIdMap
    }

    private suspend fun getBaseTemplateData(
        categoryIdMap: Map<String, String>,
        gson: Gson,
    ): BaseTemplateData =
        coroutineScope {
            val rowDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_ROW]?.let { categoryId ->
                        buildMap {
                            kotlin.runCatching { apiInterface.getAllTemplate<RowResponse>(categoryId).dataResponse }
                                .getOrElse { emptyList() }.forEach {
                                    val rowResponse = kotlin.runCatching {
                                        gson.fromJson(
                                            gson.toJson(it.customField),
                                            RowResponse::class.java
                                        )
                                    }.getOrElse {
                                        RowResponse(
                                            id = UUID.randomUUID().toString(),
                                            isPro = false,
                                            imageRowLeft = "",
                                            imageRowRight = "",
                                            previewThumbnail = "",
                                            type = "",
                                            percentToothSpacing = 1f,
                                            drawHeight = 60,
                                            toothCenterOffset = 0.186f,
                                        )
                                    }
                                    put(it.id, rowResponse.copy(id = it.id, isPro = it.isPro))
                                }
                        }
                    }
                        ?: emptyMap()
                }
            val soundDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_SOUND]?.let { categoryId ->
                        buildMap {
                            kotlin.runCatching {
                                apiInterface.getAllTemplate<SoundResponse>(
                                    categoryId
                                ).dataResponse
                            }.getOrElse { emptyList() }.forEach {
                                val soundResponse = kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(it.customField),
                                        SoundResponse::class.java,
                                    )
                                }.getOrElse {
                                    SoundResponse(
                                        id = UUID.randomUUID().toString(),
                                        name = "",
                                        fileUrl = "",
                                        thumbnailUrl = "",
                                    )
                                }

                                // Replace name by App Name
                                put(it.id, soundResponse.copy(name = it.name))
                            }
                        }
                    } ?: emptyMap()
                }
            val backgroundDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_BACKGROUND_AND_WALLPAPER]?.let { categoryId ->
                        buildMap {
                            kotlin.runCatching {
                                apiInterface.getAllTemplate<BackgroundResponse>(
                                    categoryId
                                ).dataResponse
                            }.getOrElse { emptyList() }.forEach {
                                val backgroundResponse = kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(it.customField),
                                        BackgroundResponse::class.java,
                                    )
                                }.getOrElse {
                                    BackgroundResponse(
                                        fileUrl = "",
                                        previewThumbnail = null
                                    )
                                }
                                put(
                                    it.id,
                                    backgroundResponse,
                                )
                            }
                        }
                    } ?: emptyMap()
                }
            val wallpaperDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_BACKGROUND_AND_WALLPAPER]?.let { categoryId ->
                        buildMap {
                            kotlin.runCatching {
                                apiInterface.getAllTemplate<WallpaperResponse>(
                                    categoryId
                                ).dataResponse
                            }.getOrElse { emptyList() }.forEach {
                                val wallpaperResponse = kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(it.customField),
                                        WallpaperResponse::class.java,
                                    )
                                }.getOrElse {
                                    WallpaperResponse(
                                        fileUrl = "",
                                        previewThumbnail = null
                                    )
                                }

                                put(
                                    it.id,
                                    wallpaperResponse,
                                )
                            }
                        }
                    } ?: emptyMap()
                }

            val zipperDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_ZIPPER]?.let { categoryId ->
                        buildMap {
                            kotlin.runCatching {
                                apiInterface.getAllTemplate<ZipperResponse>(
                                    categoryId
                                ).dataResponse
                            }.getOrElse { emptyList() }.forEach {
                                val zipperResponse =
                                    kotlin.runCatching {
                                        gson.fromJson(
                                            gson.toJson(it.customField),
                                            ZipperResponse::class.java,
                                        )
                                    }.getOrElse {
                                        ZipperResponse(
                                            id = UUID.randomUUID().toString(),
                                            fileUrl = "",
                                            drawHeight = 60,
                                            isPro = false,
                                        )
                                    }

                                put(it.id, zipperResponse.copy(id = it.id, isPro = it.isPro))
                            }
                        }
                    } ?: emptyMap()
                }

            val onboardingStyleDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_ONBOARDING_STYLES]?.let {
                        kotlin.runCatching {
                            apiInterface.getAllTemplate<OnboardingFavoriteStyle>(it).dataResponse
                        }.getOrElse { emptyList() }.map { style ->
                            val result =
                                kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(style.customField),
                                        OnboardingFavoriteStyle::class.java,
                                    )
                                }.getOrElse {
                                    OnboardingFavoriteStyle(
                                        name = "",
                                        thumbnailUrl = "",
                                        isShow = true,
                                        idThemes = null,
                                    )
                                }
                            result
                        }
                    } ?: emptyList()
                }

            val results =
                awaitAll(
                    rowDeferred,
                    soundDeferred,
                    backgroundDeferred,
                    wallpaperDeferred,
                    zipperDeferred,
                    onboardingStyleDeferred,
                )
            BaseTemplateData(
                rowMap = results[0] as Map<String, RowResponse>,
                soundMap = results[1] as Map<String, SoundResponse>,
                backgroundMap = results[2] as Map<String, BackgroundResponse>,
                wallpaperMap = results[3] as Map<String, WallpaperResponse>,
                zipperMap = results[4] as Map<String, ZipperResponse>,
                onboardingStyles = results[5] as List<OnboardingFavoriteStyle>,
            )
        }

    private suspend fun getHotTrendingThemes(
        categoryIdMap: Map<String, String>,
        gson: Gson,
        hotTrendingThemesCategory: List<ThemeCategoryResponse>,
    ): List<HotTrendingThemeResponse> =
        categoryIdMap[Constant.REMOTE_TABLE_HOT_TRENDING_THEME]?.let { categoryId ->
            kotlin.runCatching {
                apiInterface.getAllTemplate<HotTrendingThemeResponse>(categoryId).dataResponse
            }.getOrElse { emptyList() }.map {
                val result =
                    kotlin.runCatching {
                        val response = gson.fromJson(
                            gson.toJson(it.customField),
                            HotTrendingThemeResponse::class.java
                        )

                        response.copy(
                            idCategory = response.idCategory?.let { categoryId ->
                                hotTrendingThemesCategory.find { it.id == categoryId }?.id
                            }
                        )
                    }.getOrElse {
                        HotTrendingThemeResponse(
                            id = UUID.randomUUID().toString(),
                            isPro = false,
                            idRow = "",
                            idBackground = "",
                            idSound = "",
                            idWallpaper = "",
                            idZipper = "",
                            previewThumbnail = "",
                            idCategory = null
                        )
                    }

                result.copy(id = it.id, isPro = it.isPro)
            }
        } ?: emptyList()

    private suspend fun getAllCategoriesItemData(
        categoryIdMap: Map<String, String>,
        baseTemplateData: BaseTemplateData,
        gson: Gson,
    ): CategoryTemplateData =
        coroutineScope {
            val soundCategoriesDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_SOUND_CATEGORY]?.let { categoryId ->
                        loadSoundCategoryTemplateData(categoryId, gson)
                    } ?: emptyMap()
                }
            val backgroundCategoriesDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_BACKGROUND_CATEGORY]?.let { categoryId ->
                        loadBackgroundCategoryTemplateData(categoryId, gson)
                    } ?: emptyMap()
                }
            val wallpaperCategoriesDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_WALLPAPER_CATEGORY]?.let { categoryId ->
                        loadWallpaperCategoryTemplateData(categoryId, gson)
                    } ?: emptyMap()
                }
            val zipperCategoriesDeferred =
                async {
                    categoryIdMap[Constant.REMOTE_TABLE_ZIPPER_CATEGORY]?.let { categoryId ->
                        loadZipperCategoryTemplateData(categoryId, gson)
                    } ?: emptyMap()
                }

            val categoryResults =
                awaitAll(
                    soundCategoriesDeferred,
                    backgroundCategoriesDeferred,
                    wallpaperCategoriesDeferred,
                    zipperCategoriesDeferred,
                )

            val soundCategories = categoryResults[0] as Map<String, List<SoundCategoryResponse>>
            val backgroundCategories =
                categoryResults[1] as Map<String, List<BackgroundCategoryResponse>>
            val wallpaperCategories =
                categoryResults[2] as Map<String, List<WallpaperCategoryResponse>>
            val zipperCategories = categoryResults[3] as Map<String, List<ZipperCategoryResponse>>

            val soundCategoriesItem =
                soundCategories
                    .mapNotNull { item ->
                        val categoryItem =
                            item.value.mapNotNull { value ->
                                baseTemplateData.soundMap[value.soundId]
                            }
                        item.key to categoryItem
                    }.toMap()

            val backgroundCategoriesItem =
                backgroundCategories
                    .mapNotNull { item ->
                        val categoryItem =
                            item.value.mapNotNull { value ->
                                baseTemplateData.backgroundMap[value.backgroundId]
                            }
                        item.key to categoryItem
                    }.toMap()

            val wallpaperCategoriesItem =
                wallpaperCategories
                    .mapNotNull { item ->
                        val categoryItem =
                            item.value.mapNotNull { value ->
                                baseTemplateData.wallpaperMap[value.wallpaperId]
                            }
                        item.key to categoryItem
                    }.toMap()

            val zipperCategoriesItem =
                zipperCategories
                    .mapNotNull { item ->
                        val categoryItem =
                            item.value.mapNotNull { value ->
                                baseTemplateData.zipperMap[value.zipperId]
                            }
                        item.key to categoryItem
                    }.toMap()

            CategoryTemplateData(
                soundCategories = soundCategoriesItem,
                backgroundCategories = backgroundCategoriesItem,
                wallpaperCategories = wallpaperCategoriesItem,
                zipperCategories = zipperCategoriesItem,
            )
        }

    private suspend fun loadSoundCategoryTemplateData(
        parentCategoryId: String,
        gson: Gson,
    ): Map<String, List<SoundCategoryResponse>> =
        try {
            // Lấy danh sách category con
            val subCategories =
                apiInterface.getAllSubCategoriesByParentId(parentCategoryId).dataResponse

            // Lấy song song data cho từng category con
            coroutineScope {
                kotlin.runCatching {
                    val deferredResults =
                        (subCategories ?: emptyList()).map { categoryTemplate ->
                            async {
                                val categoryData = kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(categoryTemplate),
                                        AppCategoryData::class.java,
                                    )
                                }.getOrElse {
                                    AppCategoryData(
                                        id = UUID.randomUUID().toString(),
                                        name = "",
                                        parentId = "",
                                    )
                                }

                                val categoryName = categoryData.name
                                val categoryItems =
                                    (apiInterface.getAllTemplate<SoundCategoryResponse>(categoryData.id).dataResponse ?: emptyList()).map {
                                        gson.fromJson(gson.toJson(it.customField), SoundCategoryResponse::class.java)
                                    }
                                categoryName to categoryItems
                            }
                        }

                    deferredResults.awaitAll().toMap()
                }.getOrElse { emptyMap() }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error loading sound category template data for parent: $parentCategoryId")
            emptyMap()
        }

    private suspend fun loadBackgroundCategoryTemplateData(
        parentCategoryId: String,
        gson: Gson,
    ): Map<String, List<BackgroundCategoryResponse>> =
        try {
            // Lấy danh sách category con
            val subCategories =
                apiInterface.getAllSubCategoriesByParentId(parentCategoryId).dataResponse

            // Lấy song song data cho từng category con
            coroutineScope {
                kotlin.runCatching {
                    val deferredResults =
                        (subCategories ?: emptyList()).map { categoryTemplate ->
                            async {
                                val categoryData = kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(categoryTemplate),
                                        AppCategoryData::class.java,
                                    )
                                }.getOrElse {
                                    AppCategoryData(
                                        id = UUID.randomUUID().toString(),
                                        name = "",
                                        parentId = "",
                                    )
                                }

                                val categoryName = categoryData.name
                                val categoryItems =
                                    (apiInterface.getAllTemplate<BackgroundCategoryResponse>(categoryData.id).dataResponse ?: emptyList()).map {
                                        gson.fromJson(gson.toJson(it.customField), BackgroundCategoryResponse::class.java)
                                    }
                                categoryName to categoryItems
                            }
                        }

                    deferredResults.awaitAll().toMap()
                }.getOrElse { emptyMap() }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error loading background category template data for parent: $parentCategoryId")
            emptyMap()
        }

    private suspend fun loadWallpaperCategoryTemplateData(
        parentCategoryId: String,
        gson: Gson,
    ): Map<String, List<WallpaperCategoryResponse>> =
        try {
            // Lấy danh sách category con
            val subCategories =
                apiInterface.getAllSubCategoriesByParentId(parentCategoryId).dataResponse

            // Lấy song song data cho từng category con
            coroutineScope {
                kotlin.runCatching {
                    val deferredResults =
                        (subCategories ?: emptyList()).map { categoryTemplate ->
                            async {
                                val categoryData = kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(categoryTemplate),
                                        AppCategoryData::class.java,
                                    )
                                }.getOrElse {
                                    AppCategoryData(
                                        id = UUID.randomUUID().toString(),
                                        name = "",
                                        parentId = "",
                                    )
                                }

                                val categoryName = categoryData.name
                                val categoryItems =
                                    (apiInterface.getAllTemplate<WallpaperCategoryResponse>(categoryData.id).dataResponse ?: emptyList()).map {
                                        gson.fromJson(gson.toJson(it.customField), WallpaperCategoryResponse::class.java)
                                    }
                                categoryName to categoryItems
                            }
                        }

                    deferredResults.awaitAll().toMap()
                }.getOrElse { emptyMap() }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error loading wallpaper category template data for parent: $parentCategoryId")
            emptyMap()
        }

    private suspend fun loadZipperCategoryTemplateData(
        parentCategoryId: String,
        gson: Gson,
    ): Map<String, List<ZipperCategoryResponse>> =
        try {
            // Lấy danh sách category con
            val subCategories =
                apiInterface.getAllSubCategoriesByParentId(parentCategoryId).dataResponse

            // Lấy song song data cho từng category con
            coroutineScope {
                kotlin.runCatching {
                    val deferredResults =
                        (subCategories ?: emptyList()).map { categoryTemplate ->
                            async {
                                val categoryData = kotlin.runCatching {
                                    gson.fromJson(
                                        gson.toJson(categoryTemplate),
                                        AppCategoryData::class.java,
                                    )
                                }.getOrElse {
                                    AppCategoryData(
                                        id = UUID.randomUUID().toString(),
                                        name = "",
                                        parentId = "",
                                    )
                                }

                                val categoryName = categoryData.name
                                val categoryItems =
                                    (apiInterface.getAllTemplate<ZipperCategoryResponse>(categoryData.id).dataResponse ?: emptyList()).map {
                                        gson.fromJson(gson.toJson(it.customField), ZipperCategoryResponse::class.java)
                                    }
                                categoryName to categoryItems
                            }
                        }

                    deferredResults.awaitAll().toMap()
                }.getOrElse { emptyMap() }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error loading zipper category template data for parent: $parentCategoryId")
            emptyMap()
        }
}
