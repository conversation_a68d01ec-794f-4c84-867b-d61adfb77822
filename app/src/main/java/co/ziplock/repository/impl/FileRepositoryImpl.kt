package co.ziplock.repository.impl

import android.Manifest
import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import co.ziplock.framework.presentation.model.ImageDCIMDataItem
import co.ziplock.framework.presentation.model.pickphoto.PhotoAlbum
import co.ziplock.repository.FileRepository
import co.ziplock.util.Constant
import timber.log.Timber
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

@Singleton
class FileRepositoryImpl @Inject constructor(
    @ApplicationContext
    private val context: Context
) : FileRepository {

    companion object {
        const val TAG = "FileRepository"
        const val DEFAULT_ZIPLOCK_FOLDER_PATH = "Piontech/Ziplock"
    }

    /**
     * Save bitmap to internal storage
     * @param bitmap The bitmap to save
     * @param filename The filename to save as
     * @return The path to the saved file or null if saving failed
     */
    override fun saveBitmapToInternalStorage(bitmap: Bitmap, filename: String): String? {
        return try {
            val directory = File(context.filesDir, "location_images")
            if (!directory.exists()) {
                directory.mkdirs()
            }

            val file = File(directory, "$filename.jpg")
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }

            file.absolutePath
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }

    override suspend fun saveImageToDCIM(
        bitmap: Bitmap,
        saveFolder: String,
        onSuccess: suspend (savedUri: Uri) -> Unit,
        onError: suspend () -> Unit
    ): Uri? {
        Log.d(TAG, "saveImageToDCIM: saveFolder=$saveFolder")

        // Kiểm tra quyền cho Android 13
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.READ_MEDIA_IMAGES
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                Timber.Forest.tag(TAG).e("READ_MEDIA_IMAGES permission not granted.")
                onError()
                return null
            }
        }

        makeDirInDCIMIfNotExist(saveFolder)

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            saveImageAndroidQ(bitmap, saveFolder, onSuccess, onError)
        } else {
            saveImageLegacy(bitmap, saveFolder, onSuccess, onError)
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private suspend fun saveImageAndroidQ(
        bitmap: Bitmap,
        saveFolder: String,
        onSuccess: suspend (savedUri: Uri) -> Unit,
        onError: suspend () -> Unit
    ): Uri? {
        val collection = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
        val contentValues = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, getDefaultFileName())
            put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
            val path = "${Environment.DIRECTORY_DCIM}/${saveFolder}"
            put(MediaStore.Images.Media.RELATIVE_PATH, path)
        }

        var imageUri: Uri? = null
        var outputStream: OutputStream? = null

        try {
            imageUri = context.contentResolver.insert(collection, contentValues)
                ?: throw Exception("Failed to create new MediaStore record.")

            outputStream = context.contentResolver.openOutputStream(imageUri)
                ?: throw Exception("Failed to get output stream.")

            if (!bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)) {
                throw Exception("Failed to save bitmap.")
            }

            onSuccess(imageUri)
            return imageUri

        } catch (e: Exception) {
            e.printStackTrace()
            Timber.Forest.tag(TAG).e("saveImageAndroidQ: exception=${e.message}")
            imageUri?.let { uri -> context.contentResolver.delete(uri, null, null) }
            onError()
            return null
        } finally {
            withContext(Dispatchers.IO) {
                outputStream?.close()
            }
        }
    }

    private suspend fun saveImageLegacy(
        bitmap: Bitmap,
        saveFolder: String,
        onSuccess: suspend (savedUri: Uri) -> Unit,
        onError: suspend () -> Unit
    ): Uri? {
        val dcimDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
        val targetDir = File(dcimDir, saveFolder)
        val fileName = getDefaultFileName()
        val imageFile = File(targetDir, fileName)

        var outputStream: FileOutputStream? = null

        try {
            withContext(Dispatchers.IO) {
                outputStream = FileOutputStream(imageFile)
                if (!bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream!!)) {
                    throw Exception("Failed to save bitmap.")
                }
            }

            // Trigger media scanner to add the image to MediaStore
            MediaScannerConnection.scanFile(
                context,
                arrayOf(imageFile.absolutePath),
                arrayOf("image/jpeg")
            ) { _, uri ->
                Timber.Forest.tag(TAG).d("saveImageLegacy: uri=$uri")
                if (uri != null) {
                    runBlocking { onSuccess(uri) }
                } else {
                    runBlocking { onError() }
                }
            }

            return Uri.fromFile(imageFile)

        } catch (e: Exception) {
            e.printStackTrace()
            Timber.Forest.tag(TAG).e("saveImageLegacy: exception=${e.message}")
            imageFile.delete()
            onError()
            return null
        } finally {
            withContext(Dispatchers.IO) {
                outputStream?.close()
            }
        }
    }

    override suspend fun saveImageToCacheDir(
        bitmap: Bitmap,
        onSuccess: suspend (filePath: String?) -> Unit,
        onError: suspend () -> Unit
    ): File? {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, getDefaultFileName())
        var fos: FileOutputStream? = null
        try {
            withContext(Dispatchers.IO) {
                fos = FileOutputStream(file)
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos!!)
                onSuccess(file.path)
                return@withContext file
            }
        } catch (e: IOException) {
            onError()
            e.printStackTrace()
        } finally {
            try {
                withContext(Dispatchers.IO) {
                    fos?.close()
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return null
    }

    /**
     * Xóa một file cụ thể từ thư mục cacheDir của ứng dụng.
     *
     * @param fileName Tên của file cần xóa.
     * @return true nếu xóa thành công, false nếu file không tồn tại hoặc có lỗi xảy ra.
     */
    override fun deleteCacheFile(fileName: String): Boolean {
        val cacheDir = context.cacheDir
        val fileToDelete = File(cacheDir, fileName)

        if (fileToDelete.exists()) {
            if (fileToDelete.isFile) {
                if (fileToDelete.delete()) {
                    Timber.Forest.tag(TAG).d("Đã xóa file cache: $fileName")
                    return true
                } else {
                    Timber.Forest.tag(TAG).e("Không thể xóa file cache: $fileName")
                    return false
                }
            } else {
                Timber.Forest.tag(TAG).w("Không phải là file: $fileName trong cacheDir.")
                return false
            }
        } else {
            Timber.Forest.tag(TAG).w("File không tồn tại trong cacheDir: $fileName")
            return false
        }
    }

    override fun getDefaultFileName(): String {
        return "CameraGps_${System.currentTimeMillis()}.jpg"
    }

    override fun getFileNameFromPath(path: String?): String? {
        return File(path).name
    }

    override suspend fun saveVideoToDCIM(
        sourceFile: File,
        targetPath: String,
        onSuccess: suspend (Uri) -> Unit,
        onError: suspend () -> Unit
    ): Uri? {
        Log.d(TAG, "saveVideoToDCIM: targetPath=$targetPath")

        // Kiểm tra quyền cho Android 13
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.READ_MEDIA_VIDEO
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                Timber.Forest.tag(TAG).e("READ_MEDIA_VIDEO permission not granted.")
                onError()
                return null
            }
        }

        makeDirInDCIMIfNotExist(targetPath)

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            saveVideoAndroidQ(sourceFile, targetPath, onSuccess, onError)
        } else {
            saveVideoLegacy(sourceFile, targetPath, onSuccess, onError)
        }
    }

    override suspend fun saveVideoToCacheDir(
        sourceFile: File,
        targetPath: String,
        onSuccess: suspend (Uri) -> Unit,
        onError: suspend () -> Unit
    ): Uri? {
        val cacheDir = context.cacheDir
        val targetFile = File(cacheDir, getDefaultVideoFileName())

        try {
            copyFile(sourceFile, targetFile)
            val uri = Uri.fromFile(targetFile)
            onSuccess(uri)
            return uri
        } catch (e: IOException) {
            e.printStackTrace()
            Timber.Forest.tag(TAG).e("saveVideoToCacheDir: exception=${e.message}")
            onError()
        }
        return null
    }

    override fun copyUriToTempFile(
        context: Context,
        uri: Uri
    ): File? {
        val inputStream = context.contentResolver.openInputStream(uri) ?: return null
        val fileName = "temp_file_" + System.currentTimeMillis()
        val tempFile = File(context.cacheDir, fileName)

        inputStream.use { input ->
            FileOutputStream(tempFile).use { output ->
                input.copyTo(output)
            }
        }

        return tempFile
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private suspend fun saveVideoAndroidQ(
        sourceFile: File,
        targetPath: String,
        onSuccess: suspend (Uri) -> Unit,
        onError: suspend () -> Unit
    ): Uri? {
        val collection = MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
        val contentValues = ContentValues().apply {
            put(MediaStore.Video.Media.DISPLAY_NAME, getDefaultVideoFileName())
            put(MediaStore.Video.Media.MIME_TYPE, "video/mp4")
            val path = "${Environment.DIRECTORY_DCIM}/${targetPath}"
            put(MediaStore.Video.Media.RELATIVE_PATH, path)
        }

        var videoUri: Uri? = null
        var outputStream: OutputStream? = null
        var inputStream: InputStream? = null

        try {
            // Make sure the directory exists
            makeDirInDCIMIfNotExist(targetPath)

            videoUri = context.contentResolver.insert(collection, contentValues)
                ?: throw Exception("Failed to create new MediaStore record.")

            outputStream = context.contentResolver.openOutputStream(videoUri)
                ?: throw Exception("Failed to get output stream.")

            inputStream = FileInputStream(sourceFile)

            withContext(Dispatchers.IO) {
                inputStream.copyTo(outputStream)
            }

            onSuccess(videoUri)
            return videoUri

        } catch (e: Exception) {
            e.printStackTrace()
            Timber.Forest.tag(TAG).e("saveVideoAndroidQ: exception=${e.message}")
            videoUri?.let { uri -> context.contentResolver.delete(uri, null, null) }
            onError()
            return null
        } finally {
            withContext(Dispatchers.IO) {
                try {
                    inputStream?.close()
                } catch (e: Exception) {
                    Timber.Forest.tag(TAG).e("Error closing input stream: ${e.message}")
                }
                try {
                    outputStream?.close()
                } catch (e: Exception) {
                    Timber.Forest.tag(TAG).e("Error closing output stream: ${e.message}")
                }
            }
        }
    }

    private suspend fun saveVideoLegacy(
        sourceFile: File,
        targetPath: String,
        onSuccess: suspend (Uri) -> Unit,
        onError: suspend () -> Unit
    ): Uri? {
        val dcimDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
        val targetDir = File(dcimDir, targetPath)
        val fileName = getDefaultVideoFileName()
        val videoFile = File(targetDir, fileName)

        try {
            withContext(Dispatchers.IO) {
                copyFile(sourceFile, videoFile)
            }

            // Trigger media scanner to add the video to MediaStore
            MediaScannerConnection.scanFile(
                context,
                arrayOf(videoFile.absolutePath),
                arrayOf("video/mp4")
            ) { _, uri ->
                Timber.Forest.tag(TAG).d("saveVideoLegacy: uri=$uri")
                if (uri != null) {
                    runBlocking { onSuccess(uri) }
                } else {
                    runBlocking { onError() }
                }
            }

            return Uri.fromFile(videoFile)

        } catch (e: Exception) {
            e.printStackTrace()
            Timber.Forest.tag(TAG).e("saveVideoLegacy: exception=${e.message}")
            videoFile.delete()
            onError()
            return null
        }
    }

    private fun getDefaultVideoFileName(): String {
        return "CameraGps_${System.currentTimeMillis()}.mp4"
    }

    override suspend fun readImagesFromSpecificDCIMPath(
        pathSpecific: String,
        maxImages: Int,
    ): List<ImageDCIMDataItem> = withContext(
        Dispatchers.IO
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return@withContext readAllImagesFromSpecificDCIMPathAndroid10(pathSpecific, maxImages)
        } else {
            return@withContext getFilesFromSpecificDCIMPathPreAndroid10(pathSpecific, maxImages)
        }
    }

    override suspend fun readAllImagesDCIM(
        maxImages: Int
    ): List<ImageDCIMDataItem> = withContext(Dispatchers.IO) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return@withContext readAllImagesFromDCIMPathAndroid10(maxImages)
        } else {
            return@withContext getAllImageFromDCIMPreAndroid10(maxImages)
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    suspend fun readAllImagesFromDCIMPathAndroid10(
        maxImages: Int
    ) = suspendCoroutine<List<ImageDCIMDataItem>> {
        // Xác định nơi lưu trữ (Bộ sưu tập Hình ảnh)
        val collection = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)

        val projection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media.DATA // Thêm cột DATA để lấy đường dẫn đầy đủ
        )

        val sortOrder = MediaStore.Images.Media.DATE_ADDED + " DESC"

        val result = mutableListOf<ImageDCIMDataItem>()
        val imageCount = AtomicInteger(0)
        context.contentResolver.query(collection, projection, null, null, sortOrder)
            .use { cursor ->
                if (cursor != null && cursor.moveToFirst()) {
                    do {
                        val id =
                            cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID))
                        val contentUri = ContentUris.withAppendedId(
                            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                            id
                        )
                        val fullPath =
                            cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                        val file = File(fullPath)
                        if (file.exists()) {
                            result.add(
                                ImageDCIMDataItem(
                                    imageUri = contentUri,
                                    imagePath = fullPath
                                )
                            )
                            imageCount.incrementAndGet()
                        }
                    } while (cursor.moveToNext() && imageCount.get() < maxImages)

                    it.resume(result)
                }
            }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    suspend fun readAllImagesFromSpecificDCIMPathAndroid10(
        pathSpecific: String,
        maxImages: Int
    ) = suspendCoroutine<List<ImageDCIMDataItem>> {
        // Xác định nơi lưu trữ (Bộ sưu tập Hình ảnh)
        val collection = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)

        val projection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media.DATA // Thêm cột DATA để lấy đường dẫn đầy đủ
        )

        val sortOrder = MediaStore.Images.Media.DATE_ADDED + " DESC"

        // Đường dẫn bạn muốn lọc
        val targetPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
            .toString() + "/" + pathSpecific

        // Tạo mệnh đề WHERE để lọc theo đường dẫn
        val selection = MediaStore.Images.Media.DATA + " LIKE ?"
        val selectionArgs =
            arrayOf("$targetPath%") // Lọc các file có đường dẫn bắt đầu bằng targetPath

        val result = mutableListOf<ImageDCIMDataItem>()
        val imageCount = AtomicInteger(0)
        context.contentResolver.query(collection, projection, selection, selectionArgs, sortOrder)
            .use { cursor ->
                if (cursor != null && cursor.moveToFirst()) {
                    do {
                        val id =
                            cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID))
                        val contentUri = ContentUris.withAppendedId(
                            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                            id
                        )
                        val fullPath =
                            cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                        val file = File(fullPath)
                        if (file.exists() && file.canWrite()) {
                            result.add(
                                ImageDCIMDataItem(
                                    imageUri = contentUri,
                                    imagePath = fullPath
                                )
                            )
                            imageCount.incrementAndGet()
                        }
                    } while (cursor.moveToNext() && imageCount.get() < maxImages)
                    it.resume(result)
                } else {
                    Timber.Forest.tag(TAG).d("Không tìm thấy ảnh nào trong đường dẫn: $targetPath")
                    it.resume(result)
                }
            }
    }

    private fun getFilesFromSpecificDCIMPathPreAndroid10(
        specificPath: String,
        maxImages: Int
    ): List<ImageDCIMDataItem> {
        val dcimDir =
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).absolutePath + "/$specificPath"
        val dcimFiles = File(dcimDir)

        // Mutable list to collect all images
        val allImages = mutableListOf<File>()

        // Recursively collect all image files
        collectImageFilesRecursively(dcimFiles, allImages)

        return allImages
            .filter {
                val file = File(it.path)
                file.exists() && file.canWrite()
            }
            .take(if (maxImages > 0) maxImages else Int.MAX_VALUE)
            .sortedByDescending { it.lastModified() }
            .map {
                ImageDCIMDataItem(
                    imageUri = Uri.fromFile(it),
                    imagePath = it.absolutePath
                )
            }.also {
                Timber.d("getFilesFromSpecificDCIMPathPreAndroid10: $it")
            }
    }

    private fun getAllImageFromDCIMPreAndroid10(
        maxImages: Int
    ): List<ImageDCIMDataItem> {
        val dcimDir =
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).absolutePath
        val dcimFiles = File(dcimDir)

        // Mutable list to collect all images
        val allImages = mutableListOf<File>()

        // Recursively collect all image files
        collectImageFilesRecursively(dcimFiles, allImages)

        return allImages
            .filter {
                val file = File(it.path)
                file.exists()
            }
            .take(if (maxImages > 0) maxImages else Int.MAX_VALUE)
            .sortedByDescending { it.lastModified() }
            .map {
                ImageDCIMDataItem(
                    imageUri = Uri.fromFile(it),
                    imagePath = it.absolutePath
                )
            }.also {
                Timber.d("readAllImagesFromDCIMPathPreAndroid10: $it")
            }
    }

    /**
     * Recursively collects all image files from a directory and its subdirectories
     * @param directory The directory to search in
     * @param imageFiles Mutable list to collect the image files
     */
    private fun collectImageFilesRecursively(directory: File, imageFiles: MutableList<File>) {
        if (!directory.exists() || !directory.isDirectory) {
            return
        }

        directory.listFiles()?.forEach { file ->
            if (file.isDirectory) {
                // Recursively search in subdirectories
                collectImageFilesRecursively(file, imageFiles)
            } else if (isImageFile(file)) {
                // Add image file to the collection
                imageFiles.add(file)
            }
        }
    }

    /**
     * Checks if a file is an image file based on its extension
     * @param file The file to check
     * @return true if the file is an image file, false otherwise
     */
    private fun isImageFile(file: File): Boolean {
        val name = file.name.lowercase()
        return name.endsWith(".jpg") || name.endsWith(".jpeg") || name.endsWith(".png")
    }

    override suspend fun copyAllFilesToNewFolderDCIMDir(
        oldPath: String,
        newPath: String,
        isDeleteOldPath: Boolean
    ): Boolean = withContext(
        Dispatchers.IO
    ) {
        val dcimDir =
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).absolutePath
        val oldNormalizePath =
            validateAndNormalizeFolderPath("$dcimDir/$oldPath", defaultPath = oldPath)
        val newNormalizePath =
            validateAndNormalizeFolderPath("$dcimDir/$newPath", defaultPath = newPath)
        val oldFolder = File(oldNormalizePath)
        val newFolder = File(newNormalizePath)

        if (!oldFolder.exists()) {
            Timber.Forest.tag(TAG)
                .e("Folder cũ không tồn tại. oldPath=$oldPath, newPath=$newPath, oldFolder=${oldFolder.path}, newFolder=${newFolder.path}")
            return@withContext false
        }

        if (!oldFolder.isDirectory) {
            Timber.Forest.tag(TAG).e("Folder cũ không phải là thư mục.")
            return@withContext false
        }

        if (!newFolder.exists()) {
            if (!newFolder.mkdirs()) {
                Timber.Forest.tag(TAG).e("Không thể tạo thư mục mới.")
                return@withContext false
            }
        }

        val oldFiles = oldFolder.listFiles() ?: return@withContext true

        for (oldFile in oldFiles) {
            val destFile = File(newFolder.path, oldFile.name)
            try {
                if (oldFile.isFile) {
                    copyFile(oldFile, destFile)
                    if (isDeleteOldPath) {
                        deleteFile(oldFile)
                    }
                }
            } catch (e: IOException) {
                Timber.Forest.tag(TAG)
                    .e("Lỗi khi sao chép " + oldFile.absolutePath + " sang " + destFile.absolutePath + ": " + e.message)
                return@withContext false
            }
        }

        return@withContext true
    }

    private suspend fun copyFile(sourceFile: File, destFile: File) = withContext(Dispatchers.IO) {
        FileInputStream(sourceFile).use { fis ->
            FileOutputStream(destFile).use { fos ->
                fis.channel.use { sourceChannel ->
                    fos.channel.use { destChannel ->
                        destChannel.transferFrom(sourceChannel, 0, sourceChannel.size())
                    }
                }
            }
        }
    }

    /**
     * Xóa một file dựa trên đối tượng File được cung cấp.
     *
     * @param fileToDelete Đối tượng File cần xóa.
     * @return true nếu xóa thành công hoặc file không tồn tại, false nếu có lỗi.
     */
    private suspend fun deleteFile(fileToDelete: File?): Boolean = suspendCoroutine<Boolean> {
        if (fileToDelete == null) {
            Timber.Forest.tag(TAG).w("Attempted to delete a null file object.")
            it.resume(false) // Hoặc true tùy theo logic bạn muốn (nếu null coi như đã "xóa"?)
        }

        if (!fileToDelete!!.exists()) {
            Timber.Forest.tag(TAG)
                .i("File does not exist, no need to delete: " + fileToDelete.absolutePath)
            it.resume(true) // File không tồn tại, coi như mục tiêu "xóa" đã đạt được.
        }

        try {
            val deleted = fileToDelete.delete()
            if (deleted) {
                Timber.Forest.tag(TAG).i("Successfully deleted file: %s", fileToDelete.absolutePath)
                it.resume(true)
            } else {
                // Có thể file là một thư mục không rỗng, hoặc bị khóa bởi tiến trình khác
                Timber.Forest.tag(TAG).w("Failed to delete file: " + fileToDelete.absolutePath)
                it.resume(false)
            }
        } catch (e: SecurityException) {
            Timber.Forest.tag(TAG)
                .e(e, "Permission denied while deleting file: " + fileToDelete.absolutePath)
            it.resume(false)
        } catch (e: java.lang.Exception) {
            // Bắt các lỗi không mong muốn khác
            Timber.Forest.tag(TAG)
                .e(e, "Error occurred while deleting file: " + fileToDelete.absolutePath)
            it.resume(false)
        }
    }

    override fun makeDirInDCIMIfNotExist(path: String) {
        val dcimDir =
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).absolutePath
        val newFolder = File(dcimDir, path)
        if (!newFolder.exists()) {
            if (!newFolder.mkdirs()) {
                Timber.Forest.tag(TAG).e("makeMagnifierDirInDCIMIfNotExist: Không thể tạo thư mục mới.")
            }
        }
    }

    /**
     * Kiểm tra và chuẩn hóa folder path cho Android
     * @param folderPath đường dẫn thư mục cần kiểm tra
     * @return đường dẫn đã được chuẩn hóa
     */
    private fun validateAndNormalizeFolderPath(
        folderPath: String,
        defaultPath: String = DEFAULT_ZIPLOCK_FOLDER_PATH
    ): String {
        // Loại bỏ khoảng trắng đầu và cuối
        var normalizedPath = folderPath.trim()

        // Thay thế nhiều dấu "/" liên tiếp bằng một dấu "/"
        normalizedPath = normalizedPath.replace(Regex("/+"), "/")

        // Loại bỏ các ký tự không hợp lệ trong tên thư mục
        normalizedPath = normalizedPath.replace(Regex("[\\\\:*?\"<>|]"), "_")

        // Nếu path rỗng, trả về path mặc định
        if (normalizedPath.isBlank()) {
            return defaultPath
        }

        return normalizedPath
    }

    override suspend fun deleteImageFromDCIM(
        filePath: String,
        onSuccess: suspend (String) -> Unit,
        onError: suspend () -> Unit
    ) {
        // Kiểm tra quyền cho Android 13
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.READ_MEDIA_IMAGES
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                Timber.Forest.tag(TAG).e("READ_MEDIA_IMAGES permission not granted.")
                onError()
                return
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            deleteImageAndroidQ(filePath, onSuccess, onError)
        } else {
            deleteImageLegacy(filePath, onSuccess, onError)
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private suspend fun deleteImageAndroidQ(
        filePath: String,
        onSuccess: suspend (String) -> Unit,
        onError: suspend () -> Unit
    ) {
        try {
            val collection =
                MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
            val selection = "${MediaStore.Images.Media.DATA} = ?"
            val selectionArgs = arrayOf(filePath)

            val deletedRows = context.contentResolver.delete(
                collection,
                selection,
                selectionArgs
            )

            if (deletedRows > 0) {
                Timber.Forest.tag(TAG).d("deleteImageAndroidQ: Successfully deleted file: $filePath")
                onSuccess(filePath)
            } else {
                Timber.Forest.tag(TAG)
                    .e("deleteImageAndroidQ: File not found or couldn't be deleted: $filePath")
                onError()
            }
        } catch (e: Exception) {
            Timber.Forest.tag(TAG).e(e, "deleteImageAndroidQ: Failed to delete file: $filePath")
            onError()
        }
    }

    private suspend fun deleteImageLegacy(
        filePath: String,
        onSuccess: suspend (String) -> Unit,
        onError: suspend () -> Unit
    ) {
        try {
            val file = File(filePath)
            if (file.exists()) {
                if (file.delete()) {
                    // Notify MediaStore about the deletion
                    MediaScannerConnection.scanFile(
                        context,
                        arrayOf(filePath),
                        null
                    ) { path, uri ->
                        Timber.Forest.tag(TAG).d("deleteImageLegacy: Media scanner completed for: $path")
                    }

                    Timber.Forest.tag(TAG).d("deleteImageLegacy: Successfully deleted file: $filePath")
                    onSuccess(filePath)
                } else {
                    Timber.Forest.tag(TAG).e("deleteImageLegacy: Failed to delete file: $filePath")
                    onError()
                }
            } else {
                Timber.Forest.tag(TAG).e("deleteImageLegacy: File does not exist: $filePath")
                onError()
            }
        } catch (e: Exception) {
            Timber.Forest.tag(TAG).e(e, "deleteImageLegacy: Error deleting file: $filePath")
            onError()
        }
    }

    override fun saveBitmapToWallpaperFolder(bitmap: Bitmap, filename: String): String? {
        return try {
            val wallpaperDir = File(context.filesDir, Constant.FILES_DIRECTORY_WALLPAPER_LOCAL)
            if (!wallpaperDir.exists()) {
                wallpaperDir.mkdirs()
            }

            val file = File(wallpaperDir, "$filename.jpg")
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }

            file.absolutePath
        } catch (e: IOException) {
            Timber.Forest.tag(TAG).e(e, "saveBitmapToWallpaperFolder: Error saving bitmap")
            null
        }
    }

    override fun loadWallpaperImages(): List<String> {
        return try {
            val wallpaperDir = File(context.filesDir, Constant.FILES_DIRECTORY_WALLPAPER_LOCAL)
            if (!wallpaperDir.exists()) {
                return emptyList()
            }

            wallpaperDir.listFiles()
                ?.filter { file ->
                    file.isFile && (
                        file.extension.lowercase() == "jpg" ||
                        file.extension.lowercase() == "jpeg" ||
                        file.extension.lowercase() == "png"
                    )
                }
                ?.map { it.absolutePath }
                ?.sortedByDescending { File(it).lastModified() } // Sắp xếp theo thời gian mới nhất
                ?: emptyList()
        } catch (e: Exception) {
            Timber.Forest.tag(TAG).e(e, "loadWallpaperImages: Error loading wallpaper images")
            emptyList()
        }
    }

    override fun saveBitmapToBackgroundFolder(bitmap: Bitmap, filename: String): String? {
        return try {
            val backgroundDir = File(context.filesDir, Constant.FILES_DIRECTORY_BACKGROUND_LOCAL)
            if (!backgroundDir.exists()) {
                backgroundDir.mkdirs()
            }

            val file = File(backgroundDir, "$filename.jpg")
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }

            file.absolutePath
        } catch (e: IOException) {
            Timber.Forest.tag(TAG).e(e, "saveBitmapToBackgroundFolder: Error saving bitmap")
            null
        }
    }

    override fun loadBackgroundImages(): List<String> {
        return try {
            val backgroundDir = File(context.filesDir, Constant.FILES_DIRECTORY_BACKGROUND_LOCAL)
            if (!backgroundDir.exists()) {
                return emptyList()
            }

            backgroundDir.listFiles()
                ?.filter { file ->
                    file.isFile && (
                        file.extension.lowercase() == "jpg" ||
                        file.extension.lowercase() == "jpeg" ||
                        file.extension.lowercase() == "png"
                    )
                }
                ?.map { it.absolutePath }
                ?.sortedByDescending { File(it).lastModified() } // Sắp xếp theo thời gian mới nhất
                ?: emptyList()
        } catch (e: Exception) {
            Timber.Forest.tag(TAG).e(e, "loadBackgroundImages: Error loading background images")
            emptyList()
        }
    }

    override suspend fun getAllPhotoAlbums(): List<PhotoAlbum> = withContext(Dispatchers.IO) {
        try {
            // Check permission first
            val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Manifest.permission.READ_MEDIA_IMAGES
            } else {
                Manifest.permission.READ_EXTERNAL_STORAGE
            }

            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                Timber.Forest.tag(TAG).e("getAllPhotoAlbums: Permission not granted")
                return@withContext emptyList()
            }

            val albums = mutableListOf<PhotoAlbum>()
            val albumMap = mutableMapOf<String, MutableList<ImageDCIMDataItem>>()

            // Query all images first
            val projection = arrayOf(
                MediaStore.Images.Media._ID,
                MediaStore.Images.Media.DATA,
                MediaStore.Images.Media.BUCKET_DISPLAY_NAME,
                MediaStore.Images.Media.BUCKET_ID,
                MediaStore.Images.Media.DATE_MODIFIED
            )

            val collection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL)
            } else {
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            }

            val sortOrder = "${MediaStore.Images.Media.DATE_MODIFIED} DESC"

            context.contentResolver.query(
                collection,
                projection,
                null,
                null,
                sortOrder
            )?.use { cursor ->
                val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
                val dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                val bucketNameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.BUCKET_DISPLAY_NAME)
                val bucketIdColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.BUCKET_ID)

                while (cursor.moveToNext()) {
                    val id = cursor.getLong(idColumn)
                    val data = cursor.getString(dataColumn)
                    val bucketName = cursor.getString(bucketNameColumn) ?: "Unknown"
                    val bucketId = cursor.getString(bucketIdColumn) ?: ""

                    val contentUri = ContentUris.withAppendedId(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id
                    )

                    val imageItem = ImageDCIMDataItem(
                        imageUri = contentUri,
                        imagePath = data
                    )

                    // Group by album (bucket)
                    val albumKey = "$bucketId|$bucketName" // Use combination of ID and name as key
                    if (!albumMap.containsKey(albumKey)) {
                        albumMap[albumKey] = mutableListOf()
                    }
                    albumMap[albumKey]?.add(imageItem)
                }
            }

            // Convert map to PhotoAlbum list
            albumMap.forEach { (albumKey, photos) ->
                val bucketName = albumKey.split("|")[1]
                val bucketId = albumKey.split("|")[0]
                
                if (photos.isNotEmpty()) {
                    albums.add(
                        PhotoAlbum(
                            id = bucketId.hashCode().toLong(),
                            name = bucketName,
                            coverPhotoUri = photos.firstOrNull()?.imageUri,
                            photoCount = photos.size
                        )
                    )
                }
            }

            // Sort albums by photo count (descending) and then by name
            albums.sortedWith(compareByDescending<PhotoAlbum> { it.photoCount }.thenBy { it.name })
        } catch (e: Exception) {
            Timber.Forest.tag(TAG).e(e, "getAllPhotoAlbums: Error getting albums")
            emptyList()
        }
    }

    override suspend fun getPhotosFromAlbum(albumId: Long, maxImages: Int): List<ImageDCIMDataItem> = withContext(Dispatchers.IO) {
        try {
            // Check permission first
            val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Manifest.permission.READ_MEDIA_IMAGES
            } else {
                Manifest.permission.READ_EXTERNAL_STORAGE
            }

            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                Timber.Forest.tag(TAG).e("getPhotosFromAlbum: Permission not granted")
                return@withContext emptyList()
            }

            val photos = mutableListOf<ImageDCIMDataItem>()

            val projection = arrayOf(
                MediaStore.Images.Media._ID,
                MediaStore.Images.Media.DATA,
                MediaStore.Images.Media.BUCKET_ID,
                MediaStore.Images.Media.DATE_MODIFIED
            )

            val collection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL)
            } else {
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            }

            val sortOrder = "${MediaStore.Images.Media.DATE_MODIFIED} DESC"

            // Query all images and filter by bucket ID hash
            context.contentResolver.query(
                collection,
                projection,
                null,
                null,
                sortOrder
            )?.use { cursor ->
                val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
                val dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                val bucketIdColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.BUCKET_ID)

                var count = 0
                while (cursor.moveToNext() && count < maxImages) {
                    val bucketId = cursor.getString(bucketIdColumn) ?: ""
                    
                    // Check if this bucket's hash matches our album ID
                    if (bucketId.hashCode().toLong() == albumId) {
                        val id = cursor.getLong(idColumn)
                        val data = cursor.getString(dataColumn)

                        val contentUri = ContentUris.withAppendedId(
                            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                            id
                        )

                        photos.add(
                            ImageDCIMDataItem(
                                imageUri = contentUri,
                                imagePath = data
                            )
                        )
                        count++
                    }
                }
            }

            photos
        } catch (e: Exception) {
            Timber.Forest.tag(TAG).e(e, "getPhotosFromAlbum: Error getting photos from album $albumId")
            emptyList()
        }
    }
}