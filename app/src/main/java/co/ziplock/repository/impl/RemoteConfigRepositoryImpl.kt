package co.ziplock.repository.impl

import co.ziplock.BuildConfig
import co.ziplock.framework.presentation.model.remoteConfig.RemoteConfigData
import co.ziplock.repository.RemoteConfigRepository
import co.ziplock.util.Constant
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

@Singleton
class RemoteConfigRepositoryImpl
    @Inject
    constructor(
        private val remoteConfig: FirebaseRemoteConfig,
    ) : RemoteConfigRepository {
        companion object {
            private const val TAG = "RemoteConfigRepository"
            private const val TIMEOUT_MS = 7000L

            // Remote config keys
            private const val KEY_CONFIG_SHOW_ADS = "config_show_ads"
            private const val KEY_ADMOB_ID = "admob_id"
            const val KEY_BASE_URL_OTP = "base_url_otp"
            private const val KEY_SHOW_ONBOARDING1_FRAGMENT = "show_onboarding1_fragment"
            private const val KEY_SHOW_ONBOARDING2_FRAGMENT = "show_onboarding2_fragment"
            private const val KEY_SHOW_ONBOARDING3_FRAGMENT = "show_onboarding3_fragment"
            private const val KEY_SHOW_ONBOARDING4_FRAGMENT = "show_onboarding4_fragment"
            private const val KEY_NUMBER_OF_CONTENT_BETWEEN_THEME_LIST =
                "numberOfContentBetweenThemeList"
            private const val KEY_NUMBER_OF_CONTENT_BETWEEN_ZIP_LIST = "numberOfContentBetweenZipList"
            private const val KEY_NUMBER_OF_CONTENT_BETWEEN_ROW_LIST = "numberOfContentBetweenRowList"
            private const val KEY_NUMBER_OF_CONTENT_BETWEEN_SOUND_LIST = "numberOfContentBetweenSoundList"
            private const val KEY_NUMBER_OF_CONTENT_BETWEEN_BACKGROUND_LIST =
                "numberOfContentBetweenBackgroundList"
            private const val KEY_NUMBER_OF_CONTENT_BETWEEN_WALLPAPER_LIST =
                "numberOfContentBetweenWallpaperList"
        }

        private suspend fun fetchRemoteConfigData(): RemoteConfigData =
            suspendCancellableCoroutine { cont ->
                remoteConfig.fetchAndActivate().addOnCompleteListener { task ->
                    try {
                        val result = createRemoteConfigEntity(task.isSuccessful)
                        cont.resume(result)
                    } catch (e: Exception) {
                        Timber.tag(TAG).d("fetchRemoteConfigData: exception=$e")
                        cont.resume(getDefaultRemoteConfigData())
                    }
                }
            }

        private fun getDefaultRemoteConfigData(): RemoteConfigData = createRemoteConfigEntity(isRealData = false)

        private fun createRemoteConfigEntity(isRealData: Boolean): RemoteConfigData =
            RemoteConfigData(
                configShowAds = getStringValue(KEY_CONFIG_SHOW_ADS),
                isRealData = isRealData,
                admobId = getStringValue(KEY_ADMOB_ID),
                baseUrlOtp =
                    getStringValue(KEY_BASE_URL_OTP, BuildConfig.BASE_URL_OTP)
                        .ifEmpty { BuildConfig.BASE_URL_OTP }
                        .also { baseOtpUrl ->
                            Constant.BASE_OTP_URL = baseOtpUrl
                        },
                showOnboarding1Fragment = getBooleanValue(KEY_SHOW_ONBOARDING1_FRAGMENT, false),
                showOnboarding2Fragment = getBooleanValue(KEY_SHOW_ONBOARDING2_FRAGMENT, false),
                showOnboarding3Fragment = getBooleanValue(KEY_SHOW_ONBOARDING3_FRAGMENT, false),
                showOnboarding4Fragment = getBooleanValue(KEY_SHOW_ONBOARDING4_FRAGMENT, false),
                numberOfContentBetweenThemeList = getIntValue(KEY_NUMBER_OF_CONTENT_BETWEEN_THEME_LIST, 2),
                numberOfContentBetweenZipList = getIntValue(KEY_NUMBER_OF_CONTENT_BETWEEN_ZIP_LIST, 2),
                numberOfContentBetweenRowList = getIntValue(KEY_NUMBER_OF_CONTENT_BETWEEN_ROW_LIST, 2),
                numberOfContentBetweenSoundList = getIntValue(KEY_NUMBER_OF_CONTENT_BETWEEN_SOUND_LIST, 4),
                numberOfContentBetweenBackgroundList =
                    getIntValue(
                        KEY_NUMBER_OF_CONTENT_BETWEEN_BACKGROUND_LIST,
                        2,
                    ),
                numberOfContentBetweenWallpaperList =
                    getIntValue(
                        KEY_NUMBER_OF_CONTENT_BETWEEN_WALLPAPER_LIST,
                        2,
                    ),
            )

        private fun getStringValue(
            key: String,
            defaultValue: String = "",
        ): String =
            runCatching {
                remoteConfig.getString(key)
            }.getOrDefault(defaultValue)

        private fun getLongValue(
            key: String,
            defaultValue: Long = 0L,
        ): Long = runCatching { remoteConfig.getLong(key) }.getOrDefault(defaultValue)

        private fun getBooleanValue(
            key: String,
            defaultValue: Boolean = false,
        ): Boolean = runCatching { remoteConfig.getBoolean(key) }.getOrDefault(defaultValue)

        private fun getIntValue(
            key: String,
            defaultValue: Int = 0,
        ): Int =
            runCatching {
                remoteConfig.getLong(key).toInt()
            }.getOrDefault(defaultValue)

        override suspend fun fetchRemoteConfig(): RemoteConfigData =
            withTimeoutOrNull(TIMEOUT_MS) { fetchRemoteConfigData() }
                ?: getDefaultRemoteConfigData()
    }
