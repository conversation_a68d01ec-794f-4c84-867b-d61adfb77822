package co.ziplock.util

object BundleKey {
    const val KEY_PATTERN_CODE = "KEY_PATTERN_CODE"
    const val KEY_PIN_CODE = "KEY_PIN_CODE"
    const val KEY_FIRST_SETUP_PASSWORD = "KEY_FIRST_SETUP_PASSWORD"
    const val KEY_SECURITY_TYPE = "KEY_SECURITY_TYPE"
    const val KEY_FROM_CHANGE_SECURITY_QUESTION = "KEY_FROM_CHANGE_SECURITY_QUESTION"
    const val KEY_CURRENT_EMAIL = "KEY_CURRENT_EMAIL"
    const val KEY_FROM_CHANGE_EMAIL_SCREEN = "KEY_FROM_CHANGE_EMAIL_SCREEN"

    const val KEY_REQUEST_OPEN_CHOOSE_PASSWORD_FROM_FORGOT_PASSWORD_FLOW = "KEY_REQUEST_OPEN_CHOOSE_PASSWORD_FROM_FORGOT_PASSWORD_FLOW"
    const val KEY_ONBOARD_IAP = "KEY_ONBOARD_IAP"

    // Zipper customization keys
    const val ZIPPER_DATA = "ZIPPER_DATA"
    
    // Row customization keys
    const val ROW_DATA = "ROW_DATA"
    
    // Wallpaper customization keys
    const val WALLPAPER_DATA = "WALLPAPER_DATA"
    const val LOCAL_WALLPAPER_DATA = "LOCAL_WALLPAPER_DATA"
    
    // Background customization keys
    const val BACKGROUND_DATA = "BACKGROUND_DATA"
    const val LOCAL_BACKGROUND_DATA = "LOCAL_BACKGROUND_DATA"
    
    // Sound customization keys
    const val SOUND_DATA = "SOUND_DATA"

    const val KEY_SELECTED_PHOTO = "SELECTED_PHOTO"
    const val KEY_SAVE_CONTEXT = "SAVE_CONTEXT" // Context for saving (WALLPAPER, BACKGROUND)

    const val KEY_FROM_RE_EDIT_FLOW = "FROM_RE_EDIT_FLOW"
    
    // Font customization keys
    const val FONT_FAMILY_ID = "FONT_FAMILY_ID"
    const val FONT_COLOR_ID = "FONT_COLOR_ID"

    const val KEY_LANGUAGE_SETTING = "KEY_LANGUAGE_SETTING"

    const val KEY_IS_SHOW_ONBOARDING4_FRAGMENT = "KEY_IS_SHOW_ONBOARDING4_FRAGMENT"
}