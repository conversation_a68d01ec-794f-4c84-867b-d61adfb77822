package co.ziplock.util

import co.ziplock.BuildConfig

object Constant {
    var isPremium = false
    var isRemoteConfigSuccess = false

    var isShowOnboard1 = false
    var isShowOnboard2 = false
    var isShowOnboard3 = false
    var isShowOnboard4 = false
    const val iapId = "iapremove"

    const val iapGiaGach = "iapgiagach"
    const val ALL = "All"
    const val POPULAR = "Popular"
    const val APP_CMS_ID = "5e6cbfdc-db1c-4c43-a840-b78501ec46ef"
    const val REMOTE_TABLE_HOT_TRENDING_THEME = "Hot Trending Theme"

    const val REMOTE_TABLE_THEME_CATEGORY = "ThemeCategory"
    const val REMOTE_TABLE_ROW = "Row"
    const val REMOTE_TABLE_SOUND = "Sound"

    const val REMOTE_TABLE_BACKGROUND_AND_WALLPAPER = "Wallpaper & Background"
    const val REMOTE_TABLE_ONBOARDING_STYLES = "Onboarding Styles"
    const val REMOTE_TABLE_ZIPPER = "Zipper"

    const val REMOTE_TABLE_SOUND_CATEGORY = "Sound Category"
    const val REMOTE_TABLE_BACKGROUND_CATEGORY = "Background Category"
    const val REMOTE_TABLE_WALLPAPER_CATEGORY = "Wallpaper Category"
    const val REMOTE_TABLE_ZIPPER_CATEGORY = "Zipper Category"

    const val TIMEOUT_OTP = 300000L
    const val TIME_RESEND_OTP = 60000L

    const val TIMES_LIMITS_REQUEST_OTP = 5
    
    const val LOCAL_FILES = "Local Files"
    
    // Save context constants
    const val SAVE_CONTEXT_WALLPAPER = "WALLPAPER"
    const val SAVE_CONTEXT_BACKGROUND = "BACKGROUND"

    const val FILES_DIRECTORY_BACKGROUND_LOCAL = "Background"
    const val FILES_DIRECTORY_WALLPAPER_LOCAL = "Wallpaper"

    const val DEFAULT_DISPLAY_TIME = "00:00"

    const val TYPE_ROW_SPLIT = "split"
    const val TYPE_ROW_INTERLOCK = "interlock"
    
    // No Sound constant
    const val NO_SOUND_ID = "no_sound"
    const val NO_SOUND_URL = ""

    var BASE_OTP_URL = BuildConfig.BASE_URL_OTP

    const val NO_INDEX = -1

    var numberOfContentBetweenThemeList = 2
    var numberOfContentBetweenZipList = 2
    var numberOfContentBetweenRowList = 2
    var numberOfContentBetweenBackgroundList = 2
    var numberOfContentBetweenWallpaperList = 2
    var numberOfContentBetweenSoundList = 4

    var isLanguageToSplash = false
}