package co.ziplock.util

object EmailUtils {
    fun validateEmail(email: String): <PERSON><PERSON><PERSON> {
        if (email.isBlank()) return false

        // Check total email length (must not exceed 254 characters)
        if (email.length > 254) return false

        // Check basic email format
        if (!email.contains("@")) return false

        val parts = email.split("@")
        if (parts.size != 2) return false

        val localPart = parts[0]
        val domainPart = parts[1]

        // Check local part length (must not exceed 64 characters)
        if (localPart.length > 64) return false

        // Check domain part length (must not exceed 253 characters)
        if (domainPart.length > 253) return false

        // Validate local part
        if (!isValidLocalPart(localPart)) return false

        // Validate domain part
        if (!isValidDomainPart(domainPart)) return false

        return true
    }

    private fun isValidLocalPart(localPart: String): Boolean {
        if (localPart.isEmpty()) return false

        // Check allowed characters: letters, numbers, dots, underscores, hyphens
        val allowedChars = Regex("[a-zA-Z0-9._-]+")
        if (!localPart.matches(allowedChars)) return false

        // Cannot start or end with dot
        if (localPart.startsWith(".") || localPart.endsWith(".")) return false

        // Cannot have consecutive dots
        if (localPart.contains("..")) return false

        // Special characters must be followed by at least one letter or number
        val specialCharPattern = Regex("[._-]")
        val chars = localPart.toCharArray()
        for (i in chars.indices) {
            if (specialCharPattern.matches(chars[i].toString())) {
                // Check if it's the last character
                if (i == chars.size - 1) return false
                // Check if next character is also special
                if (i < chars.size - 1 && specialCharPattern.matches(chars[i + 1].toString())) {
                    return false
                }
            }
        }

        return true
    }

    private fun isValidDomainPart(domainPart: String): Boolean {
        if (domainPart.isEmpty()) return false

        // Must contain at least one dot
        if (!domainPart.contains(".")) return false

        // Check for consecutive dots (not allowed in domain)
        if (domainPart.contains("..")) return false

        // Check allowed characters: letters, numbers, dots, hyphens
        val allowedChars = Regex("[a-zA-Z0-9.-]+")
        if (!domainPart.matches(allowedChars)) return false

        // Split by dots and validate each part
        val domainParts = domainPart.split(".")
        if (domainParts.any { it.isEmpty() }) return false

        // Validate each domain part
        for (i in domainParts.indices) {
            val part = domainParts[i]
            
            // Each part cannot start or end with hyphen
            if (part.startsWith("-") || part.endsWith("-")) return false
            
            // Each part must contain at least one character
            if (part.isEmpty()) return false
            
            // Each domain label cannot exceed 63 characters
            if (part.length > 63) return false
        }

        // Validate TLD (last part) - must contain at least one letter
        val tld = domainParts.last()
        if (!tld.any { it.isLetter() }) return false

        return true
    }

    /**
     * Mask an email address by replacing most characters with asterisks except the first three characters.
     */
    fun maskEmail(email: String?): String? {
        if (email == null) return null
        val parts = email.split("@")
        if (parts.size != 2) return email // not a valid email
        val name = parts[0]
        val domain = parts[1]

        val visible = name.take(1).padEnd(3, '*')
        val masked = visible + "*".repeat((name.length - visible.length).coerceAtLeast(0))

        return "$masked@$domain"
    }
}