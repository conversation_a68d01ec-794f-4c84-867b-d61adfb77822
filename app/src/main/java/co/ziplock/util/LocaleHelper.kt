package co.ziplock.util

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import co.ziplock.framework.presentation.model.language.Language
import java.util.Locale

object LocaleHelper {
    
    private const val TAG = "LocaleHelper"
    
    /**
     * Tạo Context với ngôn ngữ đã được chọn trong app
     * Sử dụng cho Service để đảm bảo Service luôn sử dụng ngôn ngữ đúng
     */
    fun createLocalizedContext(context: Context): Context {
        return try {
            val locale = getLocaleFromPreference(context)
            Log.d(TAG, "createLocalizedContext: Using locale=${locale.language}")
            updateResources(context, locale)
        } catch (e: Exception) {
            Log.e(TAG, "createLocalizedContext: Error creating localized context", e)
            context
        }
    }
    
    /**
     * Lấy Locale từ SharedPreferences
     */
    private fun getLocaleFromPreference(context: Context): Locale {
        return try {
            val sharedPreferences = context.getSharedPreferences(
                context.packageName,
                Context.MODE_PRIVATE
            )
            val savedLanguage = sharedPreferences.getString("selectedLanguage", Language.getDefaultLocale()) 
                ?: Language.getDefaultLocale()
            
            Log.d(TAG, "getLocaleFromPreference: savedLanguage=$savedLanguage")
            
            if (savedLanguage.isEmpty()) {
                Log.d(TAG, "getLocaleFromPreference: savedLanguage is empty, using system default")
                Locale.getDefault()
            } else {
                Log.d(TAG, "getLocaleFromPreference: creating locale for $savedLanguage")
                Locale(savedLanguage)
            }
        } catch (e: Exception) {
            Log.e(TAG, "getLocaleFromPreference: Error getting saved language", e)
            Locale.getDefault()
        }
    }
    
    /**
     * Cập nhật resources với locale mới
     */
    private fun updateResources(context: Context, locale: Locale): Context {
        Locale.setDefault(locale)
        
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)
        
        return context.createConfigurationContext(configuration)
    }
    
    /**
     * Áp dụng ngôn ngữ cho toàn bộ app (dành cho Android 13+)
     */
    fun applyAppLanguage(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            try {
                val locale = getLocaleFromPreference(context)
                val localeList = LocaleListCompat.forLanguageTags(locale.toLanguageTag())
                AppCompatDelegate.setApplicationLocales(localeList)
                Log.d(TAG, "applyAppLanguage: Applied locale=${locale.language} for Android 13+")
            } catch (e: Exception) {
                Log.e(TAG, "applyAppLanguage: Error applying app language", e)
            }
        }
    }
}