package co.ziplock.util

import android.content.SharedPreferences
import com.google.gson.Gson
import co.ziplock.framework.network.model.BackgroundResponse
import co.ziplock.framework.network.model.RowResponse
import co.ziplock.framework.network.model.WallpaperResponse
import co.ziplock.framework.network.model.ZipperResponse
import co.ziplock.framework.presentation.model.language.Language
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PrefUtil @Inject constructor(
    private val sharedPreferences: SharedPreferences
) {
    
    private val gson = Gson()

    private inline fun <reified T> get(key: String, defaultValue: T): T {
        return when (T::class) {
            Boolean::class -> sharedPreferences.getBoolean(key, defaultValue as Boolean)
            Int::class -> sharedPreferences.getInt(key, defaultValue as Int)
            Long::class -> sharedPreferences.getLong(key, defaultValue as Long)
            Float::class -> sharedPreferences.getFloat(key, defaultValue as Float)
            String::class -> sharedPreferences.getString(key, defaultValue as? String) ?: defaultValue
            Set::class -> sharedPreferences.getStringSet(key, defaultValue as? Set<String>) ?: defaultValue
            else -> throw IllegalArgumentException("Unsupported type")
        } as T
    }

    private inline fun <reified T> put(key: String, value: T) {
        with(sharedPreferences.edit()) {
            when (value) {
                is Boolean -> putBoolean(key, value)
                is Int -> putInt(key, value)
                is Long -> putLong(key, value)
                is Float -> putFloat(key, value)
                is String -> putString(key, value)
                is Set<*> -> putStringSet(key, value as Set<String>)
                else -> throw IllegalArgumentException("Unsupported type")
            }.commit()
        }
    }

    private fun putNullableString(key: String, value: String?) {
        with(sharedPreferences.edit()) {
            if (value != null) {
                putString(key, value)
            } else {
                remove(key)
            }
            commit()
        }
    }

    var selectedLanguage: String
        get() = get("selectedLanguage", Language.getDefaultLocale())
        set(value) = put("selectedLanguage", value)

    var isPremium: Boolean
        get() = get("isPremium", false)
        set(value) = put("isPremium", value)

    var countRequestOtp: Int
        get() = get("countRequestOtp", 0)
        set(value) = put("countRequestOtp", value)

    var passwordEnabled: Boolean
        get() = get("passwordEnabled", false)
        set(value) = put("passwordEnabled", value)

    var lockScreenEnabled: Boolean
        get() = get("lockScreenEnabled", false)
        set(value) = put("lockScreenEnabled", value)

    // Security Manager related preferences
    var securityType: String
        get() = get("security_type", "NONE")
        set(value) = put("security_type", value)

    var pinCode: String?
        get() = sharedPreferences.getString("pin_code", null)
        set(value) = putNullableString("pin_code", value)

    var patternCode: String?
        get() = sharedPreferences.getString("pattern_code", null)
        set(value) = putNullableString("pattern_code", value)

    var hasSecurity: Boolean
        get() = get("has_security", false)
        set(value) = put("has_security", value)

    var failedAttempts: Int
        get() = get("failed_attempts", 0)
        set(value) = put("failed_attempts", value)

    var lockoutTime: Long
        get() = get("lockout_time", 0L)
        set(value) = put("lockout_time", value)

    var securityQuestion: String?
        get() = sharedPreferences.getString("security_question", null)
        set(value) = putNullableString("security_question", value)

    var securityQuestionKey: String?
        get() = sharedPreferences.getString("security_question_key", null)
        set(value) = putNullableString("security_question_key", value)

    var securityAnswer: String?
        get() = sharedPreferences.getString("security_answer", null)
        set(value) = putNullableString("security_answer", value)
        
    var recoveryEmail: String?
        get() = sharedPreferences.getString("recovery_email", null)
        set(value) = putNullableString("recovery_email", value)

    var testOTPMode: Boolean
        get() = get("testOTPMode", false)
        set(value) = put("testOTPMode", value)

    var vibrationEnabled: Boolean
        get() = get("vibrationEnabled", true) // Default to true
        set(value) = put("vibrationEnabled", value)

    var soundEnabled: Boolean
        get() = get("soundEnabled", true) // Default to true
        set(value) = put("soundEnabled", value)

    var dateTimeEnabled: Boolean
        get() = get("dateTimeEnabled", true) // Default to true
        set(value) = put("dateTimeEnabled", value)

    var batteryWidgetEnabled: Boolean
        get() = get("batteryWidgetEnabled", true) // Default to true
        set(value) = put("batteryWidgetEnabled", value)

    // Font and Color preferences for lock screen
    var selectedFontFamilyId: String?
        get() = sharedPreferences.getString("selected_font_family_id", "itim") // Default to itim
        set(value) = putNullableString("selected_font_family_id", value)

    var selectedFontColorId: String?
        get() = sharedPreferences.getString("selected_font_color_id", "white") // Default to white
        set(value) = putNullableString("selected_font_color_id", value)

    // Zipper customization preferences
    var selectedZipperResponse: ZipperResponse?
        get() {
            val jsonString = sharedPreferences.getString("selected_zipper_response", null)
            return if (jsonString != null) {
                try {
                    gson.fromJson(jsonString, ZipperResponse::class.java)
                } catch (e: Exception) {
                    null
                }
            } else {
                null
            }
        }
        set(value) {
            with(sharedPreferences.edit()) {
                if (value != null) {
                    try {
                        val jsonString = gson.toJson(value)
                        putString("selected_zipper_response", jsonString)
                    } catch (e: Exception) {
                        remove("selected_zipper_response")
                    }
                } else {
                    remove("selected_zipper_response")
                }
                commit()
            }
        }

    // Wallpaper customization preferences
    var selectedWallpaperResponse: WallpaperResponse?
        get() {
            val jsonString = sharedPreferences.getString("selected_wallpaper_response", null)
            return if (jsonString != null) {
                try {
                    gson.fromJson(jsonString, WallpaperResponse::class.java)
                } catch (e: Exception) {
                    null
                }
            } else {
                null
            }
        }
        set(value) {
            with(sharedPreferences.edit()) {
                if (value != null) {
                    try {
                        val jsonString = gson.toJson(value)
                        putString("selected_wallpaper_response", jsonString)
                    } catch (e: Exception) {
                        remove("selected_wallpaper_response")
                    }
                } else {
                    remove("selected_wallpaper_response")
                }
                commit()
            }
        }

    // Background customization preferences
    var selectedBackgroundResponse: BackgroundResponse?
        get() {
            val jsonString = sharedPreferences.getString("selected_background_response", null)
            return if (jsonString != null) {
                try {
                    gson.fromJson(jsonString, BackgroundResponse::class.java)
                } catch (e: Exception) {
                    null
                }
            } else {
                null
            }
        }
        set(value) {
            with(sharedPreferences.edit()) {
                if (value != null) {
                    try {
                        val jsonString = gson.toJson(value)
                        putString("selected_background_response", jsonString)
                    } catch (e: Exception) {
                        remove("selected_background_response")
                    }
                } else {
                    remove("selected_background_response")
                }
                commit()
            }
        }

    // Sound customization preferences
    var selectedSoundUrl: String?
        get() = sharedPreferences.getString("selected_sound_url", null)
        set(value) = putNullableString("selected_sound_url", value)

    var selectedSoundImagePreviewPath: String?
        get() = sharedPreferences.getString("selected_sound_image_preview_path", null)
        set(value) = putNullableString("selected_sound_image_preview_path", value)


    // Theme navigation preferences
    var selectedThemeFromSaveSuccess: Int
        get() = get("selected_theme_from_save_success", -1)
        set(value) = put("selected_theme_from_save_success", value)

    fun clearSelectedThemeFromSaveSuccess() {
        sharedPreferences.edit().remove("selected_theme_from_save_success").commit()
    }

    // RowResponse preferences
    var selectedRowResponse: RowResponse?
        get() {
            val jsonString = sharedPreferences.getString("selected_row_response", null)
            return if (jsonString != null) {
                try {
                    gson.fromJson(jsonString, RowResponse::class.java)
                } catch (e: Exception) {
                    null
                }
            } else {
                null
            }
        }
        set(value) {
            with(sharedPreferences.edit()) {
                if (value != null) {
                    try {
                        val jsonString = gson.toJson(value)
                        putString("selected_row_response", jsonString)
                    } catch (e: Exception) {
                        remove("selected_row_response")
                    }
                } else {
                    remove("selected_row_response")
                }
                commit()
            }
        }
}
