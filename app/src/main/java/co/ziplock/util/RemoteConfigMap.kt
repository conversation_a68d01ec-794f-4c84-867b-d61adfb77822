package co.ziplock.util

import android.content.Context
import co.ziplock.BuildConfig
import co.ziplock.R
import co.ziplock.repository.impl.RemoteConfigRepositoryImpl
import kotlin.io.bufferedReader
import kotlin.io.readText
import kotlin.io.use
import kotlin.to

fun readRawTextFile(
    context: Context,
    resId: Int,
): String =
    context.resources
        .openRawResource(resId)
        .bufferedReader()
        .use { it.readText() }

fun getRemoteConfigDefaults(context: Context): Map<String, Any> {
    val configShowAds = readRawTextFile(context, R.raw.config_show_ads) // đọc nội dung file raw/longtext.txt
    val adModId = readRawTextFile(context, R.raw.admob_id) // đọc nội dung file raw/longtext.txt

    return mapOf(
        "config_show_ads" to configShowAds,
        "admob_id" to adModId,
        "is_gdpr_on" to true,
        "numberOfContentBetweenTemplateList" to 5,
        "numberOfContentBetweenTemplatePreview" to 3,
        RemoteConfigRepositoryImpl.KEY_BASE_URL_OTP to BuildConfig.BASE_URL_OTP,
        "show_onboarding4_fragment" to true,
    )
}
