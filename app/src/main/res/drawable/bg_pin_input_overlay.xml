<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="#FFFFFF" />
            <solid android:color="#33FFFFFF" />
        </shape>
    </item>
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <stroke android:width="1dp" android:color="#66FFFFFF" />
            <solid android:color="#1A000000" />
        </shape>
    </item>
</selector>