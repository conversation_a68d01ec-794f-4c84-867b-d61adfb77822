<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragmentContainerMain"
        android:name="co.ziplock.framework.presentation.common.MainNavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:navGraph="@navigation/nav_main" />
    <TextView
        android:id="@+id/viewShowOpenApp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:elevation="10dp"
        android:fontFamily="@font/font_500"
        android:gravity="center"
        android:text="@string/loading_data"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:visibility="gone" />
</FrameLayout>
