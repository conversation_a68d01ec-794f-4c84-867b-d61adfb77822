<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center">

    <!-- Row 1: 1, 2, 3 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <Button
            android:id="@+id/btn_1"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="1"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

        <Button
            android:id="@+id/btn_2"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="2"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

        <Button
            android:id="@+id/btn_3"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:text="3"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

    </LinearLayout>

    <!-- Row 2: 4, 5, 6 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <Button
            android:id="@+id/btn_4"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="4"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

        <Button
            android:id="@+id/btn_5"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="5"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

        <Button
            android:id="@+id/btn_6"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:text="6"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

    </LinearLayout>

    <!-- Row 3: 7, 8, 9 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <Button
            android:id="@+id/btn_7"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="7"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

        <Button
            android:id="@+id/btn_8"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="8"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

        <Button
            android:id="@+id/btn_9"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:text="9"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

    </LinearLayout>

    <!-- Row 4: empty, 0, delete -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- Empty space -->
        <View
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_0"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="8dp"
            android:text="0"
            android:textSize="18sp"
            android:textColor="@color/black_35496d"
            android:background="@drawable/bg_keypad_white" />

        <ImageButton
            android:id="@+id/btn_delete"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:src="@drawable/ic_backspace"
            android:background="@drawable/bg_keypad_white"
            android:scaleType="centerInside" />

    </LinearLayout>

</LinearLayout>