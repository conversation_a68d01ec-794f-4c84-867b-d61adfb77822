<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/toast_background"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/_12dp">

    <ImageView
        android:layout_width="@dimen/_24dp"
        android:layout_height="@dimen/_24dp"
        android:src="@drawable/ic_check"
        app:tint="@android:color/white" />

    <TextView
        android:id="@+id/toast_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:fontFamily="@font/font_600"
        android:text="@string/your_password_has_been_set"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_18sp" />
</LinearLayout>
