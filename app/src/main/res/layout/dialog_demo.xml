<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="#4D000000">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_percent="0.8"
            android:background="@drawable/bg_radius_16"
            android:padding="@dimen/_16dp"
            android:orientation="vertical"
            android:backgroundTint="@color/white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:layout_gravity="center_horizontal"
                android:textSize="@dimen/text_size_18"
                android:text="@string/dialog_demo_title" />

            <Button
                android:id="@+id/btnClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/button_close"
                android:layout_marginTop="@dimen/_16dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>