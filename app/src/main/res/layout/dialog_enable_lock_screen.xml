<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:background="@drawable/bg_radius_16"
            android:backgroundTint="@color/white"
            android:elevation="@dimen/_8dp"
            android:padding="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <!-- Close Button -->
            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="@dimen/_4dp"
                android:src="@drawable/ic_close"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Title -->
            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:text="@string/enable_lock_screen"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_20sp"
                app:layout_constraintEnd_toStartOf="@id/ivClose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Description -->
            <TextView
                android:id="@+id/tvDescription"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/enable_lock_screen_description"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle" />

            <!-- Status Box -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clStatusBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/light_gray"
                android:padding="@dimen/_12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvDescription">

                <TextView
                    android:id="@+id/tvStatusLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_600"
                    android:text="@string/lock_screen_status"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/ivSwitchIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:alpha="0.5"
                    android:checked="true"
                    android:enabled="true"
                    android:clickable="false"
                    android:focusable="false"                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Enable Button -->
            <TextView
                android:id="@+id/tvEnable"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/blue_2fafff"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:paddingVertical="@dimen/_14dp"
                android:text="@string/enable"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/clStatusBox" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>