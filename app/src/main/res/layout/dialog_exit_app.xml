<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <FrameLayout
        android:background="#5F5E5E"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_gravity="center"
            android:background="@drawable/bg_radius_12"
            android:backgroundTint="#1E1E1E"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.cardview.widget.CardView
                android:id="@+id/layoutAds"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:cardCornerRadius="@dimen/_8dp"
                android:background="@color/ads_background"
                android:layout_margin="@dimen/_24dp"
                app:layout_constraintDimensionRatio="300:250"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <FrameLayout
                    android:id="@+id/viewGroupAds"
                    android:layout_gravity="center"
                    android:background="@color/ads_background"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:text="@string/loading_ads" />

                </FrameLayout>

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/txvDelete"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_24dp"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/do_you_want_to_exit"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp"
                app:layout_constraintBottom_toTopOf="@+id/btnExit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutAds" />

            <TextView
                android:id="@+id/btnExit"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@+id/txvDelete"
                android:layout_marginStart="@dimen/_24dp"
                android:layout_marginEnd="@dimen/_12dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:background="@drawable/bg_radius_5"
                android:backgroundTint="#D40202"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:text="@string/exit"
                android:layout_marginTop="@dimen/_16dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="264:80"
                app:layout_constraintEnd_toStartOf="@+id/btnCancel"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@+id/txvDelete"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_24dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:background="@drawable/bg_radius_4_stroke_blue"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="#3E9DF4"
                android:textSize="@dimen/_16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="264:80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btnExit" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </FrameLayout>
</layout>
