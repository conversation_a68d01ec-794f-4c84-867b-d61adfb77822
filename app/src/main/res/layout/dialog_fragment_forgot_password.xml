<?xml version="1.0" encoding="utf-8"?>
<layout>
    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_dialog_rounded"
            android:backgroundTint="@color/grey_f5f5f5"
            android:padding="@dimen/_24dp"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:layout_gravity="center">

            <!-- Title -->
            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:text="@string/label_forgot_password"
                android:textColor="@color/black_3f4955"
                android:textSize="@dimen/_20sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Security Question Option -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutSecurityQuestionOption"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                android:background="@drawable/bg_setting_item"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:padding="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle">

                <TextView
                    android:id="@+id/tvSecurityQuestionTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/label_security_question"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/radioSecurityQuestion"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <RadioButton
                    android:id="@+id/radioSecurityQuestion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:buttonTint="@color/radio_button_tint"
                    android:clickable="false"
                    android:focusable="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Email Recovery Option -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutEmailOption"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:background="@drawable/bg_setting_item"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:padding="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutSecurityQuestionOption">

                <TextView
                    android:id="@+id/tvEmailTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/recovery_email"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/radioEmailRecovery"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <RadioButton
                    android:id="@+id/radioEmailRecovery"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:buttonTint="@color/radio_button_tint"
                    android:clickable="false"
                    android:focusable="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:id="@+id/layoutButtons"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_32dp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutEmailOption">

                <TextView
                    android:id="@+id/btnCancel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_weight="1"
                    android:textColor="@color/grey_5b5b5b"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/grey_dfdfdf"
                    android:textAlignment="center"
                    android:padding="@dimen/_12dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/cancel"
                    android:textSize="@dimen/_16sp" />

                <TextView
                    android:id="@+id/btnContinue"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/font_600"
                    android:textColor="@color/white"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_2fafff"
                    android:textAlignment="center"
                    android:padding="@dimen/_12dp"
                    android:text="@string/next"
                    android:textSize="@dimen/_16sp" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>