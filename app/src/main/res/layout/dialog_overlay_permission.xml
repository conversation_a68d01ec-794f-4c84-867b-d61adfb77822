<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_radius_8"
    android:backgroundTint="@color/white"
    android:padding="@dimen/_16dp">

    <TextView
        android:id="@+id/tvPermissionTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/permission_title"
        android:textAlignment="center"
        android:textSize="@dimen/_18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvPermissionMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/permission_message"
        android:textAlignment="center"
        app:layout_constraintTop_toBottomOf="@id/tvPermissionTitle" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/tvPermissionMessage">

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_cancel"
            android:text="Cancel"
            android:textAllCaps="false"
            android:textColor="@color/gray" />

        <Button
            android:id="@+id/btnGoToSettings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_primary"
            android:text="Go to Settings"
            android:textAllCaps="false"
            android:textColor="@android:color/white" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>