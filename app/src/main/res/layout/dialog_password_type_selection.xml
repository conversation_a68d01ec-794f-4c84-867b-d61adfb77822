<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:background="@drawable/bg_dialog_rounded"
            android:backgroundTint="@color/grey_f5f5f5"
            android:padding="@dimen/_24dp">

            <!-- Title -->
            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:text="@string/password_type"
                android:textColor="@color/black_3f4955"
                android:textSize="@dimen/_20sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- PIN Option -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutPinOption"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_32dp"
                android:background="@drawable/bg_setting_item"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:padding="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle">

                <ImageView
                    android:id="@+id/ivPinIcon"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginStart="@dimen/_2dp"
                    android:src="@drawable/ic_pin_code_password_type_dialog"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/blue_2fafff" />

                <TextView
                    android:id="@+id/tvPinTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/pin_code"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp"
                    app:layout_constraintBottom_toBottomOf="@id/ivPinIcon"
                    app:layout_constraintEnd_toStartOf="@id/radioPinOption"
                    app:layout_constraintStart_toEndOf="@id/ivPinIcon"
                    app:layout_constraintTop_toTopOf="@id/ivPinIcon" />

                <RadioButton
                    android:id="@+id/radioPinOption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:buttonTint="@color/radio_button_tint"
                    android:checked="true"
                    android:clickable="false"
                    android:focusable="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Pattern Option -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutPatternOption"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:background="@drawable/bg_setting_item"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:padding="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutPinOption">

                <ImageView
                    android:id="@+id/ivPatternIcon"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginStart="@dimen/_2dp"
                    android:src="@drawable/ic_pattern_password_type_dialog"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/blue_2fafff" />

                <TextView
                    android:id="@+id/tvPatternTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/pattern"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp"
                    app:layout_constraintBottom_toBottomOf="@id/ivPatternIcon"
                    app:layout_constraintEnd_toStartOf="@id/radioPatternOption"
                    app:layout_constraintStart_toEndOf="@id/ivPatternIcon"
                    app:layout_constraintTop_toTopOf="@id/ivPatternIcon" />

                <RadioButton
                    android:id="@+id/radioPatternOption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:buttonTint="@color/radio_button_tint"
                    android:checked="false"
                    android:clickable="false"
                    android:focusable="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.tistory.zladnrms.roundablelayout.RoundableLayout
                android:id="@+id/layoutAds"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/_16dp"
                app:cornerAll="@dimen/_8dp"
                app:layout_constraintDimensionRatio="360:80"
                app:layout_constraintTop_toBottomOf="@id/layoutPatternOption">

                <FrameLayout
                    android:id="@+id/adViewGroup"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:background="#D7D6D6">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/loading_ads"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16sp" />

                </FrameLayout>
            </com.tistory.zladnrms.roundablelayout.RoundableLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:id="@+id/layoutButtons"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_32dp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutAds">

                <TextView
                    android:id="@+id/btnCancel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/grey_dfdfdf"
                    android:fontFamily="@font/font_600"
                    android:padding="@dimen/_12dp"
                    android:text="@string/cancel"
                    android:textAlignment="center"
                    android:textColor="@color/grey_5b5b5b"
                    android:textSize="@dimen/_16sp" />

                <TextView
                    android:id="@+id/btnApply"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_2fafff"
                    android:fontFamily="@font/font_600"
                    android:padding="@dimen/_12dp"
                    android:text="@string/apply"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sp" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>