<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@color/black">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/_16dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white"
            app:layout_constraintWidth_percent="0.9"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_700"
                android:text="@string/set_your_current_password"
                android:textAlignment="center"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_18sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvSubtitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_600"
                android:text="@string/connect_at_least_4_dots"
                android:textAlignment="center"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle" />

            <!-- Pattern Lock View -->
            <com.andrognito.patternlockview.PatternLockView
                android:id="@+id/patternLockView"
                android:layout_width="@dimen/_280dp"
                android:layout_height="@dimen/_280dp"
                android:layout_marginTop="@dimen/_24dp"
                app:aspectRatio="square"
                app:aspectRatioEnabled="true"
                app:dotCount="3"
                app:dotNormalSize="@dimen/_16dp"
                app:dotSelectedSize="@dimen/_24dp"
                app:pathWidth="@dimen/_8dp"
                app:normalStateColor="@color/black_35496d"
                app:correctStateColor="@color/blue_2fafff"
                app:wrongStateColor="@color/red_error"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvSubtitle" />

            <!-- Forgot Password Button -->
            <TextView
                android:id="@+id/btnForgotPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/font_500"
                android:padding="@dimen/_8dp"
                android:text="@string/forgot_password"
                android:textColor="@color/blue_2fafff"
                android:textSize="@dimen/_14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/patternLockView" />

            <TextView
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_8dp"
                android:gravity="center"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/grey_dfdfdf"
                android:fontFamily="@font/font_600"
                android:padding="@dimen/_12dp"
                android:text="@string/cancel"
                android:textAlignment="center"
                android:textColor="@color/grey_5b5b5b"
                android:textSize="@dimen/_16sp"
                android:layout_marginTop="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@id/btnForgotPassword"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>