<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@color/black">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white"
            android:padding="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.9">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_700"
                android:text="@string/set_your_current_password"
                android:textAlignment="center"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_18sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <!-- PIN Input Layout -->
            <LinearLayout
                android:id="@+id/layoutPinInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle">

                <EditText
                    android:id="@+id/etPin1"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_48dp"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:inputType="numberPassword"
                    android:maxLength="1" />

                <EditText
                    android:id="@+id/etPin2"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_48dp"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:inputType="numberPassword"
                    android:maxLength="1" />

                <EditText
                    android:id="@+id/etPin3"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_48dp"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:inputType="numberPassword"
                    android:maxLength="1" />

                <EditText
                    android:id="@+id/etPin4"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_48dp"
                    android:layout_height="@dimen/_48dp"
                    android:inputType="numberPassword"
                    android:maxLength="1" />

            </LinearLayout>

            <!-- Toggle PIN Visibility Button -->
            <ImageButton
                android:id="@+id/btnTogglePinVisibility"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/toggle_pin_visibility"
                android:src="@drawable/ic_visibility_off"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutPinInput" />

            <!-- Forgot Password Button -->
            <TextView
                android:id="@+id/btnForgotPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/font_500"
                android:padding="@dimen/_8dp"
                android:text="@string/forgot_password"
                android:textColor="@color/blue_2fafff"
                android:textSize="@dimen/_14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btnTogglePinVisibility" />

            <FrameLayout
                android:id="@+id/layoutAds"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@id/btnForgotPassword"
                app:layout_constraintDimensionRatio="360:70">

                <FrameLayout
                    android:id="@+id/adViewGroup"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:background="#D7D6D6">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/loading_ads"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16sp" />

                </FrameLayout>
            </FrameLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:id="@+id/layoutButtons"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutAds">

                <TextView
                    android:id="@+id/btnCancel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/grey_dfdfdf"
                    android:fontFamily="@font/font_600"
                    android:padding="@dimen/_12dp"
                    android:text="@string/cancel"
                    android:textAlignment="center"
                    android:textColor="@color/grey_5b5b5b"
                    android:textSize="@dimen/_16sp" />

                <TextView
                    android:id="@+id/btnApply"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_2fafff"
                    android:fontFamily="@font/font_600"
                    android:padding="@dimen/_12dp"
                    android:text="@string/apply"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sp" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>