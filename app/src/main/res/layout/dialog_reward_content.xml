<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="#383838">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/_24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_radius_16"
                android:backgroundTint="@color/white"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/view_middle_diamond">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_24dp"
                    android:layout_marginTop="@dimen/_57dp"
                    android:fontFamily="@font/font_500"
                    android:gravity="center"
                    android:text="@string/you_need_to_unlock_to_use_this_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_24dp"
                    android:layout_marginBottom="@dimen/_16dp"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/only_in_this_session"
                    android:textColor="#4B4B4B"
                    android:textSize="@dimen/_14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_radius_bottom_16"
                    android:backgroundTint="#0A2951"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/_20dp">

                    <LinearLayout
                        android:id="@+id/btn_watch_video_ad"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_24dp"
                        android:layout_marginTop="@dimen/_13dp"
                        android:background="@drawable/bg_radius_10"
                        android:backgroundTint="#2FAFFF"
                        android:gravity="center"
                        android:paddingVertical="@dimen/_6dp">

                        <ImageView
                            android:layout_width="@dimen/_20dp"
                            android:layout_height="@dimen/_16dp"
                            android:src="@drawable/ic_watch_video_ad" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_14dp"
                            android:fontFamily="@font/font_500"
                            android:text="@string/watch_video_ad"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_16sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/_8dp"
                        android:fontFamily="@font/font_400"
                        android:text="@string/or"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_12sp" />

                    <LinearLayout
                        android:id="@+id/btn_buy_vip_version"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_24dp"
                        android:background="@drawable/bg_buy_vip_version_btn"
                        android:gravity="center"
                        android:paddingVertical="@dimen/_6dp">

                        <ImageView
                            android:layout_width="@dimen/_24dp"
                            android:layout_height="@dimen/_24dp"
                            android:src="@drawable/ic_buy_vip_version" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_14dp"
                            android:fontFamily="@font/font_500"
                            android:text="@string/buy_vip_version"
                            android:textColor="#FFA81E"
                            android:textSize="@dimen/_16sp" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <View
                android:id="@+id/view_bg_diamond"
                android:layout_width="@dimen/_80dp"
                android:layout_height="@dimen/_80dp"
                android:background="@drawable/bg_radius_300"
                android:backgroundTint="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_diamond"
                android:layout_width="@dimen/_64dp"
                android:layout_height="@dimen/_64dp"
                android:src="@drawable/ic_diamond_blue"
                app:layout_constraintBottom_toBottomOf="@id/view_bg_diamond"
                app:layout_constraintEnd_toEndOf="@id/view_bg_diamond"
                app:layout_constraintStart_toStartOf="@id/view_bg_diamond"
                app:layout_constraintTop_toTopOf="@id/view_bg_diamond" />

            <View
                android:id="@+id/view_middle_diamond"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1dp"
                app:layout_constraintBottom_toBottomOf="@id/view_bg_diamond"
                app:layout_constraintTop_toTopOf="@id/view_bg_diamond" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:layout_marginTop="@dimen/_14dp"
            android:padding="@dimen/_4dp"
            android:src="@drawable/ic_close_dialog_reward"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_content" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
