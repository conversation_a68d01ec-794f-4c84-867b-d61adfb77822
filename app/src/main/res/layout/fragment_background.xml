<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_background_home">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vFakeBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/background"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/vFakeBack"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <!-- Tab Layout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:background="@null"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        app:tabIndicatorColor="@color/blue_4285f4"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/blue_4285f4"
        app:tabTextColor="@color/gray_666666" />

    <!-- Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/tabLayout">

        <!-- RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvBackgrounds"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="@dimen/_16dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="2"
            tools:itemCount="10"
            tools:listitem="@layout/item_wallpaper" />

        <!-- Loading Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyState"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="@dimen/_80dp"
                android:layout_height="@dimen/_80dp"
                android:alpha="0.3"
                android:src="@drawable/ic_wallpaper" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:text="@string/no_backgrounds_available"
                android:textColor="@color/gray_666666"
                android:textSize="@dimen/_16sp" />

        </LinearLayout>

    </FrameLayout>

    <!-- FloatingActionButton -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabFromGallery"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:backgroundTint="@android:color/white"
        android:elevation="@dimen/_4dp"
        android:fontFamily="@font/font_700"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_8dp"
        android:text="@string/from_gallery"
        android:textAllCaps="false"
        android:textColor="@color/blue"
        app:icon="@drawable/ic_gallery_to_pick_photo"
        app:iconGravity="textStart"
        app:iconPadding="@dimen/_8dp"
        app:iconTint="@color/blue"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintEnd_toEndOf="parent"
        app:strokeColor="@color/blue"
        app:strokeWidth="@dimen/_1dp" />

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>