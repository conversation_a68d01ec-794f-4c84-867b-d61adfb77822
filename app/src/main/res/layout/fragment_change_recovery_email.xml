<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f5f5f5">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vFakeBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/change_recovery_email"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/vFakeBack"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_24dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white"
            android:padding="@dimen/_16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            app:layout_constraintVertical_bias="0">

            <!-- Current Email Card -->
            <LinearLayout
                android:id="@+id/cardCurrentEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_700"
                        android:text="@string/current_recovery_email"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp" />

                    <EditText
                        android:id="@+id/tvCurrentEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:background="@drawable/bg_edit_text_change_recovery_email"
                        android:fontFamily="@font/font_400"
                        android:padding="@dimen/_12dp"
                        android:textColor="@color/black_35496d"
                        android:textColorHint="@color/gray_9e9e9e"
                        android:textSize="@dimen/_14sp"
                        tools:text="<EMAIL>" />
                </LinearLayout>
            </LinearLayout>

            <!-- Description -->
            <TextView
                android:id="@+id/tvDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/font_500"
                android:text="@string/change_recovery_email_description"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp"
                app:layout_constraintTop_toBottomOf="@id/cardCurrentEmail" />

            <!-- New Recovery Email Card -->
            <LinearLayout
                android:id="@+id/cardNewRecoveryEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/tvDescription">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_700"
                        android:text="@string/new_recovery_email"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp" />

                    <EditText
                        android:id="@+id/etNewEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:background="@drawable/bg_edit_text_change_recovery_email"
                        android:fontFamily="@font/font_400"
                        android:hint="@string/enter_your_email"
                        android:padding="@dimen/_12dp"
                        android:textColor="@color/black_35496d"
                        android:textColorHint="@color/gray_9e9e9e"
                        android:textSize="@dimen/_14sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Confirm New Recovery Email Card -->
            <LinearLayout
                android:id="@+id/cardConfirmNewRecoveryEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/cardNewRecoveryEmail">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_700"
                        android:text="@string/confirm_new_recovery_email"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp" />

                    <EditText
                        android:id="@+id/etConfirmEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:background="@drawable/bg_edit_text_change_recovery_email"
                        android:fontFamily="@font/font_400"
                        android:hint="@string/enter_your_email"
                        android:padding="@dimen/_12dp"
                        android:textColor="@color/black_35496d"
                        android:textColorHint="@color/gray_9e9e9e"
                        android:textSize="@dimen/_14sp" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tvError"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:fontFamily="@font/font_600"
                android:textColor="@color/red_error"
                android:textSize="@dimen/_16sp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/cardConfirmNewRecoveryEmail"
                tools:text="Minimum 3 characters required"
                tools:visibility="visible" />

            <!-- Buttons -->
            <TextView
                android:id="@+id/btnUpdate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/blue_2fafff"
                android:fontFamily="@font/font_700"
                android:paddingVertical="@dimen/_12dp"
                android:text="@string/update"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp"
                app:layout_constraintTop_toBottomOf="@id/tvError" />

            <TextView
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:fontFamily="@font/font_700"
                android:paddingVertical="@dimen/_12dp"
                android:text="@string/cancel"

                android:textAllCaps="false"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btnUpdate" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>


    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>


    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>