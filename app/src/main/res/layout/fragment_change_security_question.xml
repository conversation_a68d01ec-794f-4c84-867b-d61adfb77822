<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f5f5f5">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vFakeBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/security_questions"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/vFakeBack"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <!-- Main Content Container -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_24dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clMainContent"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:paddingBottom="@dimen/_10dp"
            android:layout_marginTop="@dimen/_24dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white">

            <!-- General Error Message -->
            <TextView
                android:id="@+id/tvError"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_24dp"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:textColor="@color/red_error"
                android:textSize="@dimen/_14sp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Please select a new security question"
                tools:visibility="visible" />

            <!-- Current Security Question Section -->
            <LinearLayout
                android:id="@+id/llCurrentQuestionContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_24dp"
                android:layout_marginTop="@dimen/_16dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvError">

                <!-- Current Question Label -->
                <TextView
                    android:id="@+id/tvCurrentQuestionLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_600"
                    android:text="@string/current_security_question"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <!-- Current Question Spinner -->
                <Spinner
                    android:id="@+id/spinnerCurrentSecurityQuestion"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@drawable/bg_spinner_with_arrow"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:paddingEnd="@dimen/_40dp"
                    android:spinnerMode="dropdown" />

                <!-- Current Answer Label -->
                <TextView
                    android:id="@+id/tvCurrentAnswerLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/label_your_answer"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <!-- Current Answer Input -->
                <EditText
                    android:id="@+id/etCurrentAnswer"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:alpha="0.7"
                    android:background="@drawable/bg_pin_input"
                    android:hint="@string/enter_your_answer"
                    android:inputType="text"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:textColor="@color/black_35496d"
                    android:textColorHint="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <!-- Current Answer Error -->
                <TextView
                    android:id="@+id/tvCurrentAnswerError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:fontFamily="@font/font_600"
                    android:textColor="@color/red_error"
                    android:textSize="@dimen/_16sp"
                    android:visibility="gone"
                    tools:text="Incorrect answer. Try again"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- New Security Question Section -->
            <LinearLayout
                android:id="@+id/llNewQuestionContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_24dp"
                android:layout_marginTop="@dimen/_24dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/llCurrentQuestionContainer">

                <!-- New Question Label -->
                <TextView
                    android:id="@+id/tvNewQuestionLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_600"
                    android:text="@string/new_security_question"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <!-- New Question Spinner -->
                <Spinner
                    android:id="@+id/spinnerNewSecurityQuestion"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@drawable/bg_spinner_with_arrow"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:paddingEnd="@dimen/_40dp"
                    android:spinnerMode="dropdown" />

                <!-- New Answer Label -->
                <TextView
                    android:id="@+id/tvNewAnswerLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/label_your_answer"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <!-- New Answer Input -->
                <EditText
                    android:id="@+id/etNewAnswer"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_48dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:alpha="0.7"
                    android:background="@drawable/bg_pin_input"
                    android:hint="@string/enter_your_answer"
                    android:inputType="text"
                    android:paddingHorizontal="@dimen/_12dp"
                    android:textColor="@color/black_35496d"
                    android:textColorHint="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <!-- New Answer Error -->
                <TextView
                    android:id="@+id/tvNewAnswerError"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:fontFamily="@font/font_600"
                    android:textColor="@color/red_error"
                    android:textSize="@dimen/_16sp"
                    android:visibility="gone"
                    tools:text="Minimum 3 characters required"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- Save Button -->
            <TextView
                android:id="@+id/btnSave"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_24dp"
                android:layout_marginTop="@dimen/_32dp"
                android:layout_marginBottom="@dimen/_24dp"
                android:alpha="0.5"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/blue_2fafff"
                android:enabled="false"
                android:fontFamily="@font/font_700"
                android:padding="@dimen/_12dp"
                android:text="@string/save"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/llNewQuestionContainer"
                app:layout_constraintVertical_bias="0.0" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>


    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>