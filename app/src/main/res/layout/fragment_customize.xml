<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_background_home">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vFakeBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:layout_marginTop="@dimen/_10dp"
        android:src="@drawable/ic_arrow_right"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/customize"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/vFakeBack"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <!-- Content ScrollView -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_20dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/_16dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <FrameLayout
                    android:id="@+id/layoutAdsTop"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="360:80">

                    <FrameLayout
                        android:id="@+id/adViewGroupTop"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="bottom"
                        android:background="#D7D6D6">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="@font/font_400"
                            android:gravity="center"
                            android:text="@string/loading_ads"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16sp" />

                    </FrameLayout>
                </FrameLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Zipper Section -->
            <LinearLayout
                android:id="@+id/zipperSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_marginBottom="@dimen/_24dp">

                <!-- Zipper Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/_12dp">


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/zipper"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp"
                        android:fontFamily="@font/font_700" />

                    <ImageView
                        android:id="@+id/btnZipperMore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        android:background="@drawable/bg_radius_300"
                        android:backgroundTint="@color/white"
                        app:tint="@color/blue_2fafff"
                        android:padding="@dimen/_8dp" />
                </LinearLayout>

                <!-- Zipper Preview RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvZipperPreview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_zipper_preview" />
            </LinearLayout>

            <!-- Row Section -->
            <LinearLayout
                android:id="@+id/rowSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/_24dp">

                <!-- Row Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/_12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/row"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp"
                        android:fontFamily="@font/font_700" />

                    <ImageView
                        android:id="@+id/btnRowMore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        android:background="@drawable/bg_radius_300"
                        android:backgroundTint="@color/white"
                        app:tint="@color/blue_2fafff"
                        android:padding="@dimen/_8dp" />
                </LinearLayout>

                <!-- Row Preview RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvRowPreview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_zipper_preview" />
            </LinearLayout>

            <!-- Wallpaper Section -->
            <LinearLayout
                android:id="@+id/wallpaperSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/_24dp">

                <!-- Wallpaper Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/_12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/wallpaper"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp"
                        android:fontFamily="@font/font_700" />

                    <ImageView
                        android:id="@+id/btnWallpaperMore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        android:background="@drawable/bg_radius_300"
                        android:backgroundTint="@color/white"
                        app:tint="@color/blue_2fafff"
                        android:padding="@dimen/_8dp" />
                </LinearLayout>



                <!-- Wallpaper Preview RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvWallpaperPreview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_wallpaper_preview" />
            </LinearLayout>

            <!-- Background Section -->
            <LinearLayout
                android:id="@+id/backgroundSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/_24dp">

                <!-- Background Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/_12dp">



                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/background"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp"
                        android:fontFamily="@font/font_700" />

                    <ImageView
                        android:id="@+id/btnBackgroundMore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        android:background="@drawable/bg_radius_300"
                        android:backgroundTint="@color/white"
                        app:tint="@color/blue_2fafff"
                        android:padding="@dimen/_8dp" />
                </LinearLayout>



                <!-- Background Preview RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvBackgroundPreview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_wallpaper_preview" />
            </LinearLayout>

            <!-- Sound Section -->
            <LinearLayout
                android:id="@+id/soundSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/_24dp">

                <!-- Sound Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/_12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/sound"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp"
                        android:fontFamily="@font/font_700" />

                    <ImageView
                        android:id="@+id/btnSoundMore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        android:background="@drawable/bg_radius_300"
                        android:backgroundTint="@color/white"
                        app:tint="@color/blue_2fafff"
                        android:padding="@dimen/_8dp" />
                </LinearLayout>

                <!-- Sound Preview RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvSoundPreview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_sound" />
            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>