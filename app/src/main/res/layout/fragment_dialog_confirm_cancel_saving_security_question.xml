<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@color/black">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:padding="@dimen/_24dp"
            android:layout_marginHorizontal="@dimen/_30dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_16dp"
                android:text="@string/do_you_want_to_exit"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_18sp"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="@dimen/_70dp"
                android:layout_height="@dimen/_70dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_16dp"
                android:src="@drawable/img_cancel_security_question_dialog"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_24dp"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/_4dp"
                android:text="@string/desc_confirm_cancel_saving_security_question"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/btn_continue_setup"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_2fafff"
                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_10dp"
                    android:text="@string/continue_setup"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sp" />

                <TextView
                    android:id="@+id/btn_exit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/grey_ebebeb"
                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_10dp"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="@string/exit"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</layout>