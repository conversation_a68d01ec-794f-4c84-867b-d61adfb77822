<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:padding="@dimen/_24dp"
            android:layout_marginHorizontal="@dimen/_30dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_16dp"
                android:text="@string/disable_ziplock"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_24dp"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/_4dp"
                android:text="@string/permission_confirmation_message"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_disable"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/grey_ebebeb"
                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_10dp"
                    android:text="@string/disable"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <TextView
                    android:id="@+id/btn_cancel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/grey_ebebeb"
                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_10dp"
                    android:text="@string/cancel"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</layout>