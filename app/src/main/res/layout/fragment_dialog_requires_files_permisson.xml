<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:padding="@dimen/_24dp"
            android:layout_marginHorizontal="@dimen/_30dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_16dp"
                android:text="@string/permission_required"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_24dp"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/_4dp"
                android:text="@string/allow_ziplock_to_access_photos_media_and_files_on_your_device"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_deny"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:layout_weight="1"

                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_10dp"
                    android:text="@string/deny"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />

                <TextView
                    android:id="@+id/btn_allow"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_2fafff"
                    android:fontFamily="@font/font_600"
                    android:paddingVertical="@dimen/_10dp"
                    android:text="@string/allow"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sp" />

            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</layout>