<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/_30dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/white"
            android:orientation="vertical"
            android:padding="@dimen/_24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_16dp"
                android:fontFamily="@font/font_700"
                android:text="@string/permission_required"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/_24dp"
                android:fontFamily="@font/font_500"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/_4dp"
                android:text="@string/permission_required_message"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

            <TextView
                android:id="@+id/btn_do_it"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/blue_2fafff"
                android:fontFamily="@font/font_700"
                android:paddingVertical="@dimen/_10dp"
                android:text="@string/do_it"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp" />

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:background="@drawable/bg_radius_8"
                android:fontFamily="@font/font_600"
                android:paddingVertical="@dimen/_10dp"
                android:text="@string/cancel"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp" />
        </LinearLayout>
    </FrameLayout>
</layout>