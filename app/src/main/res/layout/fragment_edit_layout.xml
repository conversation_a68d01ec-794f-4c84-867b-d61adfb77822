<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_background_home">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Next Button -->
    <TextView
        android:id="@+id/tvNext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_radius_8"
        android:backgroundTint="@color/blue_2fafff"
        android:paddingHorizontal="@dimen/_10dp"
        android:paddingVertical="@dimen/_6dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:text="@string/next"
        android:textSize="@dimen/_16sp"
        android:fontFamily="@font/font_700"
        android:textColor="@color/white"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivBack"
        app:layout_constraintBottom_toBottomOf="@id/ivBack"/>

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/edit_layout"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/ivBack"
        app:layout_constraintEnd_toStartOf="@id/tvNext"
        app:layout_constraintStart_toEndOf="@id/ivBack"
        app:layout_constraintTop_toTopOf="@id/ivBack" />

    <!-- Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_20dp"
        android:padding="@dimen/_16dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Preview Section with Theme Navigation -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/previewSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent">

                <!-- Previous Theme Button -->
                <ImageView
                    android:id="@+id/ivPreviousTheme"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_2fafff"
                    android:foreground="?attr/selectableItemBackgroundBorderless"
                    android:padding="@dimen/_8dp"
                    app:tint="@color/white"
                    android:src="@drawable/ic_arrow_right"
                    android:rotation="180"
                    android:layout_marginStart="@dimen/_16dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:contentDescription="@string/previous_theme"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/previewContainer"
                    app:layout_constraintBottom_toBottomOf="@id/previewContainer" />

                <!-- Preview Container -->
                <FrameLayout
                    android:id="@+id/previewContainer"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginHorizontal="@dimen/_16dp"
                    app:layout_constraintDimensionRatio="H,168:336"
                    app:layout_constraintEnd_toStartOf="@id/ivNextTheme"
                    app:layout_constraintStart_toEndOf="@id/ivPreviousTheme"
                    app:layout_constraintTop_toTopOf="parent">

                    <!-- Wallpaper preview background -->
                    <ImageView
                        android:id="@+id/ivWallpaperPreview"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:visibility="gone" />

                    <co.ziplock.customview.ZipperView
                        android:id="@+id/zipperPreview"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <ProgressBar
                        android:id="@+id/progressLoading"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"/>

                </FrameLayout>

                <!-- Next Theme Button -->
                <ImageView
                    android:id="@+id/ivNextTheme"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/blue_2fafff"
                    android:foreground="?attr/selectableItemBackgroundBorderless"
                    android:padding="@dimen/_8dp"
                    app:tint="@color/white"
                    android:src="@drawable/ic_arrow_right"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:contentDescription="@string/next_theme"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/previewContainer"
                    app:layout_constraintBottom_toBottomOf="@id/previewContainer" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/ivPreview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_preview_edit_layout"
                app:layout_constraintBottom_toBottomOf="@id/previewSection"
                app:layout_constraintEnd_toEndOf="parent"/>

            <FrameLayout
                android:id="@+id/layoutAdsTop"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintTop_toBottomOf="@id/previewSection"
                app:layout_constraintDimensionRatio="360:70">

                <FrameLayout
                    android:id="@+id/adViewGroupTop"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:background="#D7D6D6">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/loading_ads"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16sp" />

                </FrameLayout>
            </FrameLayout>

            <!-- Grid for customization options -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCustomizationOptions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:listitem="@layout/item_customization_option"
                tools:itemCount="6"
                android:layout_marginTop="@dimen/_18dp"
                app:layout_constraintTop_toBottomOf="@id/layoutAdsTop"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>