<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f5f5f5"
    android:padding="@dimen/_24dp">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/recovery_email"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/vFakeBack"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <ImageView
        android:id="@+id/vFakeBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Main Content Container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMainContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_24dp"
        android:background="@drawable/bg_radius_8"
        android:backgroundTint="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">


        <!-- Email Input Container -->
        <LinearLayout
            android:id="@+id/llEmailContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginTop="@dimen/_16dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Email Label -->
            <TextView
                android:id="@+id/tvEmailLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_600"
                android:text="@string/your_email"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

            <!-- Email Input -->
            <EditText
                android:id="@+id/etEmail"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_48dp"
                android:layout_marginTop="@dimen/_8dp"
                android:alpha="0.7"
                android:background="@drawable/bg_pin_input"
                android:hint="@string/enter_your_email"
                android:inputType="textEmailAddress"
                android:paddingHorizontal="@dimen/_12dp"
                android:textColor="@color/black_35496d"
                android:textColorHint="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

        </LinearLayout>

        <!-- Error Message -->
        <TextView
            android:id="@+id/tvError"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginTop="@dimen/_16dp"
            android:fontFamily="@font/font_600"
            android:gravity="start"
            android:text="@string/invalid_email_address"
            android:textColor="@color/red_error"
            android:textSize="@dimen/_14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llEmailContainer"
            tools:visibility="visible" />

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvError" />

        <!-- Continue Button -->
        <TextView
            android:id="@+id/btnContinue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginTop="@dimen/_24dp"
            android:layout_marginBottom="@dimen/_32dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/blue_2fafff"
            android:enabled="false"
            android:fontFamily="@font/font_700"
            android:padding="@dimen/_12dp"
            android:text="@string/next"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvError"
            app:layout_constraintVertical_bias="0.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Answer Security Question Link -->
    <TextView
        android:id="@+id/tvAnswerSecurityQuestion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:fontFamily="@font/font_600"
        android:drawablePadding="@dimen/_4dp"
        android:padding="@dimen/_10dp"
        android:drawableStart="@drawable/ic_security_question_recover_emal_screen"
        android:text="@string/answer_security_question"
        android:textColor="@color/blue_2fafff"
        android:textSize="@dimen/_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clMainContent" />

</androidx.constraintlayout.widget.ConstraintLayout>