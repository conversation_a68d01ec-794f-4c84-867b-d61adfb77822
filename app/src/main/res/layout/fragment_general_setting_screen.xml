<?xml version="1.0" encoding="utf-8"?>
<layout>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        xmlns:tools="http://schemas.android.com/tools">

        <LinearLayout
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintDimensionRatio="360:72"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/_16dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_arrow_left" />

            <TextView
                android:id="@+id/tvVideoName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:fontFamily="@font/font_400"
                android:maxLines="1"
                android:text="@string/setting"
                android:textColor="@color/black"
                android:textSize="@dimen/_20sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_26dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:fontFamily="@font/font_700"
                android:text="@string/information_about"
                android:textColor="#7A7878"
                android:textSize="@dimen/text_size_14" />

            <LinearLayout
                android:id="@+id/btnLanguage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp">

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginVertical="@dimen/_10dp"
                    android:src="@drawable/ic_language"/>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:fontFamily="@font/font_400"
                    android:text="@string/language"
                    android:textSize="@dimen/text_size_16" />

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_setting_next" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnDeveloper"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp">

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginVertical="@dimen/_10dp"
                    android:src="@drawable/ic_setting_developer"/>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:fontFamily="@font/font_400"
                    android:text="@string/developer"
                    android:textSize="@dimen/text_size_16" />

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_setting_next" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnPolicy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:layout_marginTop="@dimen/_10dp">

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginVertical="@dimen/_10dp"
                    android:src="@drawable/ic_setting_policy"/>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:fontFamily="@font/font_400"
                    android:text="@string/privacy_policy"
                    android:textSize="@dimen/text_size_16" />

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_setting_next" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnResetIap"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp">

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginVertical="@dimen/_10dp"
                    android:src="@drawable/ic_setting_developer"/>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:fontFamily="@font/font_400"
                    android:text="Reset iap"
                    android:textSize="@dimen/text_size_16" />

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_setting_next" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btn_inspector"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp">

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_marginVertical="@dimen/_10dp"
                    android:src="@drawable/ic_setting_developer"/>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:fontFamily="@font/font_400"
                    android:text="Open ad inspector"
                    android:textSize="@dimen/text_size_16" />

                <ImageView
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_setting_next" />

            </LinearLayout>


            <TextView
                android:id="@+id/txvVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/_37dp"
                android:fontFamily="@font/font_500"
                android:textSize="@dimen/text_size_12"
                android:textColor="#C7C7C7"
                tools:text="Application version: v 1.0.1" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_19dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/pion_year" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
