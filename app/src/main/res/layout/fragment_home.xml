<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_background_home">

    <!-- Header Section -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:fontFamily="@font/font_700"
            android:text="@string/app_title"
            android:textColor="@color/blue"
            android:textSize="@dimen/_20sp"
            app:layout_constraintBottom_toBottomOf="@id/llTopIcons"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/llTopIcons"
            app:layout_constraintTop_toTopOf="@id/llTopIcons" />

        <!-- Tab Layout -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@android:color/transparent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llTopIcons"
            app:tabIndicatorColor="@color/blue"
            app:tabSelectedTextColor="@color/blue"
            app:tabTextColor="#35496D">

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/settings" />

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_home" />

        </com.google.android.material.tabs.TabLayout>

        <LinearLayout
            android:id="@+id/llTopIcons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iconIap"
                android:layout_marginEnd="@dimen/_6dp"
                android:padding="@dimen/_10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_iap_home"/>

            <ImageView
                android:id="@+id/iconPreview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_6dp"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_visibility" />

            <ImageView
                android:id="@+id/iconFavorite"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_6dp"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_home" />

            <ImageView
                android:id="@+id/iconSettings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_6dp"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_setting" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- ViewPager to handle tab content -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/headerLayout" />

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>