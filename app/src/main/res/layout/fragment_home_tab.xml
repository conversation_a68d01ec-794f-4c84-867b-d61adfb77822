<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipeRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/img_background_home">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/_16dp">

            <!-- Lock Screen Status Setting -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/settingLockScreenStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5dp"
                android:background="@drawable/bg_setting_item"
                android:paddingVertical="@dimen/_2dp"
                android:paddingHorizontal="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/imgLockScreenStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_lockscreen_settings_tab"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/lock_screen_status"
                    android:textSize="@dimen/_16sp"
                    android:fontFamily="@font/font_700"
                    android:layout_marginHorizontal="@dimen/_10dp"
                    android:textColor="@color/blue_2fafff"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/switchLockScreen"
                    app:layout_constraintStart_toEndOf="@id/imgLockScreenStatus"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switchLockScreen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.tistory.zladnrms.roundablelayout.RoundableLayout
                android:id="@+id/layoutAdsTop"
                app:cornerAll="@dimen/_8dp"
                android:layout_marginTop="@dimen/_18dp"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/settingLockScreenStatus"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="360:80">

                <FrameLayout
                    android:id="@+id/adViewGroupTop"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:background="#D7D6D6">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/loading_ads"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16sp" />

                </FrameLayout>
            </com.tistory.zladnrms.roundablelayout.RoundableLayout>

            <!-- Tooltip for Lock Screen -->
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/tooltipLockScreen"
                android:layout_width="@dimen/_60dp"
                android:layout_height="@dimen/_60dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:lottie_rawRes="@raw/tooltip_swipe_button"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                android:layout_marginTop="@dimen/_30dp"
                app:layout_constraintBottom_toBottomOf="@id/settingLockScreenStatus"
                app:layout_constraintEnd_toEndOf="@id/settingLockScreenStatus"
                app:layout_constraintTop_toTopOf="@id/settingLockScreenStatus" />

            <!-- Customization Options -->
            <LinearLayout
                android:id="@+id/customizationOptions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:orientation="horizontal"
                android:weightSum="3"
                app:layout_constraintTop_toBottomOf="@+id/layoutAdsTop">

                <!-- Zipper Option -->
                <LinearLayout
                    android:id="@+id/zipperOption"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_home_navigation_choose_style"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingVertical="@dimen/_12dp">

                    <ImageView
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:src="@drawable/ic_zipper" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:fontFamily="@font/font_700"
                        android:text="@string/zipper"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_14sp" />
                </LinearLayout>

                <!-- Row Option -->
                <LinearLayout
                    android:id="@+id/rowOption"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_10dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_home_navigation_choose_style"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingVertical="@dimen/_12dp">

                    <ImageView
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:src="@drawable/ic_row" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:fontFamily="@font/font_700"
                        android:text="@string/row"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_14sp" />
                </LinearLayout>

                <!-- Wallpaper Option -->
                <LinearLayout
                    android:id="@+id/wallpaperOption"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_home_navigation_choose_style"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingVertical="@dimen/_12dp">

                    <ImageView
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:src="@drawable/ic_wallpaper" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:fontFamily="@font/font_700"
                        android:text="@string/wallpaper"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_14sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Hot Trending Section -->
            <LinearLayout
                android:id="@+id/hotTrendingSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/customizationOptions">

                <!-- Hot Trending Header with More button -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:drawableStart="@drawable/ic_hot"
                        android:drawablePadding="@dimen/_8dp"
                        android:fontFamily="@font/font_700"
                        android:text="@string/hot_trending"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_18sp"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/btnMoreHotTrending"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_radius_300"
                        android:backgroundTint="@color/white"
                        android:padding="@dimen/_8dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/blue_2fafff" />
                </LinearLayout>

                <!-- Grid of Trending Wallpapers -->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvTrendingWallpapers"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:orientation="horizontal"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_trending_wallpaper_home" />

                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone" />
                </FrameLayout>
            </LinearLayout>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmerCustomize"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                app:layout_constraintTop_toBottomOf="@id/hotTrendingSection"
                app:shimmer_auto_start="true"
                app:shimmer_base_alpha="0.7"
                app:shimmer_direction="left_to_right"
                app:shimmer_duration="1800"
                app:shimmer_repeat_mode="restart"
                app:shimmer_shape="linear"
                app:shimmer_tilt="0">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/btnCustomize"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/img_background_button_customize">

                <TextView
                    android:id="@+id/tvCustomize"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_20dp"
                    android:fontFamily="@font/font_700"
                    android:text="@string/customize_theme"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_20sp"
                    android:textStyle="bold"
                    android:layout_marginEnd="@dimen/_10dp"
                    app:layout_constraintEnd_toStartOf="@+id/imgCustomizeNext"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvPersonalize"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_400"
                    android:text="@string/create_theme_by_yourself"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_10dp"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_14sp"
                    app:layout_constraintEnd_toStartOf="@+id/imgCustomizeNext"
                    android:layout_marginBottom="@dimen/_20dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvCustomize"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <ImageView
                    android:id="@+id/imgCustomizeNext"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_6dp"
                    android:background="@drawable/bg_radius_300"
                    android:backgroundTint="@color/white"
                    android:padding="@dimen/_8dp"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/blue_2fafff" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>