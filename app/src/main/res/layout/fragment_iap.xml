<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_onboard_iap">

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_13dp"
        android:padding="@dimen/_6dp"
        android:src="@drawable/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/btn_skip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/font_700"
        android:padding="@dimen/_6dp"
        android:text="@string/skip"
        android:textColor="@color/white"
        android:textSize="@dimen/_18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/btn_back" />


    <View
        android:id="@+id/view_middle"
        android:layout_width="@dimen/_1dp"
        android:layout_height="@dimen/_19dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="#E5E5E5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_term_of_use"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_7dp"
        android:fontFamily="@font/font_400"
        android:padding="@dimen/_10dp"
        android:text="@string/term_of_use"
        android:textColor="#28ABF5"
        android:textSize="@dimen/_14sp"
        app:layout_constraintBottom_toBottomOf="@id/view_middle"
        app:layout_constraintEnd_toStartOf="@id/view_middle"
        app:layout_constraintTop_toTopOf="@id/view_middle" />

    <TextView
        android:id="@+id/tv_restore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_7dp"
        android:fontFamily="@font/font_400"
        android:padding="@dimen/_10dp"
        android:text="@string/restore"
        android:textColor="#28ABF5"
        android:textSize="@dimen/_14sp"
        app:layout_constraintBottom_toBottomOf="@id/view_middle"
        app:layout_constraintStart_toEndOf="@id/view_middle"
        app:layout_constraintTop_toTopOf="@id/view_middle" />

    <TextView
        android:id="@+id/tv_use_ads_version"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:fontFamily="@font/font_400"
        android:gravity="center"
        android:paddingVertical="@dimen/_12dp"
        android:text="@string/use_ads_version"
        android:textColor="#9E9E9E"
        android:textSize="@dimen/_16sp"
        app:layout_constraintBottom_toTopOf="@id/view_middle" />

    <TextView
        android:id="@+id/btn_subscribe"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_40dp"
        android:background="@drawable/bg_subcribe_btn"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:paddingVertical="@dimen/_12dp"
        android:text="@string/buy_premium"
        android:textColor="@color/white"
        android:textSize="@dimen/_16sp"
        app:layout_constraintBottom_toTopOf="@id/tv_use_ads_version" />

    <ScrollView
        android:id="@+id/scrollView2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_18dp"
        app:layout_constraintBottom_toTopOf="@id/btn_subscribe"
        app:layout_constraintTop_toBottomOf="@id/btn_back">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_700"
                android:text="@string/get_premium_once_enjoy_forever"
                android:textColor="@color/white"
                android:textSize="@dimen/_28sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_11dp"
                android:fontFamily="@font/font_400"
                android:text="@string/enjoy_ad_free_access_and_unlock_all_styles_amp_features_for_your_zip_lock_screen"
                android:textColor="@color/white"
                android:textSize="@dimen/_14sp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_18dp">

                <LinearLayout
                    android:id="@+id/ll_des"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_radius_16"
                    android:backgroundTint="@color/white"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/_24dp"
                    android:paddingBottom="@dimen/_18dp"
                    app:layout_constraintTop_toTopOf="@id/view_middle_star">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_18dp"
                        android:fontFamily="@font/font_600"
                        android:text="@string/you_ll_get"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16sp" />

                    <include
                        android:id="@+id/layout_no_ads"
                        layout="@layout/item_star_onboard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_14dp" />

                    <include
                        android:id="@+id/layout_premium_content"
                        layout="@layout/item_star_onboard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_14dp" />

                    <include
                        android:id="@+id/layout_all_function"
                        layout="@layout/item_star_onboard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_14dp" />

                    <include
                        android:id="@+id/layout_quick_open_app"
                        layout="@layout/item_star_onboard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_14dp" />
                </LinearLayout>

                <View
                    android:id="@+id/view_middle_star"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1dp"
                    app:layout_constraintBottom_toBottomOf="@id/iv_star"
                    app:layout_constraintTop_toTopOf="@id/iv_star" />

                <ImageView
                    android:id="@+id/iv_star"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:src="@drawable/ic_star_onboard"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/ll_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginBottom="@dimen/_24dp"
                android:background="@drawable/bg_radius_14"
                android:backgroundTint="@color/white"
                android:gravity="center_vertical"
                app:layout_constraintBottom_toTopOf="@id/btn_subscribe">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_20dp"
                    android:fontFamily="@font/font_400"
                    android:text="@string/permanly"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_24sp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_10dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingVertical="@dimen/_16dp">

                    <TextView
                        android:id="@+id/tv_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_400"
                        android:textAlignment="textEnd"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_30sp"
                        tools:text="69.000 VND" />

                    <TextView
                        android:id="@+id/tv_price_old"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_400"
                        android:textAlignment="textEnd"
                        android:textColor="#CC1D1D"
                        android:textSize="@dimen/_18sp"
                        tools:text="100.000 VND" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>