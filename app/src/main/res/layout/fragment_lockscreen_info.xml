<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_background_home">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Apply Button -->
    <TextView
        android:id="@+id/tvApply"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        android:background="@drawable/bg_radius_8"
        android:backgroundTint="@color/blue_2fafff"
        android:fontFamily="@font/font_700"
        android:paddingHorizontal="@dimen/_10dp"
        android:paddingVertical="@dimen/_6dp"
        android:text="@string/apply"
        android:textColor="@color/white"
        android:textSize="@dimen/_16sp"

        app:layout_constraintBottom_toBottomOf="@id/ivBack"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivBack" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/lockscreen_info"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/ivBack"
        app:layout_constraintEnd_toStartOf="@id/tvApply"
        app:layout_constraintStart_toEndOf="@id/ivBack"
        app:layout_constraintTop_toTopOf="@id/ivBack" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_20dp"
        android:padding="@dimen/_16dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Preview Section -->
            <co.ziplock.customview.ZipperView
                android:id="@+id/zipperPreview"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="H,168:336"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.47" />

            <ImageView
                android:id="@+id/ivPreview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_preview_edit_layout"
                app:layout_constraintBottom_toBottomOf="@id/zipperPreview"
                app:layout_constraintEnd_toEndOf="parent" />

            <FrameLayout
                android:id="@+id/layoutAdsTop"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/zipperPreview"
                android:layout_marginTop="@dimen/_10dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="360:70">

                <FrameLayout
                    android:id="@+id/adViewGroupTop"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:background="#D7D6D6">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/loading_ads"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16sp" />

                </FrameLayout>
            </FrameLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvFontFamilies"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/layoutAdsTop"
                tools:itemCount="6"
                tools:listitem="@layout/item_font_family" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvFontColors"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_24dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/rvFontFamilies"
                tools:listitem="@layout/item_font_color" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>