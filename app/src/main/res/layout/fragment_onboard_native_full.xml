<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvSwipe"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#313131"
        android:fontFamily="@font/font_600"
        android:paddingVertical="@dimen/_21dp"
        android:text="@string/swipe_to_continue"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/_14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/tvSwipe"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black" />

        </FrameLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>