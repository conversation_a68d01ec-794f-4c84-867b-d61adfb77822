<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/img_background_1_background_layer">

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/img_background_1_top_layer"
            app:layout_constraintDimensionRatio="H,247:344"
            app:layout_constraintBottom_toTopOf="@id/textTitle"
            android:layout_marginBottom="@dimen/_40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.9" />

        <TextView
            android:id="@+id/textTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_30dp"
            android:layout_marginBottom="@dimen/_10dp"
            android:fontFamily="@font/font_700"
            android:text="@string/title_onboarding_screen_1"
            android:textAlignment="center"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="@dimen/_20sp"
            app:layout_constraintBottom_toTopOf="@id/textDescription"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/textDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_30dp"
            android:layout_marginBottom="@dimen/_36dp"
            android:fontFamily="@font/font_400"
            android:text="@string/description_onboarding_screen_1_new"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/_14sp"
            app:layout_constraintBottom_toTopOf="@id/dotsIndicator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <co.ziplock.customview.CustomDotsIndicator
            android:id="@+id/dotsIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_19dp"
            android:layout_marginBottom="@dimen/_24dp"
            app:dotCornerRadius="@dimen/_10dp"
            app:dotHeight="@dimen/_4dp"
            app:dotSpacing="@dimen/_8dp"
            app:dotWidth="@dimen/_4dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintStart_toStartOf="parent"
            app:numbersOfDots="4"
            app:selectedDotColor="@color/blue_3e9df4"
            app:selectedDotWidthRatio="6"
            app:unselectedDotColor="@color/gray_9e9e9e" />

        <TextView
            android:id="@+id/btnNext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_14dp"
            android:layout_marginBottom="@dimen/_10dp"
            android:fontFamily="@font/font_700"
            android:padding="@dimen/_10dp"
            android:text="@string/next"
            android:textColor="@color/white"
            android:textSize="@dimen/_14sp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintEnd_toEndOf="parent" />


        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:159">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
