<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f5f5f5"
    android:padding="@dimen/_24dp">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vFakeBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/password_recovery"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/vFakeBack"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <!-- Grid Layout for Recovery Options -->
    <LinearLayout
        android:id="@+id/gridRecoveryOptions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <!-- Security Question Option -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardSecurityQuestion"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_8dp"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/_8dp"
            app:cardElevation="@dimen/_2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/_16dp">

                <ImageView
                    android:layout_width="@dimen/_48dp"
                    android:layout_height="@dimen/_48dp"
                    android:src="@drawable/ic_security_question_recover_emal_screen" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:fontFamily="@font/font_700"
                    android:textAlignment="center"
                    android:lines="2"
                    android:text="@string/label_security_question"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Email Recovery Option -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardEmailRecovery"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_8dp"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/_8dp"
            app:cardElevation="@dimen/_2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/_16dp">

                <ImageView
                    android:layout_width="@dimen/_48dp"
                    android:layout_height="@dimen/_48dp"
                    android:src="@drawable/ic_email_forgot_password" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:fontFamily="@font/font_700"
                    android:textAlignment="center"
                    android:lines="2"
                    android:text="@string/recovery_email"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_16sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>