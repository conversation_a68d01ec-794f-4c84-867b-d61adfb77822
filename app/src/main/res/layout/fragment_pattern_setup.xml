<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f5f5f5">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/vFakeBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/pattern"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/vFakeBack"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <!-- Subtitle -->
        <TextView
            android:id="@+id/tvSubtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:fontFamily="@font/font_400"
            android:gravity="center"
            android:text="@string/set_your_new_pattern"
            android:textColor="@color/black_35496d"
            android:textSize="@dimen/_16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Helper Text -->
        <TextView
            android:id="@+id/tvHelperText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:fontFamily="@font/font_400"
            android:gravity="center"
            android:text="@string/connect_at_least_4_dots"
            android:textColor="@color/black_35496d"
            android:textSize="@dimen/_14sp"
            android:alpha="0.7"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSubtitle" />

        <!-- Pattern Lock View -->
        <com.andrognito.patternlockview.PatternLockView
            android:id="@+id/patternLockView"
            android:layout_width="@dimen/_280dp"
            app:layout_constraintBottom_toTopOf="@id/llButtonContainer"
            android:layout_height="0dp"
            app:aspectRatio="square"
            android:layout_marginVertical="@dimen/_10dp"
            app:aspectRatioEnabled="true"
            app:dotCount="3"
            app:dotNormalSize="@dimen/_16dp"
            app:dotSelectedSize="@dimen/_24dp"
            app:pathWidth="@dimen/_8dp"
            app:normalStateColor="@color/black_35496d"
            app:correctStateColor="@color/blue_2fafff"
            app:wrongStateColor="@color/red_error"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvHelperText" />

        <!-- Button Container -->
        <LinearLayout
            android:id="@+id/llButtonContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_32dp"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_marginBottom="@dimen/_10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.8">
            
            <!-- Next/Apply Button -->
            <TextView
                android:id="@+id/btnNext"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/blue_2fafff"
                android:enabled="false"
                android:fontFamily="@font/font_700"
                android:padding="@dimen/_12dp"
                android:text="@string/next"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp" />

            <!-- Cancel Button -->
            <TextView
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:fontFamily="@font/font_400"
                android:padding="@dimen/_12dp"
                android:text="@string/cancel"
                android:textAlignment="center"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:100">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>