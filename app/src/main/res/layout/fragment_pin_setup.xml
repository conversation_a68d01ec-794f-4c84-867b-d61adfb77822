<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f5f5f5"
   >

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/btnTogglePinVisibility"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:src="@drawable/ic_visibility_off"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/pin_code"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/btnTogglePinVisibility"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <!-- Lock Icon -->
    <ImageView
        android:id="@+id/ivLockIcon"
        android:layout_width="wrap_content"
        android:layout_marginVertical="@dimen/_10dp"
        android:layout_height="0dp"
        android:minWidth="@dimen/_100dp"
        app:layout_constraintBottom_toTopOf="@id/cl_enter_pin"
        android:src="@drawable/ic_pin_code_password_type_dialog"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        app:tint="@color/blue" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_enter_pin"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/_80dp"
        android:layout_marginHorizontal="@dimen/_24dp"
        android:layout_marginTop="@dimen/_32dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        android:background="@drawable/bg_radius_8"
        android:backgroundTint="@color/white">

        <!-- Subtitle -->
        <TextView
            android:id="@+id/tvSubtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_24dp"
            android:fontFamily="@font/font_400"
            android:gravity="center"
            android:text="@string/set_your_new_pin_code"
            android:textColor="@color/black_35496d"
            android:textSize="@dimen/_16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- PIN Input Container -->
        <LinearLayout
            android:id="@+id/llPinContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_32dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSubtitle">

            <EditText
                android:id="@+id/etPin1"
                android:layout_width="@dimen/_48dp"
                android:layout_height="@dimen/_48dp"
                android:layout_marginEnd="@dimen/_12dp"
                android:background="@drawable/bg_pin_input"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:inputType="numberPassword"
                android:maxLength="1"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_20sp" />

            <EditText
                android:id="@+id/etPin2"
                android:layout_width="@dimen/_48dp"
                android:layout_height="@dimen/_48dp"
                android:layout_marginEnd="@dimen/_12dp"
                android:background="@drawable/bg_pin_input"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:inputType="numberPassword"
                android:maxLength="1"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_20sp" />

            <EditText
                android:id="@+id/etPin3"
                android:layout_width="@dimen/_48dp"
                android:layout_height="@dimen/_48dp"
                android:layout_marginEnd="@dimen/_12dp"
                android:background="@drawable/bg_pin_input"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:inputType="numberPassword"
                android:maxLength="1"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_20sp" />

            <EditText
                android:id="@+id/etPin4"
                android:layout_width="@dimen/_48dp"
                android:layout_height="@dimen/_48dp"
                android:background="@drawable/bg_pin_input"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:inputType="numberPassword"
                android:maxLength="1"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_20sp" />

        </LinearLayout>

        <!-- Button Container -->
        <LinearLayout
            android:id="@+id/llButtonContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_48dp"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_marginBottom="@dimen/_10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llPinContainer"
            app:layout_constraintWidth_percent="0.8">
            <!-- Next/Apply Button -->
            <TextView
                android:id="@+id/btnNext"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_radius_8"
                android:backgroundTint="@color/blue_2fafff"
                android:enabled="false"
                android:fontFamily="@font/font_700"
                android:padding="@dimen/_12dp"
                android:text="@string/next"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_16sp" />

            <!-- Cancel Button -->
            <TextView
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:fontFamily="@font/font_400"
                android:padding="@dimen/_12dp"
                android:text="@string/cancel"
                android:textAlignment="center"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:100">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>