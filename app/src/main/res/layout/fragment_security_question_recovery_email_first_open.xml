<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f5f5f5"
    >

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_10dp"
        android:rotation="180"
        android:layout_margin="@dimen/_24dp"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Skip Button -->
    <TextView
        android:id="@+id/btnSkip"
        android:layout_width="wrap_content"
        android:layout_margin="@dimen/_24dp"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:fontFamily="@font/font_700"
        android:padding="@dimen/_10dp"
        android:text="@string/skip"
        android:textColor="#2C3E50"
        android:textSize="@dimen/_16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/recover_password"
        android:textColor="#2C3E50"
        android:textSize="@dimen/_18sp"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintEnd_toStartOf="@id/btnSkip"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack" />

    <TextView
        android:id="@+id/tvInstruction2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_24dp"
        android:layout_marginTop="@dimen/_32dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/please_enter_the_email_address_you_would_like_to_use_for_account_recovery"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <!-- Main Content Container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_24dp"
        android:layout_marginVertical="@dimen/_24dp"
        android:background="@drawable/bg_radius_8"
        android:backgroundTint="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tvInstruction2"
        tools:layout_editor_absoluteX="27dp">

        <!-- Email Input Section -->
        <LinearLayout
            android:id="@+id/llEmailContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginTop="@dimen/_32dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Email Label -->
            <TextView
                android:id="@+id/tvEmailLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_600"
                android:text="@string/your_email"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

            <!-- Email Input -->
            <EditText
                android:id="@+id/etEmail"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_48dp"
                android:layout_marginTop="@dimen/_8dp"
                android:alpha="0.7"
                android:background="@drawable/bg_pin_input"
                android:hint="@string/enter_your_email"
                android:inputType="textEmailAddress"
                android:paddingHorizontal="@dimen/_12dp"
                android:textColor="@color/black_35496d"
                android:textColorHint="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

        </LinearLayout>

        <!-- Save Button -->
        <TextView
            android:id="@+id/btnSave"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginVertical="@dimen/_24dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/blue_2fafff"
            android:enabled="false"
            android:fontFamily="@font/font_700"
            android:padding="@dimen/_12dp"
            android:text="@string/save"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_16sp"
            app:layout_constraintBottom_toTopOf="@id/tvError"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llEmailContainer" />

        <!-- Error Message -->
        <TextView
            android:id="@+id/tvError"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginBottom="@dimen/_32dp"
            android:fontFamily="@font/font_400"
            android:gravity="center"
            android:text="@string/enter_a_valid_email_address"
            android:textColor="#FF0000"
            android:textSize="@dimen/_14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnSave"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>


    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>