<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@drawable/img_background_home">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/_16dp">

        <!-- Turn ON/OFF Password Setting -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settingPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_setting_item"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imgSettingPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_password_settings_tab"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/turn_on_off_password"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black_35496d"
                android:layout_marginHorizontal="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/switchPassword"
                app:layout_constraintStart_toEndOf="@id/imgSettingPassword"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Tooltip for Password -->
        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/tooltipPassword"
            android:layout_width="@dimen/_60dp"
            android:layout_height="@dimen/_60dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:lottie_rawRes="@raw/tooltip_swipe_button"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:layout_constraintBottom_toBottomOf="@id/settingPassword"
            app:layout_constraintEnd_toEndOf="@id/settingPassword"
            app:layout_constraintTop_toTopOf="@id/settingPassword"
            android:layout_marginTop="@dimen/_30dp" />

        <com.tistory.zladnrms.roundablelayout.RoundableLayout
            android:id="@+id/layoutAdsTop"
            app:cornerAll="@dimen/_8dp"
            android:layout_marginTop="@dimen/_8dp"
            android:layout_width="match_parent"
            app:layout_constraintTop_toBottomOf="@id/settingPassword"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="360:80">

            <FrameLayout
                android:id="@+id/adViewGroupTop"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </com.tistory.zladnrms.roundablelayout.RoundableLayout>

        <!-- Change Password Setting -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settingChangePassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_setting_item"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/layoutAdsTop">

            <ImageView
                android:id="@+id/imgSettingChangePassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_change_password_settings_tab"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/change_password"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black_35496d"
                android:layout_marginHorizontal="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/ivArrowChangePassword"
                app:layout_constraintStart_toEndOf="@id/imgSettingChangePassword"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivArrowChangePassword"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Password Recovery Setting -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settingPasswordRecovery"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_setting_item"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/settingChangePassword">

            <ImageView
                android:id="@+id/imgSettingPasswordRecovery"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_security_settings_tab"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/password_recovery"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black_35496d"
                android:layout_marginHorizontal="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/ivArrowPasswordRecovery"
                app:layout_constraintStart_toEndOf="@id/imgSettingPasswordRecovery"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivArrowPasswordRecovery"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:src="@drawable/ic_arrow_right"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Sound Setting -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settingSound"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_setting_item"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/settingPasswordRecovery">

            <ImageView
                android:id="@+id/imgSettingSound"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_sound_settings_screen"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/sound"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black_35496d"
                android:layout_marginHorizontal="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/switchSound"
                app:layout_constraintStart_toEndOf="@id/imgSettingSound"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchSound"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Vibration Setting -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settingVibration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_setting_item"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/settingSound">

            <ImageView
                android:id="@+id/imgSettingVibration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_vibration_settings_screen"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/vibration"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black_35496d"
                android:layout_marginHorizontal="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/switchVibration"
                app:layout_constraintStart_toEndOf="@id/imgSettingVibration"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchVibration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Date & Time Setting -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settingDateTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_setting_item"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/settingVibration">

            <ImageView
                android:id="@+id/imgSettingDateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_date_time_settings_screen"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/date_time"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black_35496d"
                android:layout_marginHorizontal="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/switchDateTime"
                app:layout_constraintStart_toEndOf="@id/imgSettingDateTime"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchDateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Battery Widget Setting -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/settingBatteryWidget"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_setting_item"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/settingDateTime">

            <ImageView
                android:id="@+id/imgSettingBatteryWidget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_battery_settings_screen"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/battery_widget"
                android:textSize="@dimen/_16sp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black_35496d"
                android:layout_marginHorizontal="@dimen/_10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/switchBatteryWidget"
                app:layout_constraintStart_toEndOf="@id/imgSettingBatteryWidget"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchBatteryWidget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>