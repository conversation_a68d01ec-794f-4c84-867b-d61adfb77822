<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_background_splash"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/txtAppName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ziplock_title"
        android:textAllCaps="true"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_32"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/_28dp"
        android:fontFamily="@font/phosphate_inline"/>

    <TextView
        android:id="@+id/txvLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_8dp"
        android:fontFamily="@font/font_700"
        android:text="@string/this_action_may_contain_ads"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_16"
        app:layout_constraintBottom_toTopOf="@+id/loadingView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <co.ziplock.customview.LoadingView
        android:id="@+id/loadingView"
        android:layout_width="0dp"
        android:layout_height="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>