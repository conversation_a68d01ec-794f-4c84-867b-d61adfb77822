<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_background_home">

    <!-- Home Button -->
    <ImageView
        android:id="@+id/ivHome"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="@dimen/_8dp"
        android:src="@drawable/ic_back_home_save_success"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/blue_2fafff" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/ivHome">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Success Message -->
            <TextView
                android:id="@+id/tvSuccessMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_18dp"
                android:fontFamily="@font/font_700"
                android:gravity="center"
                android:text="@string/zipper_lock_set_successfully"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_20sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Success Animation -->
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lottieSuccess"
                android:layout_width="@dimen/_120dp"
                android:layout_height="@dimen/_120dp"
                android:layout_marginTop="@dimen/_24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvSuccessMessage"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/anim_successfully" />

            <FrameLayout
                android:id="@+id/layoutAdsTop"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                app:layout_constraintDimensionRatio="360:70"
                app:layout_constraintTop_toBottomOf="@id/lottieSuccess">

                <FrameLayout
                    android:id="@+id/adViewGroupTop"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:background="#D7D6D6">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/loading_ads"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16sp" />

                </FrameLayout>
            </FrameLayout>

            <!-- Top 3 Themes Title -->
            <TextView
                android:id="@+id/tvTop3Title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:fontFamily="@font/font_600"
                android:text="@string/try_top_3_zipper_lock_themes"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutAdsTop" />

            <!-- Top 3 Themes RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvTop3Themes"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_32dp"
                android:layout_marginTop="@dimen/_16dp"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTop3Title"
                tools:itemCount="3"
                tools:listitem="@layout/item_theme_preview" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>


    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:background="#D7D6D6">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/font_400"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black"
                android:textSize="@dimen/_16sp" />

        </FrameLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>