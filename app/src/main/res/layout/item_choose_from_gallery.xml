<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/_75dp"
    android:layout_height="@dimen/_125dp"
    android:layout_marginEnd="@dimen/_12dp">

    <co.ziplock.customview.RoundedImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/img_background_upload_from_gallery"
        app:cornerRadius="@dimen/_10dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:src="@drawable/ic_gallery_to_pick_photo"
            android:layout_marginBottom="@dimen/_8dp"
            app:tint="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/choose_from_gallery"
            android:textColor="@color/white"
            android:textSize="@dimen/_10sp"
            android:fontFamily="@font/font_600"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>