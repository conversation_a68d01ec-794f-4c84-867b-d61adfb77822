<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="16dp">

    <!-- First Item -->
    <RelativeLayout
        android:id="@+id/rlFirstItem"
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:layout_weight="1"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_radius_16"
        android:backgroundTint="#F8F8F8"
        android:padding="12dp">

        <!-- Edit Icon -->
        <ImageView
            android:id="@+id/ivFirstEdit"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_tool_1_home"
            android:background="?attr/selectableItemBackgroundBorderless" />

        <!-- Main Icon -->
        <ImageView
            android:id="@+id/ivFirstIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_centerHorizontal="true"
            android:layout_above="@+id/tvFirstLabel"
            android:layout_marginBottom="8dp"
            tools:src="@drawable/ic_zipper" />

        <!-- Label -->
        <TextView
            android:id="@+id/tvFirstLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:textSize="12sp"
            android:textColor="@color/black"
            android:gravity="center"
            tools:text="Zipper" />

    </RelativeLayout>

    <!-- Second Item -->
    <RelativeLayout
        android:id="@+id/rlSecondItem"
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:layout_weight="1"
        android:layout_marginStart="8dp"
        android:background="@drawable/bg_radius_16"
        android:backgroundTint="#F8F8F8"
        android:padding="12dp">

        <!-- Edit Icon -->
        <ImageView
            android:id="@+id/ivSecondEdit"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_tool_1_home"
            android:background="?attr/selectableItemBackgroundBorderless" />

        <!-- Main Icon -->
        <ImageView
            android:id="@+id/ivSecondIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_centerHorizontal="true"
            android:layout_above="@+id/tvSecondLabel"
            android:layout_marginBottom="8dp"
            tools:src="@drawable/ic_tool_2_home" />

        <!-- Label -->
        <TextView
            android:id="@+id/tvSecondLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:textSize="12sp"
            android:textColor="@color/black"
            android:gravity="center"
            tools:text="Row" />

    </RelativeLayout>

</LinearLayout>