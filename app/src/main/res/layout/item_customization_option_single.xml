<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clMain"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/bg_dash_item_customize"
            android:layout_margin="@dimen/_8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintDimensionRatio="1:1">

            <!-- Main Icon -->
            <ImageView
                android:id="@+id/ivIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:src="@drawable/ic_zipper" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Label -->
        <TextView
            android:id="@+id/tvLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/_4dp"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clMain"
            tools:text="Zipper" />

        <!-- Edit Icon -->
        <ImageView
            android:id="@+id/ivEdit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_change_item_customization"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>