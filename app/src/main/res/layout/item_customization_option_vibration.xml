<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clMain"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/bg_dash_item_customize"
            android:layout_margin="@dimen/_8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintDimensionRatio="1:1">

            <!-- Switch for vibration -->
            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchVibration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginBottom="@dimen/_8dp" />

            <!-- ON/OFF Text -->
            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_700"
                android:textColor="@color/black"
                android:textSize="@dimen/_10sp"
                app:layout_constraintTop_toBottomOf="@id/switchVibration"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="@dimen/_4dp"
                tools:text="ON" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Label -->
        <TextView
            android:id="@+id/tvLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/_4dp"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clMain"
            tools:text="Vibration" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>