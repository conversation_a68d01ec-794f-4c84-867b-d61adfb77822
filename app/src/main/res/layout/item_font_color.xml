<?xml version="1.0" encoding="utf-8"?>
<layout>

    <RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="@dimen/_60dp"
        android:layout_height="@dimen/_60dp"
        android:layout_marginEnd="@dimen/_10dp">

        <View
            android:id="@+id/vColorBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_radius_300"
            tools:backgroundTint="@color/white" />

        <!-- Selection indicator -->
        <ImageView
            android:id="@+id/ivSelected"
            android:layout_width="@dimen/_18dp"
            android:layout_height="@dimen/_18dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_select_font_and_color_lock_screen"
            android:visibility="visible" />

    </RelativeLayout>
</layout>