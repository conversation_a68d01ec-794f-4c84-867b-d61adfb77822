<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="@dimen/_60dp"
        android:layout_height="@dimen/_60dp"
        android:layout_marginEnd="@dimen/_10dp">

        <FrameLayout
            android:id="@+id/flBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_radius_300"
            android:backgroundTint="#F0F0F0">

            <TextView
                android:id="@+id/tvFontSample"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/black"
                android:textSize="@dimen/_20sp"
                tools:text="Aa" />
        </FrameLayout>

        <!-- Selection indicator -->
        <ImageView
            android:id="@+id/ivSelected"
            android:layout_width="@dimen/_18dp"
            android:layout_height="@dimen/_18dp"
            android:layout_gravity="top|end"
            android:src="@drawable/ic_select_font_and_color_lock_screen"
            android:visibility="visible" />

    </FrameLayout>
</layout>