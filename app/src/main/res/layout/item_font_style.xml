<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="80dp"
    android:layout_height="60dp"
    android:layout_marginEnd="12dp"
    android:background="@drawable/bg_radius_8"
    android:backgroundTint="#F0F0F0">

    <TextView
        android:id="@+id/tvFontSample"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="Aa"
        android:textSize="20sp"
        android:textColor="@color/black"
        tools:text="Aa" />

    <!-- Selection indicator -->
    <ImageView
        android:id="@+id/ivSelected"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="4dp"
        android:src="@drawable/ic_check_circle"
        android:visibility="gone"
        app:tint="@color/blue" />

</RelativeLayout>