<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_10dp"
        android:padding="@dimen/_16dp">

        <ImageView
            android:id="@+id/ivFlag"
            android:layout_width="@dimen/_32dp"
            android:layout_height="@dimen/_32dp"
            android:scaleType="fitCenter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@android:drawable/ic_menu_gallery" />

        <TextView
            android:id="@+id/tvLanguageName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:textSize="@dimen/_16sp"
            android:fontFamily="@font/font_600"
            android:textColor="@color/black_35496d"
            app:layout_constraintStart_toEndOf="@id/ivFlag"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivSelected"
            android:layout_marginEnd="@dimen/_8dp"
            tools:text="English" />

        <ImageView
            android:id="@+id/ivSelected"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:src="@drawable/ic_language_select"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
