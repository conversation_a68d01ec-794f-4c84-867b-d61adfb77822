<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_8dp"
        android:clickable="true"
        android:focusable="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <co.ziplock.customview.RoundedImageView
                android:id="@+id/imageView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:cornerRadius="@dimen/_10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:src="@drawable/ic_launcher_background" />

            <TextView
                android:id="@+id/textTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_600"
                android:gravity="center"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sp"
                android:layout_marginTop="@dimen/_5dp"
                android:background="@drawable/bg_item_text_onboard_favorite_style"
                app:layout_constraintBottom_toBottomOf="@id/imageView"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="Style Name" />

            <CheckBox
                android:id="@+id/checkboxSelected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:focusable="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>