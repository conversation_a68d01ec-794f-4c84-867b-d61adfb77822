<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_8dp"
    android:padding="@dimen/_16dp">

    <Button
        android:id="@+id/btnSeeMore"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_48dp"
        android:text="@string/see_more"
        android:textColor="@color/white"
        android:textSize="@dimen/_14sp"
        android:fontFamily="@font/font_600"
        android:background="@drawable/bg_button_primary"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>