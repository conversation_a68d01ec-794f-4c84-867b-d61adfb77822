<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_8dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        app:cardCornerRadius="@dimen/_12dp"
        app:cardElevation="@dimen/_2dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_12dp">

            <!-- Thumbnail Image -->
            <ImageView
                android:id="@+id/ivThumbnail"
                android:layout_width="@dimen/_60dp"
                android:layout_height="@dimen/_60dp"
                android:background="@drawable/bg_rounded_corner_8dp"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_music_note"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Play/Pause Button -->
            <FrameLayout
                android:id="@+id/btnPlayPause"
                android:layout_width="@dimen/_32dp"
                android:layout_height="@dimen/_32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintBottom_toBottomOf="@id/ivThumbnail"
                app:layout_constraintStart_toStartOf="@id/ivThumbnail"
                app:layout_constraintTop_toTopOf="@id/ivThumbnail"
                app:layout_constraintEnd_toEndOf="@id/ivThumbnail">

                <ImageView
                    android:id="@+id/ivPlayPause"
                    android:layout_width="@dimen/_24dp"
                    android:layout_height="@dimen/_24dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_play"
                    app:tint="@color/white"
                    android:visibility="visible" />

                <ProgressBar
                    android:id="@+id/progressLoading"
                    android:layout_width="@dimen/_20dp"
                    android:layout_height="@dimen/_20dp"
                    android:layout_gravity="center"
                    android:visibility="gone" />

            </FrameLayout>

            <!-- Sound Name -->
            <TextView
                android:id="@+id/tvSoundName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_marginEnd="@dimen/_8dp"
                android:ellipsize="end"
                android:fontFamily="@font/font_600"
                android:maxLines="2"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp"
                app:layout_constraintBottom_toBottomOf="@+id/ivThumbnail"
                app:layout_constraintEnd_toStartOf="@id/rbSelect"
                app:layout_constraintStart_toEndOf="@id/ivThumbnail"
                app:layout_constraintTop_toTopOf="@id/ivThumbnail"
                tools:text="Healing night" />

            <!-- Radio Button -->
            <RadioButton
                android:id="@+id/rbSelect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:buttonTint="@color/radio_button_tint"
                android:clickable="false"
                android:focusable="false"
                app:layout_constraintBottom_toBottomOf="@id/ivThumbnail"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/ivThumbnail" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>
</layout>