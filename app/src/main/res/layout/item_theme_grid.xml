<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_4dp"
    android:background="@drawable/bg_rounded_8dp"
    android:elevation="@dimen/_2dp">

    <ImageView
        android:id="@+id/ivThumbnail"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:background="@drawable/bg_rounded_8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintDimensionRatio="1:1.2" />

    <ImageView
        android:id="@+id/ivProBadge"
        android:layout_width="@dimen/_24dp"
        android:layout_height="@dimen/_24dp"
        android:layout_margin="@dimen/_8dp"
        android:src="@drawable/ic_pro_badge"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Loading overlay -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/_24dp"
        android:layout_height="@dimen/_24dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>