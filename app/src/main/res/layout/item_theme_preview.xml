<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_8dp"
        android:background="?attr/selectableItemBackground">

        <ImageView
            android:id="@+id/ivThemePreview"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:background="@drawable/bg_radius_8"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Hot Trending Indicator -->
        <ImageView
            android:id="@+id/ivHotIndicator"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_margin="@dimen/_4dp"
            android:src="@drawable/ic_hot"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/ivThemePreview"
            app:layout_constraintTop_toTopOf="@id/ivThemePreview"
            app:tint="@color/red_error" />

        <TextView
            android:id="@+id/tvThemeName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:fontFamily="@font/font_500"
            android:gravity="center"
            android:textColor="@color/black_35496d"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="@id/ivThemePreview"
            app:layout_constraintStart_toStartOf="@id/ivThemePreview"
            app:layout_constraintTop_toBottomOf="@id/ivThemePreview" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>