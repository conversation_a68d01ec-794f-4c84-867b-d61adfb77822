<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_180dp"
        android:layout_margin="@dimen/_4dp"
        app:cardCornerRadius="@dimen/_8dp"
        app:cardElevation="@dimen/_2dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ivWallpaper"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/gradient_overlay" />


            <TextView
                android:id="@+id/btnTryNow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_8dp"
                android:paddingStart="@dimen/_12dp"
                android:paddingTop="@dimen/_4dp"
                android:paddingEnd="@dimen/_12dp"
                android:paddingBottom="@dimen/_4dp"
                android:text="@string/button_try_now"
                android:fontFamily="@font/font_700"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>