<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_8dp"
        app:cardCornerRadius="@dimen/_12dp"
        app:cardElevation="@dimen/_4dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Wallpaper Image -->
            <ImageView
                android:id="@+id/ivWallpaper"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="H,96:160"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ic_wallpaper" />

            <!-- Overlay for selection/hover effect -->
            <View
                android:id="@+id/overlay"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/black_transparent_20"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/ivWallpaper"
                app:layout_constraintTop_toTopOf="@id/ivWallpaper" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>
</layout>