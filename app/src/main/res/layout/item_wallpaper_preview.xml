<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/_75dp"
    android:layout_height="@dimen/_125dp"
    android:layout_marginEnd="@dimen/_12dp"
    android:background="@drawable/bg_item_preview">

    <co.ziplock.customview.RoundedImageView
        android:id="@+id/ivWallpaper"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_wallpaper"
        app:cornerRadius="@dimen/_10dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>