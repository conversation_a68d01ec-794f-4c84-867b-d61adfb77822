<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="@dimen/_80dp"
        android:layout_height="@dimen/_80dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:background="@drawable/bg_item_preview"
        android:padding="@dimen/_4dp">

        <ImageView
            android:id="@+id/ivZipper"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_zipper" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>