<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/adViewParent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:backgroundTint="#707070"
        app:cardCornerRadius="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="328:136"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/adViewHolder"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_ad"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_radius_1"
                android:backgroundTint="#FFA800"
                android:paddingHorizontal="11dp"
                android:text="AD"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <LinearLayout
                android:layout_width="0dp"
                android:id="@+id/ll_content"
                android:layout_marginTop="@dimen/_9dp"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                app:layout_constraintEnd_toStartOf="@id/tv_ad"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.cardview.widget.CardView
                    android:id="@+id/ad_app_icon_container"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginStart="@dimen/_10dp"
                    app:cardCornerRadius="@dimen/_8dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <FrameLayout
                        android:id="@+id/ad_app_icon"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@id/ad_app_icon_container"
                    app:layout_constraintEnd_toStartOf="@id/tv_ad"
                    app:layout_constraintStart_toEndOf="@id/ad_app_icon_container"
                    app:layout_constraintTop_toTopOf="@id/ad_app_icon_container">

                    <TextView
                        android:id="@+id/ad_headline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_8dp"
                        android:layout_marginBottom="@dimen/_6dp"
                        android:ellipsize="end"
                        android:lines="1"
                        android:maxLines="1"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toTopOf="@+id/ad_body"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        tools:text="Đây là tiêu đề của quảng cáo" />

                    <TextView
                        android:id="@+id/ad_body"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_8dp"
                        android:ellipsize="end"
                        android:lines="2"
                        android:maxLines="2"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_12sp"
                        app:layout_constraintBottom_toTopOf="@+id/ad_call_to_action"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        tools:text="Đây là phần mô tả của quảng cáo kéo dài như thế này" />

                </LinearLayout>

            </LinearLayout>


            <TextView
                android:id="@+id/ad_call_to_action"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#00C008"
                app:layout_constraintTop_toBottomOf="@id/ll_content"
                android:gravity="center"
                android:maxLines="1"
                android:paddingVertical="10dp"
                android:layout_marginHorizontal="@dimen/_10dp"
                android:textColor="@color/white"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:text="Open" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>
