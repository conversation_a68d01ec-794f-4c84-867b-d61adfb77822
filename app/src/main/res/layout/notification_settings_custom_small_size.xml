<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:padding="@dimen/_16dp">

    <!-- Title -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!-- App Icon -->
        <ImageView
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:src="@mipmap/ic_launcher"
            android:layout_marginEnd="@dimen/_8dp" />

        <!-- Title Text -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/overlay_settings_notification_find_ziplock"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_14sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/overlay_settings_notification_turn_it_on"
                android:textColor="@color/orange_ff5c00"
                android:textSize="@dimen/_14sp"
                android:textStyle="bold"
                android:layout_marginStart="@dimen/_5dp"/>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>