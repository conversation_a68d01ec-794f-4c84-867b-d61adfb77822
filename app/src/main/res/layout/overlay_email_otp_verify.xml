<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="@dimen/_24dp">

    <!-- Back Button -->
    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:padding="@dimen/_4dp"
        android:src="@drawable/ic_arrow_left"
        app:tint="@color/black_35496d"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_5dp"
        android:fontFamily="@font/font_700"
        android:gravity="center"
        android:text="@string/check_your_email"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnBack"
        app:layout_constraintTop_toTopOf="@id/btnBack"
        app:layout_constraintBottom_toBottomOf="@id/btnBack" />

    <!-- Main Content Container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMainContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_24dp"
        android:background="@drawable/bg_radius_8"
        android:backgroundTint="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <!-- Verification Code Container -->
        <LinearLayout
            android:id="@+id/llVerificationCodeContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginTop="@dimen/_32dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Code Label -->
            <TextView
                android:id="@+id/tvCodeLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/font_600"
                android:text="@string/security_code"
                android:textColor="@color/black_35496d"
                android:textSize="@dimen/_16sp" />

            <!-- Verification Code Input -->
            <LinearLayout
                android:id="@+id/layoutVerificationCodeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:gravity="center"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/etCode1"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:inputType="number"
                    android:maxLength="1"
                    android:gravity="center"
                    android:textSize="@dimen/_16sp" />

                <EditText
                    android:id="@+id/etCode2"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:inputType="number"
                    android:maxLength="1"
                    android:gravity="center"
                    android:textSize="@dimen/_16sp" />

                <EditText
                    android:id="@+id/etCode3"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:inputType="number"
                    android:maxLength="1"
                    android:gravity="center"
                    android:textSize="@dimen/_16sp" />

                <EditText
                    android:id="@+id/etCode4"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:inputType="number"
                    android:maxLength="1"
                    android:gravity="center"
                    android:textSize="@dimen/_16sp" />

                <EditText
                    android:id="@+id/etCode5"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:layout_marginEnd="@dimen/_4dp"
                    android:inputType="number"
                    android:maxLength="1"
                    android:gravity="center"
                    android:textSize="@dimen/_16sp" />

                <EditText
                    android:id="@+id/etCode6"
                    style="@style/PinInputStyle"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="@dimen/_40dp"
                    android:inputType="number"
                    android:maxLength="1"
                    android:gravity="center"
                    android:textSize="@dimen/_16sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Error Message -->
        <TextView
            android:id="@+id/tvError"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginTop="@dimen/_8dp"
            android:fontFamily="@font/font_400"
            android:gravity="start"
            android:text="@string/invalid_verification_code"
            android:textColor="@color/red_error"
            android:textSize="@dimen/_14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llVerificationCodeContainer"
            tools:visibility="visible" />

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvError" />

        <!-- Verify Code Button -->
        <TextView
            android:id="@+id/btnVerifyCode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_24dp"
            android:layout_marginTop="@dimen/_24dp"
            android:layout_marginBottom="@dimen/_32dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/blue_2fafff"
            android:enabled="false"
            android:fontFamily="@font/font_700"
            android:padding="@dimen/_12dp"
            android:text="@string/ok"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvError"
            app:layout_constraintVertical_bias="0.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Resend Email Link -->
    <TextView
        android:id="@+id/tvInstruction"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/font_400"
        android:gravity="center"
        android:text="@string/a_password_recovery_instruction_email_has_been_sent_to_the_email_address_user_s_entered_email_address_please_check_your_inbox_and_also_the_spam_junk_folder_to_continue"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clMainContent" />

    <TextView
        android:id="@+id/tvCountdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:fontFamily="@font/font_400"
        android:text="@string/otp_expires_in"
        android:textColor="@color/black_35496d"
        android:textSize="@dimen/_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvInstruction" />
        
<!--    <TextView
        android:id="@+id/tvOpenEmailApp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:fontFamily="@font/font_600"
        android:text="@string/open_email_app"
        android:padding="@dimen/_10dp"
        android:drawablePadding="@dimen/_4dp"
        android:drawableStart="@drawable/ic_email_forgot_password"
        android:textColor="@color/blue_2fafff"
        android:textSize="@dimen/_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvCountdown" />-->

    <TextView
        android:id="@+id/tvResendEmail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:fontFamily="@font/font_600"
        android:text="@string/resend_email"
        android:padding="@dimen/_10dp"
        android:drawablePadding="@dimen/_4dp"
        android:drawableStart="@drawable/ic_resend_email_send_otp_screen"
        android:textColor="@color/blue_2fafff"
        android:textSize="@dimen/_14sp"
        android:enabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvCountdown" />
</androidx.constraintlayout.widget.ConstraintLayout>