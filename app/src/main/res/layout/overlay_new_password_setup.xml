<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:descendantFocusability="beforeDescendants">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/_24dp"
            android:background="@color/gray_f5f5f5">

            <!-- Back Button -->
            <ImageView
                android:id="@+id/btnBack"
                android:layout_width="@dimen/_32dp"
                android:layout_height="@dimen/_32dp"
                android:padding="@dimen/_4dp"
                android:src="@drawable/ic_arrow_left"
                app:tint="@color/black_35496d"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Header -->
            <LinearLayout
                android:id="@+id/layoutHeader"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btnBack">

                <!-- Title -->
                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_700"
                    android:gravity="center"
                    android:text="@string/set_new_password"
                    android:textColor="@color/black_35496d"
                    android:textSize="@dimen/_18sp" />

                <!-- Tab Layout -->
                <LinearLayout
                    android:id="@+id/layoutTabs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:id="@+id/tabPinCode"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/_40dp"
                        android:layout_weight="1"
                        android:background="@drawable/tab_selected_background"
                        android:fontFamily="@font/font_600"
                        android:gravity="center"
                        android:text="@string/pin_code"
                        android:textColor="@color/blue_007aff"
                        android:textSize="@dimen/_16sp" />

                    <TextView
                        android:id="@+id/tabPattern"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/_40dp"
                        android:layout_weight="1"
                        android:background="@drawable/tab_unselected_background"
                        android:fontFamily="@font/font_600"
                        android:gravity="center"
                        android:text="@string/pattern"
                        android:textColor="@color/grey_5b5b5b"
                        android:textSize="@dimen/_16sp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Content Area -->
            <FrameLayout
                android:id="@+id/contentArea"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_32dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layoutHeader">

                <!-- PIN Content -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/pinContent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_radius_8"
                    android:backgroundTint="@color/white"
                    android:visibility="visible">

                    <!-- Toggle PIN Visibility Button -->
                    <ImageView
                        android:id="@+id/btnTogglePinVisibility"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_10dp"
                        android:src="@drawable/ic_visibility_off"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- Lock Icon -->
                    <ImageView
                        android:id="@+id/ivLockIcon"
                        android:layout_width="@dimen/_136dp"
                        android:layout_height="@dimen/_136dp"
                        android:layout_marginTop="@dimen/_32dp"
                        android:src="@drawable/ic_pin_code_password_type_dialog"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/blue" />

                    <!-- Subtitle -->
                    <TextView
                        android:id="@+id/tvSubtitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_24dp"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/set_your_new_pin_code"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/ivLockIcon" />

                    <!-- PIN Input Container -->
                    <LinearLayout
                        android:id="@+id/llPinContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_32dp"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvSubtitle">

                        <EditText
                            android:id="@+id/etPin1"
                            android:layout_width="@dimen/_48dp"
                            android:layout_height="@dimen/_48dp"
                            android:layout_marginEnd="@dimen/_12dp"
                            android:background="@drawable/bg_pin_input"
                            android:fontFamily="@font/font_700"
                            android:gravity="center"
                            android:inputType="numberPassword"
                            android:maxLength="1"
                            android:textColor="@color/black_35496d"
                            android:textSize="@dimen/_20sp"
                            android:focusable="false"
                            android:focusableInTouchMode="false" />

                        <EditText
                            android:id="@+id/etPin2"
                            android:layout_width="@dimen/_48dp"
                            android:layout_height="@dimen/_48dp"
                            android:layout_marginEnd="@dimen/_12dp"
                            android:background="@drawable/bg_pin_input"
                            android:fontFamily="@font/font_700"
                            android:gravity="center"
                            android:inputType="numberPassword"
                            android:maxLength="1"
                            android:textColor="@color/black_35496d"
                            android:textSize="@dimen/_20sp"
                            android:focusable="false"
                            android:focusableInTouchMode="false" />

                        <EditText
                            android:id="@+id/etPin3"
                            android:layout_width="@dimen/_48dp"
                            android:layout_height="@dimen/_48dp"
                            android:layout_marginEnd="@dimen/_12dp"
                            android:background="@drawable/bg_pin_input"
                            android:fontFamily="@font/font_700"
                            android:gravity="center"
                            android:inputType="numberPassword"
                            android:maxLength="1"
                            android:textColor="@color/black_35496d"
                            android:textSize="@dimen/_20sp"
                            android:focusable="false"
                            android:focusableInTouchMode="false" />

                        <EditText
                            android:id="@+id/etPin4"
                            android:layout_width="@dimen/_48dp"
                            android:layout_height="@dimen/_48dp"
                            android:background="@drawable/bg_pin_input"
                            android:fontFamily="@font/font_700"
                            android:gravity="center"
                            android:inputType="numberPassword"
                            android:maxLength="1"
                            android:textColor="@color/black_35496d"
                            android:textSize="@dimen/_20sp"
                            android:focusable="false"
                            android:focusableInTouchMode="false" />

                    </LinearLayout>

                    <!-- Custom Keypad -->
                    <include 
                        android:id="@+id/customKeypad"
                        layout="@layout/custom_number_keypad"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_32dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/llPinContainer" />

                    <FrameLayout
                        android:id="@+id/layoutAdsPin"
                        android:layout_width="match_parent"
                        android:layout_marginTop="@dimen/_10dp"
                        android:layout_height="0dp"
                        app:layout_constraintTop_toBottomOf="@id/customKeypad"
                        app:layout_constraintDimensionRatio="360:70">

                        <FrameLayout
                            android:id="@+id/adViewGroupPin"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="bottom"
                            android:background="#D7D6D6">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:fontFamily="@font/font_400"
                                android:gravity="center"
                                android:text="@string/loading_ads"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_16sp" />

                        </FrameLayout>
                    </FrameLayout>

                    <!-- Button Container -->
                    <LinearLayout
                        android:id="@+id/llButtonContainer"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_32dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:layout_marginBottom="@dimen/_10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layoutAdsPin"
                        app:layout_constraintWidth_percent="0.8">
                        
                        <!-- Next/Apply Button -->
                        <TextView
                            android:id="@+id/btnNext"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_radius_8"
                            android:backgroundTint="@color/blue_2fafff"
                            android:enabled="false"
                            android:fontFamily="@font/font_700"
                            android:padding="@dimen/_12dp"
                            android:text="@string/next"
                            android:textAlignment="center"
                            android:textAllCaps="false"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_16sp" />

                        <!-- Cancel Button -->
                        <TextView
                            android:id="@+id/btnCancel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_10dp"
                            android:fontFamily="@font/font_400"
                            android:padding="@dimen/_12dp"
                            android:text="@string/set_later"
                            android:textAlignment="center"
                            android:textColor="@color/black_35496d"
                            android:textSize="@dimen/_16sp" />

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- Pattern Content -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/patternContent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone">

                    <!-- Subtitle -->
                    <TextView
                        android:id="@+id/tvPatternSubtitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_24dp"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/set_your_new_pattern"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_16sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- Helper Text -->
                    <TextView
                        android:id="@+id/tvHelperText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8dp"
                        android:fontFamily="@font/font_400"
                        android:gravity="center"
                        android:text="@string/connect_at_least_4_dots"
                        android:textColor="@color/black_35496d"
                        android:textSize="@dimen/_14sp"
                        android:alpha="0.7"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvPatternSubtitle" />

                    <!-- Pattern Lock View -->
                    <com.andrognito.patternlockview.PatternLockView
                        android:id="@+id/patternLockView"
                        android:layout_width="@dimen/_280dp"
                        android:layout_height="@dimen/_280dp"
                        android:layout_marginTop="@dimen/_24dp"
                        android:clickable="true"
                        android:focusable="true"
                        app:aspectRatio="square"
                        app:aspectRatioEnabled="true"
                        app:dotCount="3"
                        app:dotNormalSize="@dimen/_16dp"
                        app:dotSelectedSize="@dimen/_24dp"
                        app:pathWidth="@dimen/_8dp"
                        app:normalStateColor="@color/black_35496d"
                        app:correctStateColor="@color/blue_2fafff"
                        app:wrongStateColor="@color/red_error"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvHelperText" />

                    <FrameLayout
                        android:id="@+id/layoutAdsPattern"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/_10dp"
                        app:layout_constraintTop_toBottomOf="@id/patternLockView"
                        app:layout_constraintDimensionRatio="360:70">

                        <FrameLayout
                            android:id="@+id/adViewGroupPattern"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="bottom"
                            android:background="#D7D6D6">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:fontFamily="@font/font_400"
                                android:gravity="center"
                                android:text="@string/loading_ads"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_16sp" />

                        </FrameLayout>
                    </FrameLayout>

                    <!-- Button Container -->
                    <LinearLayout
                        android:id="@+id/llPatternButtonContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_32dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:layout_marginBottom="@dimen/_10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layoutAdsPattern"
                        app:layout_constraintWidth_percent="0.8">
                        
                        <!-- Next/Apply Button -->
                        <TextView
                            android:id="@+id/btnPatternNext"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_radius_8"
                            android:backgroundTint="@color/blue_2fafff"
                            android:enabled="false"
                            android:fontFamily="@font/font_700"
                            android:padding="@dimen/_12dp"
                            android:text="@string/next"
                            android:textAlignment="center"
                            android:textAllCaps="false"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_16sp" />

                        <!-- Cancel Button -->
                        <TextView
                            android:id="@+id/btnPatternCancel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_10dp"
                            android:fontFamily="@font/font_400"
                            android:padding="@dimen/_12dp"
                            android:text="@string/set_later"
                            android:textAlignment="center"
                            android:textColor="@color/black_35496d"
                            android:textSize="@dimen/_16sp" />

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>