<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/black"
    android:padding="32dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/draw_pattern_to_unlock"
        android:textColor="@android:color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvErrorMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF5252"
        android:textSize="16sp"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvCountdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF9800"
        android:textSize="18sp"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <com.andrognito.patternlockview.PatternLockView
        android:id="@+id/patternLockView"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_gravity="center"
        app:aspectRatio="square"
        app:aspectRatioEnabled="true"
        app:dotCount="3"
        app:dotNormalSize="28dp"
        app:dotSelectedSize="36dp"
        app:pathWidth="10dp"
        app:normalStateColor="@android:color/white"
        app:correctStateColor="@color/blue_2fafff"
        app:wrongStateColor="@color/red_error" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btnForgotPassword"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/forgot_password"
            android:textAllCaps="false"
            android:padding="12dp"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_color" />


    </LinearLayout>

</LinearLayout>