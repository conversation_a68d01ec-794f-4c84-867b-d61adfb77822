<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:padding="32dp"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/black">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/enter_pin_code_to_unlock"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center" />

        <ImageView
            android:id="@+id/btnTogglePinVisibility"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:src="@drawable/ic_visibility_off"
            app:tint="@android:color/white" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvErrorMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF5252"
        android:textSize="16sp"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvCountdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF9800"
        android:textSize="18sp"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <!-- PIN Input Container -->
    <LinearLayout
        android:id="@+id/llPinContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="32dp"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/etPin1"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_pin_input_overlay"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:inputType="numberPassword"
            android:maxLength="1"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <EditText
            android:id="@+id/etPin2"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_pin_input_overlay"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:inputType="numberPassword"
            android:maxLength="1"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <EditText
            android:id="@+id/etPin3"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_pin_input_overlay"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:inputType="numberPassword"
            android:maxLength="1"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <EditText
            android:id="@+id/etPin4"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/bg_pin_input_overlay"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:inputType="numberPassword"
            android:maxLength="1"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:focusable="false"
            android:focusableInTouchMode="false" />

    </LinearLayout>

    <include 
        android:id="@+id/customKeypad"
        layout="@layout/custom_number_keypad"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btnForgotPassword"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/forgot_password"
            android:textAllCaps="false"
            android:padding="12dp"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_color" />

    </LinearLayout>

</LinearLayout>