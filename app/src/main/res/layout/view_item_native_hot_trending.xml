<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/_4dp"
        android:layout_marginVertical="@dimen/_4dp"
        android:layout_height="wrap_content"
        tools:background="@color/white">

        <androidx.cardview.widget.CardView
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:backgroundTint="@color/black"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardCornerRadius="@dimen/_10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="328:136"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:background="@color/ads_background">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black" />

            </FrameLayout>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>