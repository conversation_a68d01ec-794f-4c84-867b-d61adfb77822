<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_main.xml"
    app:startDestination="@id/splashFragment">

    <fragment
        android:id="@+id/splashFragment"
        android:name="co.ziplock.framework.presentation.splash.SplashFragment"
        android:label="SplashFragment"
        tools:layout="@layout/fragment_splash">
        <action
            android:id="@+id/action_splashFragment_to_homeFragment"
            app:destination="@id/homeFragment" />
        <action
            android:id="@+id/action_splashFragment_to_languageFragment"
            app:destination="@id/languageFragment" />
    </fragment>
    <fragment
        android:id="@+id/homeFragment"
        android:name="co.ziplock.framework.presentation.home.HomeFragment"
        android:label="HomeFragment"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_homeFragment_to_setupRecoveryEmailFirstOpenFragment"
            app:destination="@id/setupRecoveryEmailFirstOpenFragment" />
        <action
            android:id="@+id/action_homeFragment_to_pinSetupFragment"
            app:destination="@id/pinSetupFragment" />
        <action
            android:id="@+id/action_homeFragment_to_patternSetupFragment"
            app:destination="@id/patternSetupFragment" />
        <action
            android:id="@+id/action_homeFragment_to_passwordRecoveryMethodFragment"
            app:destination="@id/passwordRecoveryMethodFragment" />
        <action
            android:id="@+id/action_homeFragment_to_securityQuestionForgotPasswordFragment"
            app:destination="@id/securityQuestionForgotPasswordFragment" />
        <action
            android:id="@+id/action_homeFragment_to_emailPasswordRecoveryWhenForgotPasswordFragment"
            app:destination="@id/emailPasswordRecoveryWhenForgotPasswordFragment" />
        <action
            android:id="@+id/action_homeFragment_to_changeSecurityQuestionFragment"
            app:destination="@id/changeSecurityQuestionFragment" />
        <action
            android:id="@+id/action_homeFragment_to_zipperFragment"
            app:destination="@id/zipperFragment" />
        <action
            android:id="@+id/action_homeFragment_to_wallpaperFragment"
            app:destination="@id/wallpaperFragment" />
        <action
            android:id="@+id/action_homeFragment_to_backgroundFragment"
            app:destination="@id/backgroundFragment" />
        <action
            android:id="@+id/action_homeFragment_to_customizeFragment"
            app:destination="@id/customizeFragment" />
        <action
            android:id="@+id/action_homeFragment_to_themeFragment"
            app:destination="@id/themeFragment" />
        <action
            android:id="@+id/action_homeFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
        <action
            android:id="@+id/action_homeFragment_to_generalSettingFragment"
            app:destination="@id/generalSettingFragment" />
        <action
            android:id="@+id/action_homeFragment_to_rowFragment"
            app:destination="@id/rowFragment" />
        <action
            android:id="@+id/action_homeFragment_to_iapFragment"
            app:destination="@id/iapFragment" />
    </fragment>

    <fragment
        android:id="@+id/zipperFragment"
        android:name="co.ziplock.framework.presentation.zipper.ZipperFragment"
        android:label="ZipperFragment"
        tools:layout="@layout/fragment_zipper">
        <action
            android:id="@+id/action_zipperFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
    </fragment>

    <fragment
        android:id="@+id/wallpaperFragment"
        android:name="co.ziplock.framework.presentation.wallpaper.WallpaperFragment"
        android:label="WallpaperFragment"
        tools:layout="@layout/fragment_wallpaper">
        <action
            android:id="@+id/action_wallpaperFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
        <action
            android:id="@+id/action_wallpaperFragment_to_pickPhotoFragment"
            app:destination="@id/pickPhotoFragment" />
    </fragment>

    <fragment
        android:id="@+id/backgroundFragment"
        android:name="co.ziplock.framework.presentation.background.BackgroundFragment"
        android:label="BackgroundFragment"
        tools:layout="@layout/fragment_background">
        <action
            android:id="@+id/action_backgroundFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
        <action
            android:id="@+id/action_backgroundFragment_to_pickPhotoFragment"
            app:destination="@id/pickPhotoFragment" />
    </fragment>

    <fragment
        android:id="@+id/soundFragment"
        android:name="co.ziplock.framework.presentation.sound.SoundFragment"
        android:label="SoundFragment"
        tools:layout="@layout/fragment_sound">
        <action
            android:id="@+id/action_soundFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
    </fragment>

    <fragment
        android:id="@+id/customizeFragment"
        android:name="co.ziplock.framework.presentation.customize.CustomizeFragment"
        android:label="CustomizeFragment"
        tools:layout="@layout/fragment_customize">
        <action
            android:id="@+id/action_customizeFragment_to_zipperFragment"
            app:destination="@id/zipperFragment" />
        <action
            android:id="@+id/action_customizeFragment_to_rowFragment"
            app:destination="@id/rowFragment" />
        <action
            android:id="@+id/action_customizeFragment_to_wallpaperFragment"
            app:destination="@id/wallpaperFragment" />
        <action
            android:id="@+id/action_customizeFragment_to_backgroundFragment"
            app:destination="@id/backgroundFragment" />
        <action
            android:id="@+id/action_customizeFragment_to_soundFragment"
            app:destination="@id/soundFragment" />
        <action
            android:id="@+id/action_customizeFragment_to_pickPhotoFragment"
            app:destination="@id/pickPhotoFragment" />
        <action
            android:id="@+id/action_customizeFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
    </fragment>

    <fragment
        android:id="@+id/pickPhotoFragment"
        android:name="co.ziplock.framework.presentation.pickphoto.PickPhotoFragment"
        android:label="PickPhotoFragment"
        tools:layout="@layout/fragment_pick_photo">
        <action
            android:id="@+id/action_pickPhotoFragment_to_editImageFragment"
            app:destination="@id/editImageFragment" />
    </fragment>

    <fragment
        android:id="@+id/editImageFragment"
        android:name="co.ziplock.framework.presentation.edit_image.EditImageFragment"
        android:label="EditImageFragment"
        tools:layout="@layout/fragment_edit_image">
        <action
            android:id="@+id/action_editImageFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
    </fragment>

    <fragment
        android:id="@+id/editLayoutFragment"
        android:name="co.ziplock.framework.presentation.edit_layout.EditLayoutFragment"
        android:label="EditLayoutFragment"
        tools:layout="@layout/fragment_edit_layout">
        <action
            android:id="@+id/action_editLayoutFragment_to_lockscreenInfoFragment"
            app:destination="@id/lockscreenInfoFragment" />
        <action
            android:id="@+id/action_editLayoutFragment_to_wallpaperFragment"
            app:destination="@id/wallpaperFragment" />
        <action
            android:id="@+id/action_editLayoutFragment_to_backgroundFragment"
            app:destination="@id/backgroundFragment" />
        <action
            android:id="@+id/action_editLayoutFragment_to_soundFragment"
            app:destination="@id/soundFragment" />
        <argument
            android:name="zipperData"
            app:argType="co.ziplock.framework.network.model.ZipperResponse"
            app:nullable="true" />
        <action
            android:id="@+id/action_editLayoutFragment_to_rowFragment"
            app:destination="@id/rowFragment" />
        <action
            android:id="@+id/action_editLayoutFragment_to_zipperFragment"
            app:destination="@id/zipperFragment" />
    </fragment>

    <fragment
        android:id="@+id/lockscreenInfoFragment"
        android:name="co.ziplock.framework.presentation.lockscreen_info.LockscreenInfoFragment"
        android:label="LockscreenInfoFragment"
        tools:layout="@layout/fragment_lockscreen_info">
        <action
            android:id="@+id/action_lockscreenInfoFragment_to_zipperLockSaveSuccessFragment"
            app:destination="@id/zipperLockSaveSuccessFragment" />
    </fragment>

    <fragment
        android:id="@+id/zipperLockSaveSuccessFragment"
        android:name="co.ziplock.framework.presentation.save_success.ZipperLockSaveSuccessFragment"
        android:label="ZipperLockSaveSuccessFragment"
        tools:layout="@layout/fragment_zipper_lock_save_success">
        <action
            android:id="@+id/action_zipperLockSaveSuccessFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
    </fragment>

    <fragment
        android:id="@+id/themeFragment"
        android:name="co.ziplock.framework.presentation.theme.ThemeFragment"
        android:label="ThemeFragment"
        tools:layout="@layout/fragment_theme">
        <action
            android:id="@+id/action_themeFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
    </fragment>

    <fragment
        android:id="@+id/pinSetupFragment"
        android:name="co.ziplock.framework.presentation.security.pin.PinSetupFragment"
        android:label="PinSetupFragment"
        tools:layout="@layout/fragment_pin_setup">
        <action
            android:id="@+id/action_pinSetup_to_securityQuestionSetup"
            app:destination="@id/securityQuestionSetupFragment" />
    </fragment>

    <fragment
        android:id="@+id/patternSetupFragment"
        android:name="co.ziplock.framework.presentation.security.pattern.PatternSetupFragment"
        android:label="PatternSetupFragment"
        tools:layout="@layout/fragment_pattern_setup">
        <action
            android:id="@+id/action_patternSetupFragment_to_securityQuestionSetupFragment"
            app:destination="@id/securityQuestionSetupFragment" />
    </fragment>

    <fragment
        android:id="@+id/securityQuestionSetupFragment"
        android:name="co.ziplock.framework.presentation.security.setup_security_question.SecurityQuestionSetupFragment"
        android:label="SecurityQuestionSetupFragment"
        tools:layout="@layout/fragment_security_question_setup">
        <action
            android:id="@+id/action_securityQuestionSetupFragment_to_setupRecoveryEmailFirstOpenFragment"
            app:destination="@id/setupRecoveryEmailFirstOpenFragment" />
    </fragment>

    <!-- Security Recovery Flow -->
    <fragment
        android:id="@+id/setupRecoveryEmailFirstOpenFragment"
        android:name="co.ziplock.framework.presentation.security.setup_recovery_email_first_open.SetupRecoveryEmailFirstOpenFragment"
        android:label="SetupRecoveryEmailFirstOpenFragment"
        tools:layout="@layout/fragment_security_question_recovery_email_first_open">
        <action
            android:id="@+id/action_setupRecoveryEmailFirstOpenFragment_to_otpVerifyWhenSetupOrChangeEmailFragment"
            app:destination="@id/otpVerifyWhenSetupOrChangeEmailFragment" />
    </fragment>

    <fragment
        android:id="@+id/setupRecoveryEmailFromRecoveryEmailFragment"
        android:name="co.ziplock.framework.presentation.security.setup_recovery_email_from_recovery_email.SetupRecoveryEmailFromRecoveryEmailFragment"
        android:label="SetupRecoveryEmailFromRecoveryEmailFragment"
        tools:layout="@layout/fragment_security_question_from_recovery_email">
        <action
            android:id="@+id/action_setupRecoveryEmailFromRecoveryEmailFragment_to_otpVerifyWhenSetupOrChangeEmailFragment"
            app:destination="@id/otpVerifyWhenSetupOrChangeEmailFragment" />
    </fragment>

    <!-- Password Recovery Flow -->
    <fragment
        android:id="@+id/passwordRecoveryMethodFragment"
        android:name="co.ziplock.framework.presentation.password_recovery.PasswordRecoveryMethodFragment"
        android:label="PasswordRecoveryMethodFragment"
        tools:layout="@layout/fragment_password_recovery_method">
        <action
            android:id="@+id/action_passwordRecoveryMethodFragment_to_changeSecurityQuestionFragment"
            app:destination="@id/changeSecurityQuestionFragment" />
        <action
            android:id="@+id/action_passwordRecoveryMethodFragment_to_pinSetupFragment"
            app:destination="@id/pinSetupFragment" />
        <action
            android:id="@+id/action_passwordRecoveryMethodFragment_to_patternSetupFragment"
            app:destination="@id/patternSetupFragment" />
        <action
            android:id="@+id/action_passwordRecoveryMethodFragment_to_setupRecoveryEmailFromRecoveryEmailFragment"
            app:destination="@id/setupRecoveryEmailFromRecoveryEmailFragment" />
        <action
            android:id="@+id/action_passwordRecoveryMethodFragment_to_changeRecoveryEmailFragment"
            app:destination="@id/changeRecoveryEmailFragment" />
        <action
            android:id="@+id/action_passwordRecoveryMethodFragment_to_securityQuestionForgotPasswordFragment"
            app:destination="@id/securityQuestionForgotPasswordFragment" />
        <action
            android:id="@+id/action_passwordRecoveryMethodFragment_to_emailPasswordRecoveryWhenForgotPasswordFragment"
            app:destination="@id/emailPasswordRecoveryWhenForgotPasswordFragment" />

    </fragment>

    <fragment
        android:id="@+id/changeRecoveryEmailFragment"
        android:name="co.ziplock.framework.presentation.security.change_recovery_email.ChangeRecoveryEmailFragment"
        android:label="ChangeRecoveryEmailFragment"
        tools:layout="@layout/fragment_change_recovery_email">
        <action
            android:id="@+id/action_changeRecoveryEmailFragment_to_otpVerifyWhenSetupOrChangeEmailFragment"
            app:destination="@id/otpVerifyWhenSetupOrChangeEmailFragment" />
    </fragment>

    <fragment
        android:id="@+id/securityQuestionForgotPasswordFragment"
        android:name="co.ziplock.framework.presentation.security.security_question_forgot_password.SecurityQuestionForgotPasswordFragment"
        android:label="SecurityQuestionForgotPasswordFragment"
        tools:layout="@layout/fragment_security_question_forgot_password">

    </fragment>

    <fragment
        android:id="@+id/emailPasswordRecoveryWhenForgotPasswordFragment"
        android:name="co.ziplock.framework.presentation.security.email_password_recovery_when_forgot_password.EmailPasswordRecoveryWhenForgotPasswordFragment"
        android:label="EmailPasswordRecoveryWhenForgotPasswordFragment"
        tools:layout="@layout/fragment_email_password_recovery">

        <action
            android:id="@+id/action_emailPasswordRecoveryWhenForgotPasswordFragment_to_emailOTPVerifyWhenForgotPasswordFragment"
            app:destination="@id/emailOTPVerifyWhenForgotPasswordFragment" />
    </fragment>

    <fragment
        android:id="@+id/emailOTPVerifyWhenForgotPasswordFragment"
        android:name="co.ziplock.framework.presentation.security.email_otp_verify_when_forgot_password_fragment.EmailOTPVerifyWhenForgotPasswordFragment"
        android:label="EmailOTPVerifyWhenForgotPasswordFragment"
        tools:layout="@layout/fragment_email_otp_verify_when_forgot_password" />

    <fragment
        android:id="@+id/otpVerifyWhenSetupOrChangeEmailFragment"
        android:name="co.ziplock.framework.presentation.security.otp_verify_when_setup_or_change_email.OTPVerifyWhenSetupOrChangeEmailFragment"
        android:label="OTPVerifyWhenSetupOrChangeEmailFragment"
        tools:layout="@layout/fragment_email_otp_verify_when_setup_or_change_email" />

    <!-- Change Security Question -->
    <fragment
        android:id="@+id/changeSecurityQuestionFragment"
        android:name="co.ziplock.framework.presentation.security.change_security_question.ChangeSecurityQuestionFragment"
        android:label="ChangeSecurityQuestionFragment"
        tools:layout="@layout/fragment_change_security_question">
        <action
            android:id="@+id/action_changeSecurityQuestionFragment_to_securityQuestionSetupFragment"
            app:destination="@id/securityQuestionSetupFragment" />
        <action
            android:id="@+id/action_changeSecurityQuestionFragment_to_securityQuestionForgotPasswordFragment"
            app:destination="@id/securityQuestionForgotPasswordFragment" />
        <action
            android:id="@+id/action_changeSecurityQuestionFragment_to_emailPasswordRecoveryWhenForgotPasswordFragment"
            app:destination="@id/emailPasswordRecoveryWhenForgotPasswordFragment" />
    </fragment>

    <fragment
        android:id="@+id/generalSettingFragment"
        android:name="co.ziplock.framework.presentation.generalsetting.GeneralSettingFragment"
        android:label="GeneralSettingFragment"
        tools:layout="@layout/fragment_general_setting_screen">
        <action
            android:id="@+id/action_generalSettingFragment_to_languageFragment"
            app:destination="@id/languageFragment" />
    </fragment>

    <fragment
        android:id="@+id/rowFragment"
        android:name="co.ziplock.framework.presentation.row.RowFragment"
        android:label="RowFragment"
        tools:layout="@layout/fragment_row">
        <action
            android:id="@+id/action_rowFragment_to_editLayoutFragment"
            app:destination="@id/editLayoutFragment" />
    </fragment>

    <fragment
        android:id="@+id/languageFragment"
        android:name="co.ziplock.framework.presentation.language.LanguageFragment"
        android:label="LanguageFragment"
        tools:layout="@layout/fragment_language">
        <action
            android:id="@+id/action_languageFragment_to_onboardingFragment"
            app:destination="@id/onboardingFragment"
            app:popUpTo="@id/splashFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_languageFragment_to_splashFragment"
            app:popUpTo="@id/homeFragment"
            app:popUpToInclusive="true"
            app:destination="@id/splashFragment" />
    </fragment>

    <fragment
        android:id="@+id/onboardingFragment"
        android:name="co.ziplock.framework.presentation.onboarding.OnboardingFragment"
        android:label="OnboardingFragment"
        tools:layout="@layout/fragment_onboarding">
        <action
            android:id="@+id/action_onboardingFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/splashFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <action
        android:id="@+id/action_to_iapFragment"
        app:destination="@id/iapFragment" />
    <fragment
        android:id="@+id/iapFragment"
        android:name="co.ziplock.framework.presentation.iap.IapFragment"
        android:label="IapFragment"
        tools:layout="@layout/fragment_iap" />

</navigation>