<resources>
    <declare-styleable name="RoundedImageView">
        <attr name="cornerRadius" format="dimension" />
    </declare-styleable>
    <declare-styleable name="CustomDotsIndicator">
        <attr name="dotHeight" format="dimension"/>
        <attr name="dotWidth" format="dimension"/>
        <attr name="dotSpacing" format="dimension"/>
        <attr name="selectedDotWidthRatio" format="float"/>
        <attr name="dotCornerRadius" format="dimension"/>
        <attr name="selectedDotColor" format="color"/>
        <attr name="unselectedDotColor" format="color"/>
        <attr name="numbersOfDots" format="integer"/>
    </declare-styleable>
</resources>