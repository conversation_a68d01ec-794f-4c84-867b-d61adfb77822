<resources>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="admob_application_id" translatable="false">ca-app-pub-1533767333836969~3618068341</string>
    <string translatable="false" name="please_wait">Please wait!</string>
    
    <!-- App Title -->
    <string name="app_title" translatable="false">ZIP LOCK SCREEN</string>
    
    <!-- Tabs -->
    <string name="tab_home">Home</string>
    <string name="settings">Settings</string>
    
    <!-- Wallpaper -->
    <string name="wallpaper_neon_theme">Neon Theme</string>
    <string name="button_try_now">Try Now</string>
    <string name="add_photo">Add Photo</string>
    
    <!-- Edit Image -->
    <string name="edit_image">Edit Image</string>
    
    <!-- Dialog -->
    <string name="dialog_demo_title">This is demo dialog</string>
    <string name="button_close">Close</string>
    
    <!-- Permission Dialog -->
    <string name="permission_title">Permission Required</string>
    <string name="permission_message">ZIP LOCK SCREEN needs the \'Draw over other apps\' permission to display the lock screen. Please enable this permission in the settings.</string>
    <string name="button_cancel">Cancel</string>
    <string name="button_go_to_settings">Go to Settings</string>
    <string name="lock_screen_status">Lock screen status</string>
    <string name="turn_on_off_password">Turn ON/OFF Password</string>
    <string name="password_type">Password Type</string>
    <string name="change_password">Change Password</string>
    <string name="password_recovery">Password Recovery</string>
    <string name="zipper">Zipper</string>
    <string name="no_zippers_available">No zippers available</string>
    <string name="no_rows_available">No rows available</string>
    <string name="no_wallpapers_available">No wallpapers available</string>
    <string name="all">All</string>
    <string name="local_files">Local Files</string>

    <string name="row">Row</string>
    <string name="wallpaper">Wallpaper</string>
    <string name="no_backgrounds_available">No backgrounds available</string>
    <string name="customize">Customize</string>
    <string name="personalize_what_you_like">Personalize what you like</string>
    <string name="hot_trending">Hot Trending</string>
    <string name="more">More</string>
    <string name="hot_trending_themes">Hot Trending Themes</string>
    <string name="choose_from_gallery">Choose from Gallery</string>
    <string name="preset_wallpapers">Preset Wallpapers</string>
    <string name="preset_backgrounds">Preset Backgrounds</string>

    <string name="permission_required">Permission required</string>
    <string name="permission_required_message">The app need draw on top Permission. Please check it first!</string>
    <string name="cancel">Cancel</string>
    <string name="do_it">Do it</string>
    <string name="are_you_sure">Are you sure?</string>
    <string name="permission_confirmation_message">Are you sure you want to turn off the lock screen? Your phone will use its default lock screen instead.</string>
    <string name="no">No</string>
    <string name="yes">Yes</string>
    <string name="pattern">Pattern</string>
    <string name="pin_code">PIN Code</string>
    <string name="apply">Apply</string>
    <string name="preview">Preview</string>
    <string name="enable_lock_screen">Enable Lock screen</string>
    <string name="enable_lock_screen_description">Without this permission, the lock screen feature won\'t function. Let\'s enable it</string>
    <string name="enable">Enable</string>
    <string name="lockscreen_info">Lockscreen Info</string>
    <string name="edit_layout">Edit Layout</string>
    <string name="zipper_lock_set_successfully">Zipper Lock set successfully</string>
    <string name="try_top_3_zipper_lock_themes">Try Top 3 Zipper Lock Themes</string>
    <string name="set_your_new_pin_code">Set your new PIN code</string>
    <string name="next">Next</string>
    <string name="previous_theme">Previous Theme</string>
    <string name="next_theme">Next Theme</string>
    <string name="re_enter_your_pin_code">Re-enter your PIN code</string>
    <string name="set_your_new_pattern">Set your new Pattern</string>
    <string name="re_draw_your_pattern">Re-draw your Pattern</string>
    <string name="connect_at_least_4_dots">Connect at least 4 dots</string>
    <string name="pattern_do_not_match">Pattern do not match</string>
    <string name="too_many_failed_attempts">Too many failed attempts. Please wait %d seconds.</string>
    
    <!-- New Password Setup -->
    <string name="confirm_your_new_pin_code">Confirm your new PIN code</string>
    <string name="confirm_your_new_pattern">Confirm your new pattern</string>
    <string name="draw_pattern_again_to_confirm">Draw pattern again to confirm</string>
    <string name="pins_do_not_match_try_again">PINs do not match. Try again.</string>
    <string name="patterns_do_not_match_try_again">Patterns do not match. Try again.</string>
    <string name="pattern_recorded">Pattern recorded</string>
    <string name="confirm">Confirm</string>
    
    <!-- Security Questions -->
    <string name="security_questions">Security Questions</string>
    <string name="save">Save</string>
    <string name="the_answers_will_be_used_to_verify_your_identity_when_needed">The answers will be used to verify your identity when needed</string>
    <string name="select_question">Select question</string>
    <string name="your_answer">Your answer</string>
    <string name="enter_3_256_characters">Enter 3–256 characters</string>
    <string name="please_provide_your_answer">Please provide your answer</string>
    <string name="please_select_a_security_question">Please select a security question</string>
    <string name="security_question_setup_completed_successfully">Security question setup completed successfully!</string>
    <string name="failed_to_setup_security_question">Failed to setup security question: %s</string>
    
    <!-- PIN Setup -->
    <string name="your_pin_is_incorrect_try_again">Your PIN is incorrect. Try again</string>
    <string name="pin_setup_completed_successfully">PIN setup completed successfully!</string>
    <string name="failed_to_setup_pin">Failed to setup PIN: %s</string>
    
    <!-- Pattern Setup -->
    <string name="too_many_failed_attempts_please_wait_30_seconds">Too many failed attempts. Please wait 30 seconds.</string>
    <string name="pattern_setup_completed_successfully">Pattern setup completed successfully!</string>
    <string name="failed_to_setup_pattern">Failed to setup pattern: %s</string>
    
    <!-- PIN Verification Error Messages -->
    <string name="incorrect_pin_attempts_left">Incorrect PIN. You have %d attempts left.</string>
    <string name="too_many_incorrect_attempts">Too many incorrect attempts. Please try again in 30 seconds</string>
    
    <!-- Pattern Verification Error Messages -->
    <string name="incorrect_pattern_attempts_left">Incorrect pattern. You have %d attempts left.</string>
    <string name="locked_out_countdown">Too many incorrect attempts. Please try again in %d seconds</string>
    
    <!-- Security Questions List -->
    <string name="please_enter_the_email_address_you_would_like_to_use_for_account_recovery">Please enter the email address you would like to use for account recovery.</string>
    <string name="recover_password">Recover password</string>
    <string name="your_email">Your email:</string>
    <string name="enter_a_valid_email_address">Enter a valid email address</string>
    <string name="enter_your_email">Enter your email</string>
    <string name="skip">Skip</string>
    <string name="your_password_has_been_set">Your password has been set</string>
    <string name="do_you_want_to_exit">Do you want to exit?</string>
    <string name="desc_confirm_cancel_saving_security_question">Your password won\'t be saved if you don\'t complete this step. To protect your account, you should set up a security question now.</string>
    <string name="exit">Exit</string>
    <string name="continue_step">Continue Step</string>
    <string name="continue_setup">Continue Setup</string>

    <string-array name="security_questions" translatable="false">
        <item>@string/security_question_1</item>
        <item>@string/security_question_2</item>
        <item>@string/security_question_3</item>
        <item>@string/security_question_4</item>
        <item>@string/security_question_5</item>
        <item>@string/security_question_6</item>
        <item>@string/security_question_7</item>
        <item>@string/security_question_8</item>
        <item>@string/security_question_9</item>
        <item>@string/security_question_10</item>
    </string-array>

    <string-array name="security_question_keys" translatable="false">
        <item>security_question_1</item>
        <item>security_question_2</item>
        <item>security_question_3</item>
        <item>security_question_4</item>
        <item>security_question_5</item>
        <item>security_question_6</item>
        <item>security_question_7</item>
        <item>security_question_8</item>
        <item>security_question_9</item>
        <item>security_question_10</item>
    </string-array>

    <string name="security_question_1">What was the name of your first pet?</string>
    <string name="security_question_2">In what city or town did your parents meet?</string>
    <string name="security_question_3">What was the make and model of your first car?</string>
    <string name="security_question_4">What is the name of the street you grew up on?</string>
    <string name="security_question_5">What was the name of your elementary or primary school?</string>
    <string name="security_question_6">What is the first name of your oldest cousin?</string>
    <string name="security_question_7">What was the first concert you attended?</string>
    <string name="security_question_8">What is your favorite book?</string>
    <string name="security_question_9">What was your childhood nickname?</string>
    <string name="security_question_10">Where did you go on your first airplane trip?</string>
    <string name="security_question">Security Question:</string>
    <string name="label_security_question">Security Question</string>

    <!-- Tooltip Messages -->
    <string name="tooltip_lock_screen_message">To activate the lock screen feature, please enable this permission</string>
    <string name="tooltip_password_message">Enable PIN Lock or Pattern for extra security</string>
    <string name="disable_ziplock">Disable Ziplock?</string>
    <string name="disable">Disable</string>
    <string name="answer_must_be_between_3_and_256_characters">Your new answer must be between 3 \nand 256 characters.</string>
    <string name="choose_a_question_that_s_easy_for_you_to_remember_but_hard_for_others_to_guess">Choose a question that\'s easy for you to remember, but hard for others to guess</string>
    <string name="choose_a_security_question_that_only_you_know_the_answer_to_so_we_can_verify_your_identity">Choose a security question that only you know the answer to, so we can verify your identity.</string>
    
    <!-- Verification Dialog Strings -->
    <string name="verify_your_pattern">Verify Your Pattern</string>
    <string name="draw_your_current_pattern">Draw your current pattern</string>
    <string name="forgot_password">Forgot Password?</string>
    <string name="verify_your_pin">Verify Your PIN</string>
    <string name="enter_your_current_pin">Enter your current PIN</string>
    <string name="toggle_pin_visibility">Toggle PIN Visibility</string>
    <string name="set_your_current_password">Set your current password</string>
    
    <!-- Password Recovery Flow -->
    <string name="choose_a_recovery_method">Choose a recovery method</string>
    <string name="answer_security_question">Answer Security Question</string>
    <string name="recover_via_email">Recover via Email</string>
    <string name="continue_text">Continue</string>
    <string name="enter_your_answer">Enter your answer</string>
    <string name="incorrect_answer_please_try_again">Incorrect answer. Please try again.</string>
    <string name="submit">Submit</string>
    <string name="email_password_recovery">Email Password Recovery</string>
    <string name="invalid_email_address">Invalid email address</string>
    <string name="email_not_matching_saved">Email does not match your saved recovery email</string>
    <string name="no_account_found_with_this_email">No account found with this email.</string>
    <string name="check_your_email">Check Your Email</string>
    <string name="password_recovery_instruction_email_has_been_sent">Password recovery instruction email has been sent to your registered email address. Please check your inbox and enter the verification code below.</string>
    <string name="verification_code">Verification Code:</string>
    <string name="enter_verification_code">Enter verification code</string>
    <string name="invalid_verification_code">Invalid verification code</string>
    <string name="resend_email">Resend Email</string>
    <string name="resend_in_seconds">Resend in (%d)s</string>
    <string name="open_email_app">Open Email App</string>
    <string name="no_email_app_found">No email app found</string>
    <string name="verify_code">Verify Code</string>
    <string name="recovery_email_not_set_up">Recovery email not set up</string>
    <string name="user_has_not_set_up_recovery_email">You haven\'t set up a recovery email yet</string>
    <string name="recovery_email">Recovery email</string>
    <string name="label_forgot_password">Forgot password</string>
    <string name="your_question_answer_is_incorrect_try_again"><![CDATA[Your Question & Answer is incorrect.\n Try again]]></string>
    <string name="verification_code_sent">Verification code sent</string>
    <string name="error_sending_otp">Error sending OTP. Please try again.</string>
    <string name="otp_request_limit_exceeded">You have reached the limit of 5 OTP requests within 24 hours. Please try again later.</string>
    <string name="password_recovery_successful">Password recovery successful!</string>
    <string name="that_doesn_t_look_like_a_valid_email_address_please_check_the_format">That doesn\'t look like a valid email address. Please check the format.</string>
    <string name="this_email_address_is_not_registered_in_our_system_please_double_check_or_try_a_different_email">This email address is not registered in our system. Please double-check or try a different email.</string>
    <string name="security_code">Security code</string>
    <string name="des_limit_request_otp">You have reached the limit of 5 OTP requests within 24 hours. Please try again later.</string>
    <!-- Split into separate strings for better formatting control -->
    <string name="password_recovery_email_sent_prefix">A password recovery instruction email has been sent to the email address: </string>
    <string name="password_recovery_email_sent_suffix">Please check your inbox (and also the spam/junk folder) to continue.</string>
    
    <!-- Keep original for backward compatibility -->
    <string name="a_password_recovery_instruction_email_has_been_sent_to_the_email_address_user_s_entered_email_address_please_check_your_inbox_and_also_the_spam_junk_folder_to_continue">A password recovery instruction email has been sent to the email address: <b>%1$s</b>. \n <b>Please check your inbox (and also the spam/junk folder) to continue.</b></string>
    <string name="something_error">Something error</string>
    <string name="ok">OK</string>
    <string name="your_security_code_is_incorrect_try_again">Invalid OTP code. Please try again.</string>
    <string name="otp_sent_successfully">OTP sent successfully!</string>
    <string name="otp_expires_in">OTP expires in: %1$s</string>
    <string name="otp_expired">OTP code has expired. Please request a new one.</string>
    
    <!-- Change Security Question -->
    <string name="current_security_question">Current security question?</string>
    <string name="new_security_question">New security question?</string>
    <string name="label_new_answer">New answer:</string>
    <string name="minimum_3_characters_required">Minimum 3 characters required</string>
    <string name="changes_saved_successfully">Changes saved successfully!</string>
    <string name="failed_to_save_security_question">Failed to save security question: %s</string>
    <string name="incorrect_answer_try_again">Incorrect answer. Try again</string>
    <string name="please_answer_your_current_security_question">Please answer your current security question.</string>
    <string name="please_select_a_new_security_question">Please select a new security question</string>
    <string name="label_your_answer">Your answer:</string>
    <string name="recovery_email_has_been_updated">Recovery email has been updated!</string>
    
    <!-- Change Recovery Email -->
    <string name="change_recovery_email">Change Recovery Email</string>
    <string name="current_recovery_email">Current recovery email</string>
    <string name="change_recovery_email_description">Enter your new recovery email address below. We will send a verification link to this address.</string>
    <string name="new_recovery_email">New recovery email</string>
    <string name="confirm_new_recovery_email">Confirm new recovery email</string>
    <string name="update">Update</string>
    <string name="invalid_email_format">Invalid email format.</string>
    <string name="emails_do_not_match">Emails do not match</string>
    <string name="new_email_same_as_current">New email cannot be the same as the current one.</string>
    <string name="incorrect_password">Incorrect password. Please try again.</string>
    <string name="sound">Sound</string>
    <string name="background">Background</string>
    <string name="vibration">Vibration</string>
    <string name="on">ON</string>
    <string name="off">OFF</string>
    <string name="no_sounds_available">No sounds available</string>
    <string name="popular">Popular</string>
    
    <!-- New Authentication Layer Strings -->
    <string name="choose_recovery_method">Choose a recovery method</string>
    <string name="use_recovery_email">Use Recovery Email</string>
    <string name="recovery_email_available">Recovery email is available</string>
    <string name="please_enter_an_answer">Please enter an answer</string>
    <string name="back">Back</string>
    <string name="email_recovery">Email Recovery</string>
    <string name="otp_will_be_sent_to">OTP will be sent to:</string>
    <string name="send_otp">Send OTP</string>
    <string name="sending_otp">Sending OTP…</string>
    <string name="verify_otp">Verify OTP</string>
    <string name="enter_otp_sent_to_email">Enter the OTP sent to your email</string>
    <string name="enter_6_digit_otp">Enter 6-digit OTP</string>
    <string name="please_enter_valid_otp">Please enter a valid 6-digit OTP</string>
    <string name="invalid_otp">Invalid OTP. Please try again.</string>
    <string name="resend_otp">Resend OTP</string>
    <string name="otp_resent">OTP resent successfully!</string>
    <string name="setup_new_password">Setup New Password</string>
    <string name="setup_new_pin">Setup New PIN</string>
    <string name="setup_new_pattern">Setup New Pattern</string>
    <string name="create_new_password_message">Create a new password to secure your device</string>
    <string name="enter_new_pin">Enter your new PIN</string>
    <string name="draw_new_pattern">Draw your new pattern</string>
    <string name="save_password">Save Password</string>
    <string name="password_reset_successful">Password reset successful!</string>
    <string name="re_enter_your_pattern">Re-enter your Pattern</string>
    <string name="pattern_doesn_t_match_try_again">Pattern doesn\'t match. Try again</string>
    <string name="set_later">Set later</string>
    <string name="camera_unavailable">Camera is not available</string>
    <string name="no_flash_on_device">This device does not have a flashlight</string>
    <string name="flashlight_turned_on">Flashlight is turned on</string>
    <string name="flashlight_turned_off">Flashlight is turned off</string>
    <string name="camera_access_error">Camera access error: %1$s</string>
    <string name="flashlight_control_error">Flashlight control error: %1$s</string>
    <string name="overlay_is_already_showing_or_permission_not_granted">Overlay is already showing or permission not granted</string>
    <string name="unlocked">Unlocked!</string>
    <string name="error_showing_overlay">Error showing overlay: %1$s</string>
    <string name="zipper_lock_active">Zipper Lock Active</string>
    <string name="service_is_running">Service is running</string>
    <string name="service_notification_description">Zipper overlay service notification</string>
    <string name="overlay_is_showing">Overlay is showing</string>
    <string name="enter_pin_code_to_unlock">Enter PIN Code to unlock</string>
    <string name="draw_pattern_to_unlock">Draw pattern to unlock</string>
    <string name="set_new_password">Set new Password</string>
    <string name="clear_all">Clear All</string>
    <string name="loading_ads">Loading Ads...</string>
    <string name="all_photos">All photos</string>
    <string name="in_app">In app</string>
    <string name="allow_ziplock_to_access_photos_media_and_files_on_your_device">Allow ZipLock to access photos, media, and files on your device?</string>
    <string name="deny">Deny</string>
    <string name="allow">Allow</string>

    <!-- Permission -->
    <string name="permission_explanation">Core fundamental are based on these permissions</string>
    <string name="permission_settings_message">You need to allow necessary permissions in Settings manually</string>
    <string name="permission_denied">These permissions are denied: %1$s</string>
    <string name="crop">Crop</string>
    <string name="done">Done</string>
    <string name="from_gallery">From Gallery</string>
    <string name="themes">Themes</string>

    <!-- Setting -->
    <string name="setting">General setting</string>
    <string name="information_about">Information about</string>
    <string name="developer">Developer</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="store_name">Store Name:</string>
    <string name="website">Website:</string>
    <string name="current_version">Current version:</string>
    <string name="skip_password_setup">Skip Password Setup?</string>
    <string name="skip_password_setup_desc">If you skip now, Ziplock screen lock will be temporarily disabled, and your phone will use its default lock screen (if any). You can set up a new Ziplock password later from the app settings. Are you sure you want to continue?</string>
    <string name="yes_skip">Yes, skip</string>
    <string name="this_action_may_contain_ads">This action may contain ads</string>

    <!-- Language Screen -->
    <string name="language_choose_language">Choose Language</string>
    <!-- Language Names -->
    <string name="language_english" translatable="false">English</string>
    <string name="language_spanish" translatable="false">Español</string>
    <string name="language_arabic" translatable="false">العربية</string>
    <string name="language_portuguese" translatable="false">Português</string>
    <string name="language_french" translatable="false">Français</string>
    <string name="language_german" translatable="false">Deutsch</string>
    <string name="language_chinese" translatable="false">中文</string>
    <string name="language_korean" translatable="false">한국어</string>
    <string name="language_japanese" translatable="false">日本語</string>
    <string name="language_russian" translatable="false">Русский</string>
    <string name="language_vietnamese" translatable="false">Tiếng Việt</string>
    <string name="language_thai" translatable="false">ไทย</string>
    <string name="language_turkish" translatable="false">Türkçe</string>
    <string name="language_hindi" translatable="false">हिन्दी</string>
    <string name="language_uzbek" translatable="false">O\'zbek</string>
    <string name="language_italian" translatable="false">Italiano</string>
    <string name="language_polish" translatable="false">Polski</string>
    <string name="language_persian" translatable="false">فارسی</string>
    <string name="language_ukrainian" translatable="false">Українська</string>
    <string name="language_romanian" translatable="false">Română</string>
    <string name="language_dutch" translatable="false">Nederlands</string>
    <string name="language_hungarian" translatable="false">Magyar</string>
    <string name="language_bulgarian" translatable="false">Български</string>
    <string name="language_greek" translatable="false">Ελληνικά</string>

    <string name="title_onboarding_screen_1">Style in a Slide</string>
    <string name="title_onboarding_screen_2">Your Signature Sound</string>
    <string name="title_onboarding_screen_3">Safety First, Always</string>
    <string name="title_onboarding_screen_4">What’s your style?</string>
    <string name="description_onboarding_screen_1_new">Don\'t just unlock. Dazzle.\n Anime zippers, diamond pulls &amp; more. </string>
    <string name="description_onboarding_screen_2_new">Every slide needs a soundtrack.\n From epic beats to fun effects, find your sound.</string>
    <string name="description_onboarding_screen_3">Your lock screen is more than just security.\n Make it a personal statement. </string>
    <string name="description_onboarding_screen_4">Tell us what you like to get the best theme recommendations.</string>
    <string name="important_permission">Important Permission</string>
    <string name="grant_permission">Grant permission</string>
    <string name="important">Important</string>

    <string name="all_permissions_are_already_granted">All permissions are already granted.</string>
    <string name="start_now">Start Now</string>
    <string name="ziplock" translatable="false">ZipLock Screen</string>
    <string name="ziplock_title" translatable="false">ZipLock</string>

    <string name="customize_theme">Customize theme</string>
    <string name="create_theme_by_yourself">Create theme by yourself</string>
    <string name="date_time">Date &amp; Time</string>
    <string name="battery_widget">Battery Widget</string>
    <string name="no_sound">No Sound</string>

    <!-- Overlay Settings Notification -->
    <string name="overlay_settings_notification_title">Find ZipLock and turn it on</string>
    <string name="overlay_settings_notification_find_ziplock">Find ZipLock and </string>
    <string name="overlay_settings_notification_turn_it_on">turn it on</string>
    <string name="language">Language</string>
    <string name="swipe_to_continue">Swipe to continue</string>
    <string name="use_ads_version">Use ads version</string>
    <string name="subscribe">Subscribe</string>
    <string name="permanly">Permanly:</string>
    <string name="get_premium_once_enjoy_forever">Get Premium Once, Enjoy Forever!</string>
    <string name="enjoy_ad_free_access_and_unlock_all_styles_amp_features_for_your_zip_lock_screen"><![CDATA[Enjoy ad-free access and unlock all styles & features for your Zip Lock screen.]]></string>
    <string name="you_ll_get">You\'ll get:</string>
    <string name="no_ads">No ads</string>
    <string name="premium_contents">Premium contents</string>
    <string name="all_functions">All functions</string>
    <string name="quick_open_app">Quick open app</string>
    <string name="term_of_use">Term of use</string>
    <string name="restore">Restore</string>
    <string name="loading_data">Loading data</string>
    <string name="you_need_to_unlock_to_use_this_content">You need to unlock to use this content</string>
    <string name="only_in_this_session">(Only in this session)</string>
    <string name="watch_video_ad">Watch video Ad</string>
    <string name="or">Or</string>
    <string name="buy_vip_version">Buy VIP version</string>
    <string name="buy_premium">Buy Premium</string>
</resources>