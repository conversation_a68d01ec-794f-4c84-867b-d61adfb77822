<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.BaseClean" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>
    
    <!-- PIN Input Style -->
    <style name="PinInputStyle">
        <item name="android:background">@drawable/bg_pin_input</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/black_35496d</item>
        <item name="android:fontFamily">@font/font_500</item>
        <item name="android:imeOptions">actionNext</item>
    </style>
    
    <!-- PIN Keypad Button Style -->
    <style name="PinKeypadButton">
        <item name="android:layout_width">@dimen/_60dp</item>
        <item name="android:layout_height">@dimen/_60dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/_24sp</item>
        <item name="android:textColor">@color/black_3f4955</item>
        <item name="android:fontFamily">@font/font_500</item>
    </style>
</resources>