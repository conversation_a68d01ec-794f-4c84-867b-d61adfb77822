<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/permissionx_default_dialog_bg"
    android:orientation="vertical"
    >

    <TextView
        android:id="@+id/messageText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="20sp"
        android:textColor="@color/permissionx_text_color"
        android:gravity="center"
        android:layout_marginLeft="28dp"
        android:layout_marginRight="28dp"
        android:layout_marginTop="28dp"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="28dp"
        android:layout_marginRight="28dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="20dp"
        android:scrollbars="none"
        android:layout_weight="1">

        <LinearLayout
            android:id="@+id/permissionsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            />

    </ScrollView>

    <LinearLayout
        android:id="@+id/positiveLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >

        <View
            android:layout_width="match_parent"
            android:layout_height="0.8dp"
            android:background="@color/permissionx_split_line"
            />

        <Button
            android:id="@+id/positiveBtn"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:textSize="14sp"
            android:textColor="@color/permissionx_tint_color"
            android:textAllCaps="false"
            tools:text="允许"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/negativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >

        <View
            android:layout_width="match_parent"
            android:layout_height="0.8dp"
            android:background="@color/permissionx_split_line"
            />

        <Button
            android:id="@+id/negativeBtn"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:textSize="14sp"
            android:textColor="@color/permissionx_tint_color"
            android:textAllCaps="false"
            tools:text="拒绝"/>

    </LinearLayout>

</LinearLayout>