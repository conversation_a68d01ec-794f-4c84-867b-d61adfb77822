<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:orientation="horizontal"
    tools:ignore="UseCompoundDrawables">

    <ImageView
        android:id="@+id/permissionIcon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center_vertical"
        app:tint="@color/permissionx_tint_color"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/permissionText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:textSize="16sp"
        android:textColor="@color/permissionx_text_color" />

</LinearLayout>