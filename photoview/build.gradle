apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

android {
    namespace = "com.github.chrisbanes.photoview"
    compileSdkVersion libs.versions.compileSdkVersion.get().toInteger()

    defaultConfig {
        minSdkVersion libs.versions.minSdkVersion.get().toInteger()
        targetSdkVersion libs.versions.targetSdkVersion.get().toInteger()
        versionCode 1
        versionName "1.0"
    }
}

dependencies {
    implementation "androidx.appcompat:appcompat:1.1.0"
}

/*afterEvaluate {
    publishing {
        publications {
            release(MavenPublication) {
                from components.release

                group = 'com.github.chrisbanes'
                artifactId = 'PhotoView'
                version = '2.3.0'

                // Adds javadocs and sources as separate jars.
                artifact androidJavadocsJar
                artifact(sourceJar)

                pom {
                    name = 'PhotoView'
                    description = 'A simple ImageView that support zooming, both by Multi-touch gestures and double-tap.'
                    url = 'https://github.com/Baseflow/PhotoView'
                    licenses {
                        license {
                            name = 'The Apache License, Version 2.0'
                            url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                        }
                    }
                    developers {
                        developer {
                            id = 'chrisbanes'
                            name = 'Chris Banes'
                        }
                    }
                    scm {
                        connection = 'scm:**************/chrisbanes/PhotoView.git'
                        developerConnection = 'scm:**************/chrisbanes/PhotoView.git'
                        url = 'https://github.com/chrisbanes/PhotoView'
                    }
                }
            }
        }
    }
}*/

//task androidJavadocs(type: Javadoc) {
//    source = android.sourceSets.main.java.srcDirs
//    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
//    android.libraryVariants.all { variant ->
//        if (variant.name == 'release') {
//            owner.classpath += variant.javaCompileProvider.get().classpath
//        }
//    }
//    exclude '**/R.html', '**/R.*.html', '**/index.html'
//}

/*
task androidJavadocsJar(type: Jar, dependsOn: androidJavadocs) {
    archiveClassifier.set('javadoc')
    from androidJavadocs.destinationDir
}

task sourceJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier "sources"
}*/
